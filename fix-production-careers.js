#!/usr/bin/env node

/**
 * Production Careers Fix Script
 * Diagnoses and provides solutions for careers page production issues
 */

const https = require('https');
const fs = require('fs');

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    req.on('error', reject);
    req.end();
  });
}

async function diagnoseCareersIssue() {
  console.log('🔍 MetaNord Careers Production Diagnosis');
  console.log('=========================================\n');

  // Test 1: Check production API
  console.log('1️⃣ Testing Production API...');
  try {
    const response = await makeRequest('https://api.metanord.eu/api/careers');
    if (response.status === 200) {
      const data = JSON.parse(response.data);
      console.log(`✅ Production API working: ${data.length} job postings`);
      console.log(`   Jobs: ${data.map(job => job.title).join(', ')}`);
    } else {
      console.log(`❌ Production API failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Production API error: ${error.message}`);
  }

  // Test 2: Check CORS
  console.log('\n2️⃣ Testing CORS Configuration...');
  try {
    const response = await makeRequest('https://api.metanord.eu/api/careers', {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://metanord-frontend.vercel.app',
        'Access-Control-Request-Method': 'GET'
      }
    });
    
    const corsOrigin = response.headers['access-control-allow-origin'];
    if (corsOrigin === 'https://metanord-frontend.vercel.app' || corsOrigin === '*') {
      console.log('✅ CORS properly configured');
    } else {
      console.log(`❌ CORS issue: Origin header is '${corsOrigin}'`);
    }
  } catch (error) {
    console.log(`❌ CORS test failed: ${error.message}`);
  }

  // Test 3: Check frontend deployment
  console.log('\n3️⃣ Testing Frontend Deployment...');
  try {
    const response = await makeRequest('https://metanord-frontend.vercel.app/careers');
    if (response.status === 200) {
      if (response.data.includes('Something went wrong')) {
        console.log('❌ Frontend shows error page');
      } else {
        console.log('✅ Frontend loads successfully');
      }
    } else {
      console.log(`❌ Frontend failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Frontend test failed: ${error.message}`);
  }

  // Provide solutions
  console.log('\n🛠️  SOLUTION STEPS:');
  console.log('==================');
  
  console.log('\n📋 The issue is likely one of these:');
  console.log('1. Backend not deployed to production');
  console.log('2. CORS not configured for Vercel domain');
  console.log('3. Frontend API URL pointing to wrong backend');
  console.log('4. Environment variables not set correctly');

  console.log('\n🚀 QUICK FIX OPTIONS:');
  console.log('=====================');
  
  console.log('\nOption A: Deploy Your Local Backend');
  console.log('-----------------------------------');
  console.log('1. Run: node deploy-backend.js');
  console.log('2. Follow the Railway deployment instructions');
  console.log('3. Update VITE_API_URL in Vercel to your Railway URL');
  console.log('4. Redeploy frontend');

  console.log('\nOption B: Fix Existing Production Backend');
  console.log('----------------------------------------');
  console.log('1. Contact backend administrator');
  console.log('2. Add CORS origin: https://metanord-frontend.vercel.app');
  console.log('3. Ensure careers endpoints are implemented');
  console.log('4. Verify job postings data exists');

  console.log('\nOption C: Use Local Backend Temporarily');
  console.log('--------------------------------------');
  console.log('1. Expose local backend with ngrok:');
  console.log('   npm install -g ngrok');
  console.log('   ngrok http 3001');
  console.log('2. Update VITE_API_URL in Vercel to ngrok URL');
  console.log('3. Redeploy frontend');

  console.log('\n⚡ IMMEDIATE STEPS:');
  console.log('==================');
  console.log('1. Check if you have access to deploy backend');
  console.log('2. If yes: Run "node deploy-backend.js"');
  console.log('3. If no: Contact backend team to add CORS and careers endpoints');
  console.log('4. Update frontend environment variables');
  console.log('5. Redeploy frontend');

  console.log('\n📞 Need Help?');
  console.log('=============');
  console.log('The careers page works locally but fails in production because:');
  console.log('- Local backend (localhost:3001) has the careers data');
  console.log('- Production backend (api.metanord.eu) may not have the same data/endpoints');
  console.log('- CORS configuration differences between local and production');
  console.log('\nSolution: Deploy your local backend or sync data with production backend.');
}

diagnoseCareersIssue().catch(console.error);
