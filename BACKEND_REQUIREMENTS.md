# MetaNord Admin Dashboard - Backend Requirements

## 🎯 Overview
This document outlines the missing backend API endpoints and database requirements needed to complete the MetaNord admin dashboard functionality.

## 🚨 CRITICAL MISSING ENDPOINTS

### 1. **Site Content Management**
**Current Status**: Frontend implemented, backend endpoints missing

#### Required Endpoints:
```
GET    /api/admin/content                    - Fetch all site content
POST   /api/admin/content                    - Create new content item
PUT    /api/admin/content/:id                - Update existing content item
DELETE /api/admin/content/:id                - Delete content item
POST   /api/admin/content/version            - Create content version for history
GET    /api/admin/content/versions/:id       - Get content version history
```

#### Database Schema:
```sql
CREATE TABLE site_content (
  id SERIAL PRIMARY KEY,
  section VARCHAR(50) NOT NULL,           -- 'about', 'footer', 'contact', 'certificates'
  key VARCHAR(100) NOT NULL,              -- 'company_description', 'mission', 'values'
  value TEXT NOT NULL,                    -- The actual content
  language VARCHAR(5) NOT NULL DEFAULT 'en', -- 'en', 'et', 'ru', 'lv', 'lt', 'pl'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(section, key, language)
);

CREATE TABLE content_versions (
  id SERIAL PRIMARY KEY,
  content_type VARCHAR(50) NOT NULL,      -- 'page', 'section', etc.
  content_id INTEGER,                     -- Reference to main content
  data JSONB NOT NULL,                    -- Versioned content data
  version INTEGER NOT NULL,
  created_by INTEGER REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 2. **Enhanced User Management**
**Current Status**: Basic CRUD exists, missing invitation system and role management

#### Missing Endpoints:
```
POST   /api/admin/users/invite              - Send user invitation email
PATCH  /api/admin/users/:id                 - Update user (already exists but needs enhancement)
DELETE /api/admin/users/:id                 - Delete user (already exists)
GET    /api/admin/users/invitations         - List pending invitations
POST   /api/admin/users/invitations/:token/accept - Accept invitation
```

#### Enhanced Database Schema:
```sql
-- Enhance existing users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS full_name VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active'; -- 'active', 'inactive', 'pending'

CREATE TABLE user_invitations (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  full_name VARCHAR(255),
  role VARCHAR(20) NOT NULL,              -- 'admin', 'editor', 'viewer'
  token VARCHAR(255) NOT NULL UNIQUE,
  invited_by INTEGER REFERENCES users(id),
  expires_at TIMESTAMP NOT NULL,
  accepted_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 3. **Enhanced Notifications System**
**Current Status**: Basic endpoints exist, missing CRUD operations

#### Missing Endpoints:
```
PATCH  /api/admin/notifications/:id/read    - Mark notification as read (exists)
PATCH  /api/admin/notifications/:id/unread  - Mark notification as unread (exists)
DELETE /api/admin/notifications/:id         - Delete single notification (exists)
DELETE /api/admin/notifications/clear-all   - Clear all notifications (exists)
POST   /api/admin/notifications             - Create new notification
```

#### Database Schema:
```sql
CREATE TABLE notifications (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(20) NOT NULL,              -- 'info', 'warning', 'success', 'error'
  read BOOLEAN DEFAULT FALSE,
  resource_type VARCHAR(50),              -- 'inquiry', 'quote', 'user', etc.
  resource_id INTEGER,                    -- ID of related resource
  action_text VARCHAR(100),               -- Optional action button text
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 ENHANCEMENT REQUIREMENTS

### 4. **Real Data Integration**
**Current Status**: Using mock data, needs real database integration

#### Contact Inquiries:
- ✅ **Already working**: Contact form submissions are saved to `mockInquiries` array
- ❌ **Missing**: Persistent database storage
- ❌ **Missing**: Status update endpoints

#### Required Database Schema:
```sql
CREATE TABLE contact_inquiries (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  company VARCHAR(255),
  phone VARCHAR(50),
  subject VARCHAR(255),
  message TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'new',       -- 'new', 'read', 'replied', 'archived'
  archived BOOLEAN DEFAULT FALSE,
  inquiry_type VARCHAR(50) DEFAULT 'general', -- 'general', 'product', 'support'
  product_interest VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 5. **Missing API Endpoints for Existing Features**

#### Content Management:
```
GET    /api/content                        - Public content endpoint (may exist)
GET    /api/content/hero                   - Hero section content
GET    /api/content/buttons                - Button text content
```

#### User Session Management:
```
GET    /api/admin/me                       - Get current user info (exists)
POST   /api/admin/refresh                  - Refresh authentication token
```

## 🔐 AUTHENTICATION & AUTHORIZATION

### Current Implementation:
- Basic session-based authentication exists
- `requireAuth` middleware implemented

### Required Enhancements:
1. **Role-based permissions**:
   - Admin: Full access to all endpoints
   - Editor: Content management only
   - Viewer: Read-only access

2. **Permission middleware**:
```javascript
const requireRole = (roles) => (req, res, next) => {
  if (!req.user || !roles.includes(req.user.role)) {
    return res.status(403).json({ error: 'Insufficient permissions' });
  }
  next();
};
```

## 📧 EMAIL SYSTEM

### Required for User Invitations:
```javascript
// Email service configuration
const emailService = {
  sendInvitation: async (email, token, inviterName) => {
    // Send invitation email with signup link
  },
  sendPasswordReset: async (email, token) => {
    // Send password reset email
  }
};
```

## 🗄️ DATABASE MIGRATION SCRIPTS

### Priority 1 - Site Content:
```sql
-- Create site_content table
-- Insert default content for each section and language
-- Create indexes for performance
```

### Priority 2 - User Enhancements:
```sql
-- Add new columns to users table
-- Create user_invitations table
-- Update existing user records
```

### Priority 3 - Notifications:
```sql
-- Create notifications table
-- Insert sample notifications
-- Create indexes
```

## 🚀 IMPLEMENTATION PRIORITY

### Phase 1 (Critical - Immediate):
1. Site Content Management endpoints
2. Enhanced User Management (CRUD operations)
3. Real database integration for contact inquiries

### Phase 2 (Important - Next Sprint):
1. User invitation system
2. Enhanced notifications CRUD
3. Role-based permissions

### Phase 3 (Nice to Have):
1. Content versioning system
2. Advanced email templates
3. Audit logging

## 📋 TESTING REQUIREMENTS

### API Testing:
- Unit tests for all new endpoints
- Integration tests for user flows
- Authentication/authorization tests

### Database Testing:
- Migration scripts validation
- Data integrity constraints
- Performance testing for content queries

## 🔍 MONITORING & LOGGING

### Required Logging:
- User actions (create, update, delete)
- Content changes with timestamps
- Failed authentication attempts
- API response times

### Metrics to Track:
- Active user sessions
- Content update frequency
- System performance
- Error rates by endpoint
