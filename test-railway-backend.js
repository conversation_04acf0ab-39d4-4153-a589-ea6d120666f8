#!/usr/bin/env node

/**
 * Railway Backend Test Script
 * Tests the current Railway backend for careers functionality
 */

const https = require('https');

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    req.on('error', reject);
    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

async function testRailwayBackend() {
  console.log('🚂 Testing MetaNord Railway Backend');
  console.log('===================================\n');

  const BACKEND_URL = 'https://api.metanord.eu'; // Update this if different

  // Test 1: Health check
  console.log('1️⃣ Testing Backend Health...');
  try {
    const response = await makeRequest(`${BACKEND_URL}/health`);
    if (response.status === 200) {
      console.log('✅ Backend is online');
    } else {
      console.log(`⚠️  Backend returned: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Backend health check failed: ${error.message}`);
  }

  // Test 2: Public careers endpoint
  console.log('\n2️⃣ Testing Public Careers API...');
  try {
    const response = await makeRequest(`${BACKEND_URL}/api/careers`);
    if (response.status === 200) {
      const data = JSON.parse(response.data);
      console.log(`✅ Careers API working: ${data.length} job postings`);
      if (data.length > 0) {
        console.log(`   Sample job: ${data[0].title}`);
        console.log(`   Has required fields: ${data[0].responsibilities ? 'Yes' : 'No'}`);
      } else {
        console.log('⚠️  No job postings found');
      }
    } else {
      console.log(`❌ Careers API failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Careers API error: ${error.message}`);
  }

  // Test 3: Admin login
  console.log('\n3️⃣ Testing Admin Login...');
  try {
    const response = await makeRequest(`${BACKEND_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'https://metanord-frontend.vercel.app'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    if (response.status === 200) {
      const data = JSON.parse(response.data);
      if (data.success) {
        console.log('✅ Admin login working');
        console.log(`   User: ${data.user.name} (${data.user.role})`);
      } else {
        console.log('❌ Admin login failed: Invalid response');
      }
    } else {
      console.log(`❌ Admin login failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Admin login error: ${error.message}`);
  }

  // Test 4: Admin careers endpoint (without auth - should return 401)
  console.log('\n4️⃣ Testing Admin Careers Endpoint...');
  try {
    const response = await makeRequest(`${BACKEND_URL}/api/admin/careers`, {
      headers: {
        'Origin': 'https://metanord-frontend.vercel.app'
      }
    });

    if (response.status === 401) {
      console.log('✅ Admin careers endpoint exists (returns 401 as expected)');
    } else if (response.status === 404) {
      console.log('❌ Admin careers endpoint not found (404)');
    } else {
      console.log(`⚠️  Admin careers endpoint returned: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Admin careers test error: ${error.message}`);
  }

  // Test 5: CORS check
  console.log('\n5️⃣ Testing CORS Configuration...');
  try {
    const response = await makeRequest(`${BACKEND_URL}/api/careers`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://metanord-frontend.vercel.app',
        'Access-Control-Request-Method': 'GET'
      }
    });

    const corsOrigin = response.headers['access-control-allow-origin'];
    const corsCredentials = response.headers['access-control-allow-credentials'];

    if (corsOrigin === 'https://metanord-frontend.vercel.app' || corsOrigin === '*') {
      console.log('✅ CORS origin configured correctly');
    } else {
      console.log(`❌ CORS origin issue: ${corsOrigin}`);
    }

    if (corsCredentials === 'true') {
      console.log('✅ CORS credentials enabled');
    } else {
      console.log('❌ CORS credentials not enabled');
    }
  } catch (error) {
    console.log(`❌ CORS test error: ${error.message}`);
  }

  // Summary
  console.log('\n📋 DIAGNOSIS SUMMARY:');
  console.log('=====================');
  console.log('Based on the tests above, check if:');
  console.log('1. ✅ Backend is online and accessible');
  console.log('2. ✅ Careers API returns job postings with complete data');
  console.log('3. ✅ Admin login works with correct credentials');
  console.log('4. ✅ Admin careers endpoint exists');
  console.log('5. ✅ CORS is configured for Vercel domain');
  console.log('');
  console.log('If any tests fail, use the backend-check-prompt.md');
  console.log('to get help from Augment AI on the backend repo.');
}

testRailwayBackend().catch(console.error);
