# MetaNord OÜ - Corporate Website

A modern, responsive website for MetaNord OÜ, a company specializing in aluminum profiles and infrastructure products.

## Features

- **Responsive Design**: Fully mobile and tablet responsive layout
- **Modern UI**: Clean, Nordic-inspired design with professional aesthetics
- **Product Catalog**: Detailed product category displays with filters
- **Contact Form**: Interactive contact form with validation
- **Multilingual Support**: Language switcher for multiple languages
- **Optimized Images**: High-quality product imagery
- **SEO Friendly**: Proper meta tags and semantic HTML structure

## Technologies Used

- HTML5
- CSS3 with custom variables
- Vanilla JavaScript
- Express.js for the server
- SVG icons for crisp, scalable interface elements

## Project Structure

project/
├── public/ # Static website files
│   ├── assets/ # Assets directory
│   │   ├── css/ # CSS stylesheets
│   │   ├── js/ # JavaScript files
│   │   └── images/ # Image assets
│   ├── index.html # Homepage
│   ├── products.html # Products catalog
│   └── contact.html # Contact page
├── server.js # Express server for deployment
└── README.md # Project documentation

## Backend API Service

This repository only contains the client-side application. All endpoints under `/api/*` (for example `/api/contact` and `/api/quote`) are served by a separate backend. The backend is a Node.js service that needs to be deployed independently. Configuration typically requires environment variables for the database connection, authentication secrets and mail delivery via SendGrid.

When running the backend you should:

- Enable [CORS](https://developer.mozilla.org/docs/Web/HTTP/CORS) for the origin where this frontend will be hosted.
- Provide your SendGrid credentials (e.g. `SENDGRID_API_KEY` and sender addresses) so contact form emails can be sent.
- Configure any additional variables your deployment requires (for example `DATABASE_URL` or `JWT_SECRET`).

Refer to the backend project documentation for the full list of environment variables and setup instructions.

### Environment Variables

The project relies on a few environment variables which should be provided at build time:

- `VITE_API_URL` – base URL for the backend API. Defaults to `https://api.metanord.eu` if not set.
- `OPENAI_API_KEY` – API key used by `translation-script.js` to generate translations. This value must be supplied through the process environment and should not be committed to the repository.

### Configuring `VITE_API_URL`

The frontend communicates with the backend using the `VITE_API_URL` environment variable. You can define different values for various environments by creating `.env` files (e.g. `.env.local`, `.env.production`). For example:

```bash
VITE_API_URL=http://localhost:3000
```

During development, point `VITE_API_URL` to your local backend. In production it should be set to the publicly accessible URL of your API service.

## Design Principles

- Clean Nordic Aesthetics: Minimalist design with ample whitespace
- Industrial Focus: Design elements reflect the company's industry focus
- Accessibility: Proper contrast and semantic HTML for accessibility
- Performance: Optimized assets and minimal dependencies for fast loading
- Usability: Intuitive navigation and clear information architecture

## Company Information

- **Name**: MetaNord OÜ
- **Business**: Trading and distribution of aluminum profiles and infrastructure products
- **Location**: Tallinn, Estonia
- **Registry Code**: 17235227
- **Email**: <EMAIL>

All rights reserved. This site and its content are proprietary to MetaNord OÜ.
