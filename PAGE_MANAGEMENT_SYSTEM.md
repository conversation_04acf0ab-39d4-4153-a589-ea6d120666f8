# MetaNord Page Management System

## Overview

A comprehensive page management system for the MetaNord admin dashboard that provides advanced content editing, SEO optimization, and page building capabilities.

## Features Implemented

### 1. Enhanced Page Editor Modal (`PageEditorModal.tsx`)
- **Tabbed Interface**: Content, SEO, Settings, Preview tabs
- **Rich Text Editor Integration**: WYSIWYG content editing
- **Drag-and-Drop Page Builder**: Visual page construction
- **Template Selection**: Choose from pre-designed templates
- **Form Validation**: Using react-hook-form and zod
- **Multi-language Support**: Content management for 6 languages
- **Real-time Preview**: Live preview with device toggles

### 2. Rich Text Editor (`RichTextEditor.tsx`)
- **WYSIWYG Editing**: Full rich text editing capabilities
- **Formatting Tools**: Bold, italic, underline, strikethrough
- **Text Alignment**: Left, center, right, justify
- **Lists**: Bullet and numbered lists
- **Media Insertion**: Links, images, quotes, code blocks
- **Keyboard Shortcuts**: Standard shortcuts (Ctrl+B, Ctrl+I, etc.)
- **Preview Mode**: Toggle between edit and preview
- **Custom Styling**: Tailwind CSS integration

### 3. Drag-and-Drop Page Builder (`DragDropPageBuilder.tsx`)
- **Component Library**: Pre-built components (text, images, buttons, etc.)
- **Drag-and-Drop Interface**: Using react-beautiful-dnd
- **Section Management**: Add, edit, delete page sections
- **Responsive Preview**: Desktop, tablet, mobile views
- **Undo/Redo**: History management for changes
- **Properties Panel**: Component and section customization
- **Visual Editor**: Real-time preview of changes

### 4. Enhanced Pages Component (`Pages.tsx`)
- **Advanced Search**: Search pages by title, content, slug
- **Status Filtering**: Filter by published, draft, archived
- **Sorting Options**: Sort by title, date, status
- **Bulk Actions**: Publish, unpublish, archive, delete multiple pages
- **Grid/List Views**: Toggle between view modes
- **Page Statistics**: View counts, status indicators
- **Quick Actions**: Edit, duplicate, delete, view page

### 5. Page Management Dashboard (`PageManagementDashboard.tsx`)
- **Analytics Overview**: Page views, visitors, time on page
- **Status Distribution**: Visual charts of page statuses
- **Language Statistics**: Pages by language breakdown
- **Top Performing Pages**: Analytics for best pages
- **Recent Activity**: Timeline of page changes
- **Interactive Charts**: Using recharts library

## Technical Implementation

### Form Validation Schema
```typescript
const pageFormSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Invalid slug format"),
  description: z.string().max(500, "Description too long").optional(),
  metaTitle: z.string().max(60, "Meta title too long").optional(),
  metaDescription: z.string().max(160, "Meta description too long").optional(),
  canonicalUrl: z.string().url("Invalid URL").optional().or(z.literal("")),
  ogImage: z.string().url("Invalid image URL").optional().or(z.literal("")),
  status: z.enum(['draft', 'published', 'archived']),
  language: z.string().min(2, "Language is required"),
  templateId: z.number().optional(),
});
```

### API Endpoints Required

#### Page Management
- `GET /api/admin/pages` - List pages with filtering/sorting
- `POST /api/admin/pages` - Create new page
- `GET /api/admin/pages/:id` - Get specific page
- `PUT /api/admin/pages/:id` - Update page
- `DELETE /api/admin/pages/:id` - Delete page
- `POST /api/admin/pages/:id/duplicate` - Duplicate page
- `POST /api/admin/pages/bulk-action` - Bulk operations

#### Templates
- `GET /api/admin/page-templates` - Get available templates

#### Analytics
- `GET /api/admin/pages/stats` - Page statistics
- `GET /api/admin/pages/analytics` - Page analytics
- `GET /api/admin/pages/language-stats` - Language distribution

### Database Schema

#### Pages Table
```sql
CREATE TABLE pages (
  id SERIAL PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  slug VARCHAR(200) UNIQUE NOT NULL,
  description TEXT,
  content JSONB,
  meta_title VARCHAR(60),
  meta_description VARCHAR(160),
  og_image VARCHAR(500),
  canonical_url VARCHAR(500),
  status VARCHAR(20) DEFAULT 'draft',
  language VARCHAR(5) NOT NULL,
  template_id INTEGER REFERENCES page_templates(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by INTEGER REFERENCES users(id),
  published_at TIMESTAMP
);
```

#### Page Templates Table
```sql
CREATE TABLE page_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  preview_image VARCHAR(500),
  content_structure JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### Page Revisions Table
```sql
CREATE TABLE page_revisions (
  id SERIAL PRIMARY KEY,
  page_id INTEGER REFERENCES pages(id) ON DELETE CASCADE,
  content JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  created_by INTEGER REFERENCES users(id)
);
```

## Usage Instructions

### Creating a New Page
1. Click "Create Page" button in the Pages section
2. Fill in basic page information (title, slug, description)
3. Choose a template (optional)
4. Use either the Page Builder or Rich Text Editor for content
5. Configure SEO settings (meta title, description, OG tags)
6. Set page status and language
7. Save the page

### Editing Existing Pages
1. Find the page in the Pages list
2. Click the "Edit" button or page card
3. Modify content using the tabbed interface
4. Use the Preview tab to see changes
5. Save changes

### Using the Page Builder
1. Select "Page Builder" mode in the Content tab
2. Drag components from the sidebar to the canvas
3. Click components to edit their properties
4. Use the properties panel to customize styling
5. Preview on different device sizes
6. Save the page structure

### Bulk Operations
1. Select multiple pages using checkboxes
2. Choose bulk action (publish, unpublish, archive, delete)
3. Confirm the action
4. Pages will be updated accordingly

## Styling and Design

### Brand Colors
- Primary: `#2D7EB6` (MetaNord Blue)
- Secondary: `#40BFB9` (MetaNord Teal)
- Gradients: `from-[#2D7EB6] to-[#40BFB9]`

### Component Styling
- Consistent with existing MetaNord design system
- Tailwind CSS for styling
- Responsive design for all screen sizes
- Accessibility features (ARIA labels, keyboard navigation)

## Dependencies

### Required Packages
- `react-hook-form` - Form handling
- `@hookform/resolvers/zod` - Form validation
- `zod` - Schema validation
- `react-beautiful-dnd` - Drag and drop functionality
- `recharts` - Charts and analytics
- `@tanstack/react-query` - Data fetching
- `lucide-react` - Icons

### UI Components
- All UI components from the existing MetaNord component library
- Custom components built on top of Radix UI primitives

## Future Enhancements

### Planned Features
1. **Advanced SEO Tools**
   - SEO score analysis
   - Keyword optimization suggestions
   - Meta tag previews

2. **Content Scheduling**
   - Schedule page publishing
   - Content expiration dates
   - Automated status changes

3. **Version Control**
   - Page revision history
   - Compare versions
   - Rollback functionality

4. **Advanced Analytics**
   - Heat maps
   - User behavior tracking
   - A/B testing capabilities

5. **Collaboration Features**
   - Multi-user editing
   - Comments and reviews
   - Approval workflows

6. **Performance Optimization**
   - Image optimization
   - Lazy loading
   - CDN integration

## Testing

### Recommended Tests
1. **Unit Tests**
   - Form validation
   - Component rendering
   - Utility functions

2. **Integration Tests**
   - API interactions
   - Page creation workflow
   - Bulk operations

3. **E2E Tests**
   - Complete page management workflow
   - Cross-browser compatibility
   - Mobile responsiveness

## Deployment Notes

### Environment Variables
- Ensure proper API URL configuration
- Set up authentication middleware
- Configure file upload endpoints

### Performance Considerations
- Implement pagination for large page lists
- Use virtual scrolling for component library
- Optimize bundle size with code splitting

This comprehensive page management system provides a professional-grade solution for managing website content with advanced editing capabilities, SEO optimization, and analytics integration.
