# MetaNord Careers Page - Production Issue Fix Guide

## 🚨 Issue Analysis

The MetaNord Careers page works correctly on localhost:5173 but shows "something went wrong" in production. This is a common deployment issue with several potential causes.

## 🔍 Diagnostic Tools Added

### 1. Enhanced Error Boundary (`CareersErrorBoundary.tsx`)
- Catches and displays detailed error information
- Shows environment details and API configuration
- Provides retry and reload options
- Copies error details for debugging

### 2. Production Diagnostics (`production-diagnostics.ts`)
- Tests API connectivity
- Checks CORS configuration
- Validates environment variables
- Detects mixed content issues
- Auto-runs in development, available in production

### 3. Enhanced API Error Logging
- Detailed error logging in production
- API configuration debugging
- Request/response details

## 🛠️ Common Production Issues & Fixes

### Issue 1: API URL Configuration
**Problem**: Frontend trying to connect to localhost in production
**Fix**: Ensure `VITE_API_URL` is set correctly

```bash
# In Vercel/Netlify environment variables
VITE_API_URL=https://api.metanord.eu

# Or in .env.production
VITE_API_URL=https://api.metanord.eu
```

### Issue 2: CORS Configuration
**Problem**: Backend not allowing frontend domain
**Fix**: Update backend CORS settings

```javascript
// In backend-server.js
app.use(cors({
  origin: [
    'https://metanord-frontend.vercel.app',
    'https://your-production-domain.com',
    'http://localhost:5173' // Keep for development
  ],
  credentials: true
}));
```

### Issue 3: Mixed Content (HTTPS/HTTP)
**Problem**: HTTPS frontend trying to access HTTP API
**Fix**: Ensure API uses HTTPS in production

### Issue 4: Backend Not Deployed
**Problem**: API endpoints not available
**Fix**: Deploy backend to Railway/Heroku/etc.

### Issue 5: Environment Variables Missing
**Problem**: Build-time variables not set
**Fix**: Set in deployment platform

## 🔧 Step-by-Step Fix Process

### Step 1: Check Current Configuration
1. Open browser console on production site
2. Look for API configuration logs
3. Check for CORS errors
4. Note any 404/500 errors

### Step 2: Verify Environment Variables
```bash
# Check if VITE_API_URL is set correctly
echo $VITE_API_URL

# Should be: https://api.metanord.eu (or your backend URL)
```

### Step 3: Test API Endpoints Directly
```bash
# Test careers endpoint
curl -X GET "https://api.metanord.eu/api/careers" \
  -H "Accept: application/json" \
  -H "Origin: https://your-frontend-domain.com"

# Should return job postings JSON
```

### Step 4: Check CORS Headers
```bash
# Test CORS preflight
curl -X OPTIONS "https://api.metanord.eu/api/careers" \
  -H "Origin: https://your-frontend-domain.com" \
  -H "Access-Control-Request-Method: GET" \
  -v

# Look for Access-Control-Allow-Origin header
```

### Step 5: Deploy Backend (if needed)
```bash
# If backend isn't deployed yet
cd /path/to/backend
railway login
railway init
railway up

# Set environment variables in Railway dashboard
```

## 🚀 Quick Fix Commands

### For Vercel Deployment:
```bash
# Set environment variable
vercel env add VITE_API_URL production
# Enter: https://api.metanord.eu

# Redeploy
vercel --prod
```

### For Netlify Deployment:
```bash
# Set in netlify.toml
[build.environment]
  VITE_API_URL = "https://api.metanord.eu"

# Or in Netlify dashboard: Site settings > Environment variables
```

## 🧪 Testing the Fix

### 1. Run Diagnostics
```javascript
// In browser console on production site
import { runQuickDiagnostics } from './utils/production-diagnostics';
runQuickDiagnostics();
```

### 2. Check Network Tab
- Look for failed API requests
- Check request URLs (should not be localhost)
- Verify response status codes

### 3. Test Careers Page
- Visit `/careers` page
- Should show job listings
- No console errors
- Application modal should work

## 📋 Production Checklist

- [ ] `VITE_API_URL` environment variable set correctly
- [ ] Backend deployed and accessible
- [ ] CORS configured for production domain
- [ ] HTTPS enabled on both frontend and backend
- [ ] No mixed content warnings
- [ ] API endpoints returning data
- [ ] Error boundary catches and displays issues
- [ ] Console shows API configuration logs

## 🔍 Debugging Commands

```bash
# Check current API configuration
curl -s https://your-frontend.vercel.app | grep -o 'VITE_API_URL[^"]*'

# Test API from production domain
curl -X GET "https://api.metanord.eu/api/careers" \
  -H "Origin: https://your-frontend.vercel.app" \
  -H "Accept: application/json"

# Check if backend is running
curl -I https://api.metanord.eu/api/careers
```

## 🎯 Expected Results After Fix

1. **Careers page loads successfully** in production
2. **Job listings display** (3 positions: Sales Manager, Production Engineer, Logistics Coordinator)
3. **No console errors** related to API calls
4. **Application modal works** when clicking "Apply Now"
5. **Error boundary shows helpful info** if issues occur

## 📞 If Issues Persist

1. Check browser console for specific error messages
2. Use the diagnostic tools to identify the exact issue
3. Verify backend deployment status
4. Test API endpoints manually
5. Check deployment platform logs

The enhanced error handling and diagnostics will help identify the specific issue causing the "something went wrong" error in production.
