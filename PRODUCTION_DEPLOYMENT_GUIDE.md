# MetaNord Production Deployment Guide

## 🚀 Deploying to Production

Now that the admin dashboard is fully functional in development, here's how to deploy it to production.

## 1. Backend Deployment (Railway)

### Option A: Deploy Current Backend Server
```bash
# 1. Create a new Railway project
railway login
railway init

# 2. Add environment variables in Railway dashboard
CORS_ORIGINS=https://your-frontend-domain.vercel.app
PORT=3001

# 3. Deploy the backend
railway up
```

### Option B: Integrate with Existing Backend
If you have an existing backend on Railway, add these endpoints:

```javascript
// Add to your existing Express server
app.get('/api/admin/products', requireAuth, async (req, res) => {
  // Your database query logic here
  const products = await Product.findAll({ where: { language: req.query.language } });
  res.json(products);
});

app.post('/api/admin/products', requireAuth, async (req, res) => {
  // Your product creation logic here
  const product = await Product.create(req.body);
  res.json({ success: true, product });
});

// Similar for projects, auth, etc.
```

## 2. Frontend Deployment (Vercel)

### Update Environment Variables
Create `.env.production` in the client folder:
```bash
VITE_API_URL=https://your-backend.railway.app
```

### Deploy to Vercel
```bash
cd client
vercel --prod
```

### Set Environment Variables in Vercel Dashboard
- Go to your Vercel project settings
- Add environment variable: `VITE_API_URL=https://your-backend.railway.app`
- Redeploy

## 3. Database Integration

### If using PostgreSQL on Railway:
```javascript
// Add to backend-server.js
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Replace mock data with real database queries
app.get('/api/admin/products', requireAuth, async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM products WHERE language = $1',
      [req.query.language || 'en']
    );
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## 4. Authentication Setup

### Replace Mock Authentication
```javascript
// Add proper authentication middleware
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

const requireAuth = async (req, res, next) => {
  try {
    const token = req.cookies.authToken || req.headers.authorization?.split(' ')[1];
    if (!token) return res.status(401).json({ error: 'No token provided' });
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Access denied' });
    
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
};

app.post('/api/admin/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    const user = await User.findOne({ where: { email } });
    
    if (!user || !await bcrypt.compare(password, user.password)) {
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }
    
    const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET, { expiresIn: '24h' });
    res.cookie('authToken', token, { httpOnly: true, secure: true });
    res.json({ success: true, user: { id: user.id, email: user.email, isAdmin: user.isAdmin } });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## 5. Security Checklist

### Backend Security
- [ ] Add proper CORS configuration for production domains
- [ ] Implement rate limiting
- [ ] Add input validation and sanitization
- [ ] Use HTTPS only
- [ ] Set secure cookie options
- [ ] Add request logging and monitoring

### Frontend Security
- [ ] Remove development debugging logs
- [ ] Validate all user inputs
- [ ] Implement proper error boundaries
- [ ] Add CSP headers
- [ ] Enable HTTPS redirect

## 6. Environment Variables Summary

### Backend (Railway)
```bash
DATABASE_URL=postgresql://...
JWT_SECRET=your-secret-key
CORS_ORIGINS=https://your-frontend.vercel.app
NODE_ENV=production
PORT=3001
```

### Frontend (Vercel)
```bash
VITE_API_URL=https://your-backend.railway.app
```

## 7. Testing Production Deployment

### 1. Test API Endpoints
```bash
# Test authentication
curl -X POST https://your-backend.railway.app/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your-password"}'

# Test products API
curl -X GET https://your-backend.railway.app/api/admin/products?language=en \
  -H "Authorization: Bearer your-jwt-token"
```

### 2. Test Frontend
- Navigate to `https://your-frontend.vercel.app/admin/login`
- Login with real credentials
- Test CRUD operations for products and projects
- Verify data persistence

## 8. Monitoring and Maintenance

### Set up monitoring for:
- [ ] API response times
- [ ] Error rates
- [ ] Database performance
- [ ] User authentication events
- [ ] CRUD operation success rates

### Regular maintenance:
- [ ] Monitor logs for errors
- [ ] Update dependencies
- [ ] Backup database regularly
- [ ] Review and rotate JWT secrets
- [ ] Monitor API usage patterns

## 🎯 Quick Deployment Commands

```bash
# 1. Deploy backend to Railway
cd /path/to/metanord-frontend
railway login
railway init
railway up

# 2. Deploy frontend to Vercel
cd client
vercel --prod

# 3. Set environment variables in both platforms
# Railway: DATABASE_URL, JWT_SECRET, CORS_ORIGINS
# Vercel: VITE_API_URL
```

## ✅ Production Readiness Checklist

- [ ] Backend deployed to Railway
- [ ] Frontend deployed to Vercel
- [ ] Database connected and migrated
- [ ] Environment variables configured
- [ ] Authentication system implemented
- [ ] CORS properly configured
- [ ] HTTPS enabled on both frontend and backend
- [ ] Error monitoring set up
- [ ] Admin credentials created
- [ ] CRUD operations tested in production

Once all items are checked, your MetaNord admin dashboard will be fully operational in production! 🚀
