const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware - Enhanced CORS configuration for production
const allowedOrigins = [
  'http://localhost:5173',
  'http://localhost:5174',
  'https://metanord-frontend.vercel.app',
  'https://metanord.eu',
  'https://www.metanord.eu'
];

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Load products data
const dataPath = path.join(__dirname, 'client', 'src', 'data', 'finalized', 'metanord-products.json');
let products = [];
try {
  if (fs.existsSync(dataPath)) {
    products = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
    console.log(`Loaded ${products.length} products from ${dataPath}`);
  } else {
    console.warn(`Products file not found at ${dataPath}`);
  }
} catch (e) {
  console.error('Failed to load products data', e);
}

// Mock projects data
let projects = [
  {
    id: 1,
    title: "Modern Office Complex",
    language: "en",
    slug: "modern-office-complex",
    description: "A state-of-the-art office building featuring aluminum facade systems.",
    summary: "Contemporary office design with sustainable materials",
    location: "Oslo, Norway",
    year: 2023,
    published: true,
    productTags: ["aluminum", "facade_systems"],
    images: {
      main: "/images/projects/office-complex-main.jpg",
      gallery: ["/images/projects/office-1.jpg", "/images/projects/office-2.jpg"]
    },
    createdAt: "2023-01-15T10:00:00Z",
    updatedAt: "2023-01-15T10:00:00Z"
  },
  {
    id: 2,
    title: "Residential Tower",
    language: "en",
    slug: "residential-tower",
    description: "High-rise residential building with advanced polyethylene components.",
    summary: "Luxury residential tower with modern amenities",
    location: "Bergen, Norway",
    year: 2022,
    published: true,
    productTags: ["polyethylene", "components"],
    images: {
      main: "/images/projects/residential-main.jpg",
      gallery: ["/images/projects/residential-1.jpg"]
    },
    createdAt: "2022-06-10T14:30:00Z",
    updatedAt: "2022-06-10T14:30:00Z"
  }
];

// Mock job postings data
let jobPostings = [
  {
    id: 1,
    title: "Sales Manager",
    description: "Lead our sales efforts across European markets, developing client relationships and driving revenue growth in the industrial infrastructure sector.",
    slug: "sales-manager",
    location: "Tallinn, Estonia",
    language: "en",
    published: true,
    department: "Sales",
    type: "Full-time",
    responsibilities: [
      "Develop and execute sales strategies for European markets",
      "Build and maintain relationships with key B2B clients",
      "Identify new business opportunities in infrastructure sectors",
      "Collaborate with technical teams to provide customer solutions",
      "Manage sales pipeline and achieve revenue targets"
    ],
    requirements: [
      "Bachelor's degree in Business, Engineering, or related field",
      "5+ years of B2B sales experience, preferably in industrial products",
      "Strong knowledge of European markets and regulations",
      "Excellent communication skills in English and at least one other European language",
      "Experience with CRM systems and sales analytics"
    ],
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    title: "Production Engineer",
    description: "Oversee production processes and quality control for our aluminum profiles and infrastructure products, ensuring compliance with European standards.",
    slug: "production-engineer",
    location: "Tallinn, Estonia",
    language: "en",
    published: true,
    department: "Engineering",
    type: "Full-time",
    responsibilities: [
      "Monitor and optimize production processes for aluminum profiles",
      "Implement quality control procedures and standards",
      "Collaborate with design teams on product development",
      "Ensure compliance with European manufacturing standards",
      "Lead continuous improvement initiatives"
    ],
    requirements: [
      "Bachelor's degree in Mechanical or Industrial Engineering",
      "3+ years of experience in manufacturing or production",
      "Knowledge of aluminum processing and quality standards",
      "Experience with lean manufacturing principles",
      "Strong analytical and problem-solving skills"
    ],
    createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3,
    title: "Logistics Coordinator",
    description: "Coordinate international shipments and supply chain operations, ensuring timely delivery of products across European and international markets.",
    slug: "logistics-coordinator",
    location: "Tallinn, Estonia",
    language: "en",
    published: true,
    department: "Operations",
    type: "Full-time",
    responsibilities: [
      "Manage international shipping and customs documentation",
      "Coordinate with freight forwarders and logistics partners",
      "Track shipments and resolve delivery issues",
      "Optimize shipping routes and costs",
      "Maintain inventory levels and warehouse operations"
    ],
    requirements: [
      "Bachelor's degree in Logistics, Supply Chain, or Business",
      "2+ years of experience in international logistics",
      "Knowledge of European customs and trade regulations",
      "Experience with logistics software and tracking systems",
      "Strong organizational and communication skills"
    ],
    createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString()
  }
];

// Mock user authentication
const mockUser = {
  id: 1,
  username: "admin",
  email: "<EMAIL>",
  name: "Admin User",
  role: "admin",
  isAdmin: true
};

// Session storage (in production, use Redis or database)
const activeSessions = new Map();

// Auth middleware
const requireAuth = (req, res, next) => {
  const sessionId = req.cookies?.sessionId || req.headers['x-session-id'];

  if (!sessionId) {
    console.log('[Auth] No session ID provided');
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  const session = activeSessions.get(sessionId);
  if (!session || session.expiresAt < Date.now()) {
    console.log('[Auth] Invalid or expired session');
    if (session) activeSessions.delete(sessionId);
    return res.status(401).json({ success: false, message: 'Session expired' });
  }

  // Extend session
  session.expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
  req.user = session.user;
  next();
};

// Public API endpoints
app.get('/api/products', (req, res) => {
  const language = req.query.language || 'en';
  const category = req.query.category;
  
  let filteredProducts = products.filter(p => !p.language || p.language === language);
  
  if (category && category !== 'all') {
    filteredProducts = filteredProducts.filter(p => p.category === category);
  }
  
  console.log(`[API] Returning ${filteredProducts.length} products for language: ${language}, category: ${category || 'all'}`);
  res.json(filteredProducts);
});

app.get('/api/products/:slug', (req, res) => {
  const slug = req.params.slug;
  const product = products.find(p => String(p.productId) === String(slug));
  if (product) {
    return res.json(product);
  }
  res.status(404).json({ error: 'Product not found' });
});

app.get('/api/projects', (req, res) => {
  const language = req.query.language || 'en';
  const filteredProjects = projects.filter(p => !p.language || p.language === language);
  console.log(`[API] Returning ${filteredProjects.length} projects for language: ${language}`);
  res.json(filteredProjects);
});

app.get('/api/projects/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const project = projects.find(p => p.id === id);
  if (project) {
    return res.json(project);
  }
  res.status(404).json({ error: 'Project not found' });
});

// Public Careers API endpoints
app.get('/api/careers', (req, res) => {
  const language = req.query.language || 'en';
  const filteredJobs = jobPostings.filter(job =>
    job.published && (!job.language || job.language === language)
  );
  console.log(`[API] Returning ${filteredJobs.length} job postings for language: ${language}`);
  res.json(filteredJobs);
});

app.get('/api/careers/slug/:slug', (req, res) => {
  const slug = req.params.slug;
  const language = req.query.language || 'en';

  // Find job by slug, prioritizing the requested language
  let job = jobPostings.find(j =>
    j.slug === slug &&
    j.published &&
    (!j.language || j.language === language)
  );

  // If not found in requested language, try English as fallback
  if (!job && language !== 'en') {
    job = jobPostings.find(j =>
      j.slug === slug &&
      j.published &&
      (!j.language || j.language === 'en')
    );
  }

  if (job) {
    console.log(`[API] Found job posting: ${job.title}`);
    return res.json(job);
  }

  console.log(`[API] Job posting not found for slug: ${slug}`);
  res.status(404).json({ error: 'Job posting not found' });
});

// Admin authentication endpoints
app.get('/api/admin/me', requireAuth, (req, res) => {
  console.log('[Auth] Returning authenticated user:', req.user.username);
  res.json({ success: true, user: req.user });
});

app.post('/api/admin/login', (req, res) => {
  const { email, password } = req.body;

  // Accept both username and email fields for backward compatibility
  const username = email || req.body.username;

  console.log(`[Auth] Login attempt for user: ${username}`);

  // Check for admin credentials
  if ((username === '<EMAIL>' || username === 'admin') &&
      (password === 'admin123' || password === 'admin')) {

    // Generate session ID
    const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // Create session
    const session = {
      user: mockUser,
      createdAt: Date.now(),
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      lastAccess: Date.now()
    };

    activeSessions.set(sessionId, session);

    // Set session cookie with production-ready settings
    const isProduction = process.env.NODE_ENV === 'production';
    res.cookie('sessionId', sessionId, {
      httpOnly: true,
      secure: isProduction, // Use HTTPS in production
      sameSite: isProduction ? 'none' : 'lax', // 'none' required for cross-site cookies in production
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      path: '/', // Ensure cookie is available for all paths
      // Don't set domain in development to allow localhost
    });

    console.log(`[Auth] Login successful for user: ${username}, session: ${sessionId}`);
    res.json({ success: true, user: mockUser });
  } else {
    console.log(`[Auth] Failed login attempt for user: ${username}`);
    res.status(401).json({ success: false, message: 'Invalid credentials' });
  }
});

app.post('/api/admin/logout', (req, res) => {
  const sessionId = req.cookies?.sessionId;

  if (sessionId) {
    activeSessions.delete(sessionId);
    console.log(`[Auth] Session ${sessionId} logged out`);
  }

  // Clear session cookie
  res.clearCookie('sessionId');
  res.json({ success: true, message: 'Logged out successfully' });
});

// Admin Products endpoints
app.get('/api/admin/products', requireAuth, (req, res) => {
  const language = req.query.language || 'en';
  const category = req.query.category;

  let filteredProducts = products.filter(p => !p.language || p.language === language);

  if (category && category !== 'all') {
    filteredProducts = filteredProducts.filter(p => p.category === category);
  }

  console.log(`[Admin API] Returning ${filteredProducts.length} products for admin`);
  res.json(filteredProducts);
});

// Admin Product Detail endpoint - Get individual product for editing
app.get('/api/admin/products/:id', requireAuth, (req, res) => {
  const productId = req.params.id;
  const language = req.query.language || 'en';

  console.log(`[Admin API] Fetching product ${productId} for editing (language: ${language})`);

  // Find product by productId, prioritizing the requested language
  let product = products.find(p =>
    String(p.productId) === String(productId) &&
    (!p.language || p.language === language)
  );

  // If not found in requested language, try English as fallback
  if (!product && language !== 'en') {
    product = products.find(p =>
      String(p.productId) === String(productId) &&
      (!p.language || p.language === 'en')
    );
  }

  // If still not found, try any language
  if (!product) {
    product = products.find(p => String(p.productId) === String(productId));
  }

  if (product) {
    console.log(`[Admin API] Found product ${productId} for editing:`, product.title);
    return res.json(product);
  }

  console.log(`[Admin API] Product ${productId} not found`);
  res.status(404).json({ error: 'Product not found' });
});

// Admin Product Documents endpoint
app.get('/api/admin/products/:id/documents', requireAuth, (req, res) => {
  const productId = req.params.id;
  console.log(`[Admin API] Fetching documents for product ${productId}`);

  // For now, return empty array as documents are not implemented yet
  // In a real implementation, this would fetch from a documents table/collection
  res.json([]);
});

app.post('/api/admin/products', requireAuth, (req, res) => {
  console.log('[Admin API] Creating new product:', req.body);
  // Mock creation - just return success
  res.json({ success: true, message: 'Product created successfully' });
});

app.put('/api/admin/products/:id', requireAuth, (req, res) => {
  const id = req.params.id;
  console.log(`[Admin API] Updating product ${id}:`, req.body);
  // Mock update - just return success
  res.json({ success: true, message: 'Product updated successfully' });
});

app.patch('/api/admin/products/:id', requireAuth, (req, res) => {
  const id = req.params.id;
  console.log(`[Admin API] Patching product ${id}:`, req.body);
  // Mock update - just return success
  res.json({ success: true, message: 'Product updated successfully' });
});

app.delete('/api/admin/products/:id', requireAuth, (req, res) => {
  const id = req.params.id;
  console.log(`[Admin API] Deleting product ${id}`);
  // Mock deletion - just return success
  res.json({ success: true, message: 'Product deleted successfully' });
});

// Admin Projects endpoints
app.get('/api/admin/projects', requireAuth, (req, res) => {
  const language = req.query.language || 'en';
  const filteredProjects = projects.filter(p => !p.language || p.language === language);
  console.log(`[Admin API] Returning ${filteredProjects.length} projects for admin`);
  res.json(filteredProjects);
});

app.post('/api/admin/projects', requireAuth, (req, res) => {
  console.log('[Admin API] Creating new project:', req.body);
  const newProject = {
    id: projects.length + 1,
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  projects.push(newProject);
  res.json({ success: true, project: newProject });
});

app.patch('/api/admin/projects/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`[Admin API] Updating project ${id}:`, req.body);
  const projectIndex = projects.findIndex(p => p.id === id);
  if (projectIndex !== -1) {
    projects[projectIndex] = { ...projects[projectIndex], ...req.body, updatedAt: new Date().toISOString() };
    res.json({ success: true, project: projects[projectIndex] });
  } else {
    res.status(404).json({ error: 'Project not found' });
  }
});

app.delete('/api/admin/projects/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`[Admin API] Deleting project ${id}`);
  const projectIndex = projects.findIndex(p => p.id === id);
  if (projectIndex !== -1) {
    projects.splice(projectIndex, 1);
    res.json({ success: true, message: 'Project deleted successfully' });
  } else {
    res.status(404).json({ error: 'Project not found' });
  }
});

// Admin Careers endpoints
app.get('/api/admin/careers', requireAuth, (req, res) => {
  const language = req.query.language || 'en';
  const filteredJobs = jobPostings.filter(job => !job.language || job.language === language);
  console.log(`[Admin API] Returning ${filteredJobs.length} job postings for admin`);
  res.json(filteredJobs);
});

app.get('/api/admin/careers/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const job = jobPostings.find(j => j.id === id);
  if (job) {
    console.log(`[Admin API] Found job posting ${id} for editing:`, job.title);
    return res.json(job);
  }
  console.log(`[Admin API] Job posting ${id} not found`);
  res.status(404).json({ error: 'Job posting not found' });
});

app.post('/api/admin/careers', requireAuth, (req, res) => {
  console.log('[Admin API] Creating new job posting:', req.body);
  const newJob = {
    id: Math.max(...jobPostings.map(j => j.id), 0) + 1,
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  jobPostings.push(newJob);
  res.json({ success: true, job: newJob });
});

app.put('/api/admin/careers/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`[Admin API] Updating job posting ${id}:`, req.body);
  const jobIndex = jobPostings.findIndex(j => j.id === id);
  if (jobIndex !== -1) {
    jobPostings[jobIndex] = {
      ...jobPostings[jobIndex],
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    res.json({ success: true, job: jobPostings[jobIndex] });
  } else {
    res.status(404).json({ error: 'Job posting not found' });
  }
});

app.delete('/api/admin/careers/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`[Admin API] Deleting job posting ${id}`);
  const jobIndex = jobPostings.findIndex(j => j.id === id);
  if (jobIndex !== -1) {
    jobPostings.splice(jobIndex, 1);
    res.json({ success: true, message: 'Job posting deleted successfully' });
  } else {
    res.status(404).json({ error: 'Job posting not found' });
  }
});

// Mock data arrays (need to be defined before endpoints use them)
const mockQuotes = [
  {
    id: 1,
    name: 'John Anderson',
    email: '<EMAIL>',
    company: 'Nordic Construction AS',
    phone: '+47 123 45 678',
    productId: 'aluminum-u-profiles',
    productName: 'Aluminum U-Profiles',
    quantity: '500 units',
    comment: 'We need these profiles for an office building renovation project. Urgent delivery required within 2 weeks.',
    status: 'new',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    name: 'Sarah Lindberg',
    email: '<EMAIL>',
    company: 'Baltic Industries OÜ',
    phone: '+372 987 65 432',
    productId: 'led-profiles',
    productName: 'LED Aluminum Profiles',
    quantity: '1200 meters',
    comment: 'Large order for warehouse expansion project. Looking for bulk pricing and flexible delivery schedule.',
    status: 'in progress',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3,
    name: 'Michael Petrov',
    email: '<EMAIL>',
    company: 'Construction Pro Ltd',
    phone: '+371 555 12 345',
    productId: 'special-profiles',
    productName: 'Special Aluminum Profiles',
    quantity: '300 units',
    comment: 'Custom profiles needed for specialized building system. Please provide technical consultation.',
    status: 'quoted',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 4,
    name: 'Anna Kowalski',
    email: '<EMAIL>',
    company: 'MetalWorks Poland',
    phone: '+48 ***********',
    productId: 'machine-building-profiles',
    productName: 'Aluminum T-Profiles',
    quantity: '800 pieces',
    comment: 'Industrial automation project requires precision T-profiles. Quality certification needed.',
    status: 'completed',
    createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
  }
];

// Public Contact Form endpoint
app.post('/api/contact', (req, res) => {
  console.log('[API] Received contact form submission:', req.body);

  try {
    const { name, email, company, phone, subject, message, productInterest, inquiryType } = req.body;

    // Validate required fields
    if (!name || !email || !message) {
      return res.status(400).json({
        success: false,
        message: 'Name, email, and message are required fields'
      });
    }

    // Create new inquiry
    const newInquiry = {
      id: Math.max(...mockInquiries.map(i => i.id), 0) + 1,
      name,
      email,
      company: company || '',
      phone: phone || '',
      subject: subject || 'Contact Form Inquiry',
      message,
      status: 'new',
      archived: false,
      createdAt: new Date().toISOString(),
      inquiryType: inquiryType || 'general',
      productInterest: productInterest || ''
    };

    // Add to mock inquiries array
    mockInquiries.unshift(newInquiry); // Add to beginning for recent display

    console.log(`[API] Created new inquiry with ID: ${newInquiry.id}`);

    // Simulate email sending (in real app, this would send actual emails)
    const emailSent = Math.random() > 0.1; // 90% success rate simulation

    res.json({
      success: true,
      message: 'Contact form submitted successfully',
      inquiryId: newInquiry.id,
      emailSent,
      emailError: !emailSent ? 'Email service temporarily unavailable' : null
    });

  } catch (error) {
    console.error('[API] Error processing contact form:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while processing your request'
    });
  }
});

// Public Quote Request endpoint
app.post('/api/quote', (req, res) => {
  console.log('[API] Received quote request:', req.body);

  try {
    const { name, email, company, phone, productId, productName, quantity, comment } = req.body;

    // Validate required fields
    if (!name || !email || !productName || !quantity) {
      return res.status(400).json({
        success: false,
        message: 'Name, email, product name, and quantity are required fields'
      });
    }

    // Create new quote request
    const newQuote = {
      id: Math.max(...mockQuotes.map(q => q.id), 0) + 1,
      name,
      email,
      company: company || '',
      phone: phone || '',
      productId: productId || '',
      productName,
      quantity,
      comment: comment || '',
      status: 'new',
      createdAt: new Date().toISOString()
    };

    // Add to mock quotes array
    mockQuotes.unshift(newQuote); // Add to beginning for recent display

    console.log(`[API] Created new quote request with ID: ${newQuote.id}`);

    res.json({
      success: true,
      message: 'Quote request submitted successfully',
      quoteId: newQuote.id
    });

  } catch (error) {
    console.error('[API] Error processing quote request:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while processing your request'
    });
  }
});

// Mock data for admin endpoints
// Real contact inquiries (test data removed)
const mockInquiries = [
  {
    id: 1,
    name: 'Anna Kowalski',
    email: '<EMAIL>',
    subject: 'Technical Specifications',
    message: 'Hello, I am working on a construction project in Warsaw and need detailed technical specifications for your aluminum profiles. Could you send me the complete documentation?',
    status: 'new',
    archived: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    company: 'TechBuild Poland',
    phone: '+48 22 123 4567',
    inquiryType: 'technical'
  },
  {
    id: 2,
    name: 'Lars Eriksson',
    email: '<EMAIL>',
    subject: 'Partnership Inquiry',
    message: 'We are a construction company based in Stockholm and are interested in establishing a partnership for aluminum profile supply. Could we schedule a meeting to discuss terms?',
    status: 'read',
    archived: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    company: 'Nordic Build AB',
    phone: '+46 8 123 456 78',
    inquiryType: 'partnership'
  },
  {
    id: 3,
    name: 'Elena Petrov',
    email: '<EMAIL>',
    subject: 'Custom Profile Request',
    message: 'We need custom aluminum profiles for a special architectural project. The profiles need to meet specific dimensions and load requirements. Can you help with custom manufacturing?',
    status: 'new',
    archived: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    company: 'Baltic Steel Solutions',
    phone: '+371 67 123 456',
    inquiryType: 'custom'
  }
];

// Real offers (test data removed)
const mockOffers = [
  {
    id: 1,
    clientName: 'TechBuild Poland',
    clientEmail: '<EMAIL>',
    amount: 12500,
    status: 'sent',
    description: 'Custom Aluminum Profiles - Technical Specifications Package',
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    createdAt: new Date(Date.now() - *********).toISOString(),
    updatedAt: new Date(Date.now() - *********).toISOString()
  }
];

const mockUsers = [
  {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    lastLogin: new Date().toISOString(),
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    name: 'Editor User',
    email: '<EMAIL>',
    role: 'editor',
    status: 'active',
    lastLogin: new Date(Date.now() - 86400000).toISOString(),
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
  }
];

const mockDocuments = [
  {
    id: 1,
    title: 'Product Catalog 2024',
    description: 'Complete product catalog with specifications',
    fileUrl: '/documents/catalog-2024.pdf',
    fileType: 'application/pdf',
    fileSize: 2048576,
    uploadDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    category: 'catalog'
  },
  {
    id: 2,
    title: 'Technical Specifications',
    description: 'Detailed technical specifications for aluminum profiles',
    fileUrl: '/documents/tech-specs.pdf',
    fileType: 'application/pdf',
    fileSize: 1024768,
    uploadDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
    category: 'technical'
  }
];

// Admin Inquiries endpoints
app.get('/api/admin/inquiries', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching inquiries');
  res.json(mockInquiries);
});

app.get('/api/admin/contact', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching contact inquiries');
  res.json(mockInquiries);
});

app.post('/api/admin/contact/:id/status', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const { status } = req.body;
  console.log(`[Admin API] Updating inquiry ${id} status to ${status}`);
  res.json({ success: true, message: 'Status updated successfully' });
});

app.post('/api/admin/contact/:id/archive', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const { archived } = req.body;
  console.log(`[Admin API] ${archived ? 'Archiving' : 'Unarchiving'} inquiry ${id}`);
  res.json({ success: true, message: `Inquiry ${archived ? 'archived' : 'unarchived'} successfully` });
});

// Additional inquiry endpoints for the ContactInquiries component
app.post('/api/admin/inquiries/:id/status', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const { status } = req.body;
  console.log(`[Admin API] Updating inquiry ${id} status to ${status}`);
  res.json({ success: true, message: 'Inquiry status updated successfully' });
});

app.post('/api/admin/inquiries/:id/archive', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const { archived } = req.body;
  console.log(`[Admin API] ${archived ? 'Archiving' : 'Unarchiving'} inquiry ${id}`);
  res.json({ success: true, message: `Inquiry ${archived ? 'archived' : 'unarchived'} successfully` });
});

app.get('/api/admin/inquiries/export', requireAuth, (req, res) => {
  const format = req.query.format || 'csv';
  const status = req.query.status;
  const archived = req.query.archived;
  console.log(`[Admin API] Exporting inquiries in ${format} format with filters:`, { status, archived });

  // Mock export response
  if (format === 'csv') {
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="inquiries.csv"');
    res.send('id,name,email,subject,status,created_at\n1,John Smith,<EMAIL>,Product Inquiry,new,2025-06-06');
  } else {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename="inquiries.json"');
    res.json(mockInquiries);
  }
});

// Admin Offers endpoints
app.get('/api/admin/offers', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching offers');
  res.json(mockOffers);
});

app.post('/api/admin/offers', requireAuth, (req, res) => {
  console.log('[Admin API] Creating new offer:', req.body);
  const newOffer = {
    id: mockOffers.length + 1,
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  mockOffers.push(newOffer);
  res.json({ success: true, offer: newOffer });
});

// Admin Users endpoints
app.get('/api/admin/users', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching users');
  res.json(mockUsers);
});

// Admin Documents endpoints
app.get('/api/admin/documents', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching documents');
  res.json(mockDocuments);
});

app.post('/api/admin/documents', requireAuth, (req, res) => {
  console.log('[Admin API] Uploading new document:', req.body);
  const newDocument = {
    id: mockDocuments.length + 1,
    ...req.body,
    uploadDate: new Date().toISOString()
  };
  mockDocuments.push(newDocument);
  res.json({ success: true, document: newDocument });
});

// Admin Dashboard endpoint
app.get('/api/admin/dashboard', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching dashboard stats');

  const dashboardStats = {
    success: true,
    stats: {
      users: {
        total: mockUsers.length,
        admins: mockUsers.filter(u => u.role === 'admin').length,
        editors: mockUsers.filter(u => u.role === 'editor').length,
        viewers: mockUsers.filter(u => u.role === 'viewer').length,
      },
      inquiries: {
        total: mockInquiries.length,
        new: mockInquiries.filter(i => i.status === 'new').length,
        inProgress: mockInquiries.filter(i => i.status === 'read').length,
        resolved: mockInquiries.filter(i => i.status === 'replied').length,
        archived: mockInquiries.filter(i => i.archived).length,
      },
      quoteRequests: {
        total: mockQuotes.length,
        new: mockQuotes.filter(q => q.status === 'new').length,
        reviewing: mockQuotes.filter(q => q.status === 'in progress').length,
        quoted: mockQuotes.filter(q => q.status === 'quoted').length,
        accepted: mockQuotes.filter(q => q.status === 'completed').length,
        rejected: 0,
      },
      crm: {
        totalClients: mockClients.length,
        newLeads: mockClients.filter(c => c.status === 'lead').length,
        qualified: mockClients.filter(c => c.status === 'active').length,
        proposal: 0,
        closedWon: 0,
      },
      offers: {
        total: mockOffers.length,
        draft: mockOffers.filter(o => o.status === 'draft').length,
        sent: mockOffers.filter(o => o.status === 'sent').length,
        accepted: mockOffers.filter(o => o.status === 'accepted').length,
        rejected: mockOffers.filter(o => o.status === 'rejected').length,
        expired: mockOffers.filter(o => o.status === 'expired').length,
      },
      content: {
        documents: mockDocuments.length,
        projects: projects.length,
        products: products.length,
        publishedProjects: projects.filter(p => p.published).length,
      },
      notifications: {
        unread: mockNotifications.filter(n => !n.read).length,
      },
    },
    recentActivity: {
      inquiries: mockInquiries.slice(0, 5).map(inquiry => ({
        id: inquiry.id,
        name: inquiry.name,
        email: inquiry.email,
        message: inquiry.message,
        status: inquiry.status,
        createdAt: inquiry.createdAt
      })),
      quoteRequests: mockQuotes.slice(0, 5).map(quote => ({
        id: quote.id,
        productName: quote.productName,
        clientName: quote.name,
        status: quote.status,
        createdAt: quote.createdAt
      }))
    }
  };

  res.json(dashboardStats);
});

// Admin Quotes endpoints

app.get('/api/admin/quotes', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching quote requests');
  res.json({ success: true, quotes: mockQuotes });
});

app.post('/api/admin/quotes/:id/status', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const { status } = req.body;
  console.log(`[Admin API] Updating quote ${id} status to ${status}`);
  res.json({ success: true, message: 'Quote status updated successfully' });
});

// Admin CRM endpoints
const mockClients = [
  {
    id: 1,
    name: 'Nordic Construction AS',
    email: '<EMAIL>',
    phone: '+47 123 45 678',
    company: 'Nordic Construction AS',
    status: 'active',
    type: 'enterprise',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    lastContact: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    name: 'Baltic Industries OÜ',
    email: '<EMAIL>',
    phone: '+372 987 65 432',
    company: 'Baltic Industries OÜ',
    status: 'lead',
    type: 'enterprise',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    lastContact: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
  }
];

app.get('/api/admin/crm/clients', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching CRM clients');
  res.json(mockClients);
});

// Admin Navigation endpoints
const mockNavigation = [
  {
    id: 1,
    label: 'Home',
    href: '/',
    order: 1,
    visible: true,
    language: 'en'
  },
  {
    id: 2,
    label: 'Products',
    href: '/products',
    order: 2,
    visible: true,
    language: 'en'
  },
  {
    id: 3,
    label: 'Projects',
    href: '/projects',
    order: 3,
    visible: true,
    language: 'en'
  },
  {
    id: 4,
    label: 'Contact',
    href: '/contact',
    order: 4,
    visible: true,
    language: 'en'
  }
];

app.get('/api/admin/navigation', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching navigation items');
  res.json(mockNavigation);
});

// Admin Media endpoints
const mockMedia = [
  {
    id: 1,
    name: 'product-image-1.jpg',
    url: '/images/products/aluminum-u-profile.jpg',
    type: 'image',
    size: 245760,
    folder: 'products',
    uploadedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    name: 'project-hero.jpg',
    url: '/images/projects/office-complex-main.jpg',
    type: 'image',
    size: 512000,
    folder: 'projects',
    uploadedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
  }
];

app.get('/api/admin/media', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching media items');
  const folder = req.query.folder || '';
  const type = req.query.type || 'all';
  const search = req.query.search || '';

  let filteredMedia = mockMedia;

  if (folder) {
    filteredMedia = filteredMedia.filter(m => m.folder === folder);
  }

  if (type !== 'all') {
    filteredMedia = filteredMedia.filter(m => m.type === type);
  }

  if (search) {
    filteredMedia = filteredMedia.filter(m =>
      m.name.toLowerCase().includes(search.toLowerCase())
    );
  }

  res.json(filteredMedia);
});

// Admin Notifications endpoints
const mockNotifications = [
  {
    id: 1,
    title: 'New Quote Request',
    message: 'John Anderson requested a quote for Aluminum U-Profiles',
    type: 'quote',
    read: false,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    title: 'New Contact Inquiry',
    message: 'Sarah Johnson sent a message about delivery options',
    type: 'contact',
    read: false,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3,
    title: 'Order Update',
    message: 'Order #1234 has been shipped',
    type: 'order',
    read: true,
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  }
];

app.get('/api/admin/notifications', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching notifications');
  res.json(mockNotifications);
});

app.get('/api/admin/notifications/unread', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching unread notifications');
  const unreadNotifications = mockNotifications.filter(n => !n.read);
  res.json(unreadNotifications);
});

// Mark notification as read (support both POST and PATCH)
app.post('/api/admin/notifications/:id/read', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`[Admin API] Marking notification ${id} as read`);

  // Find and update the notification
  const notification = mockNotifications.find(n => n.id === id);
  if (notification) {
    notification.read = true;
    res.json({ success: true, message: 'Notification marked as read' });
  } else {
    res.status(404).json({ error: 'Notification not found' });
  }
});

app.patch('/api/admin/notifications/:id/read', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`[Admin API] Marking notification ${id} as read`);

  // Find and update the notification
  const notification = mockNotifications.find(n => n.id === id);
  if (notification) {
    notification.read = true;
    res.json({ success: true, message: 'Notification marked as read' });
  } else {
    res.status(404).json({ error: 'Notification not found' });
  }
});

// Mark notification as unread
app.patch('/api/admin/notifications/:id/unread', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`[Admin API] Marking notification ${id} as unread`);

  // Find and update the notification
  const notification = mockNotifications.find(n => n.id === id);
  if (notification) {
    notification.read = false;
    res.json({ success: true, message: 'Notification marked as unread' });
  } else {
    res.status(404).json({ error: 'Notification not found' });
  }
});

// Mark all notifications as read
app.patch('/api/admin/notifications/mark-all-read', requireAuth, (req, res) => {
  console.log('[Admin API] Marking all notifications as read');

  // Mark all notifications as read
  mockNotifications.forEach(notification => {
    notification.read = true;
  });

  res.json({ success: true, message: 'All notifications marked as read' });
});

// Delete single notification
app.delete('/api/admin/notifications/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`[Admin API] Deleting notification ${id}`);

  // Find and remove the notification
  const index = mockNotifications.findIndex(n => n.id === id);
  if (index !== -1) {
    mockNotifications.splice(index, 1);
    res.json({ success: true, message: 'Notification deleted successfully' });
  } else {
    res.status(404).json({ error: 'Notification not found' });
  }
});

// Clear all notifications
app.delete('/api/admin/notifications/clear-all', requireAuth, (req, res) => {
  console.log('[Admin API] Clearing all notifications');

  // Clear all notifications
  mockNotifications.length = 0;

  res.json({ success: true, message: 'All notifications cleared successfully' });
});

// ===== SITE CONTENT MANAGEMENT ENDPOINTS =====

// Mock site content data
let siteContent = [
  {
    id: 1,
    section: "about",
    key: "company_description",
    value: "MetaNord is a leading supplier of high-quality industrial infrastructure products across Europe. We specialize in aluminum profiles, polyethylene systems, steel products, and comprehensive drainage solutions.",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    section: "about",
    key: "mission",
    value: "To provide innovative, sustainable infrastructure solutions that build stronger communities and support economic growth across Europe.",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 3,
    section: "about",
    key: "values",
    value: "Quality, Innovation, Sustainability, Customer Focus, European Excellence",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 4,
    section: "footer",
    key: "company_tagline",
    value: "Building Europe's Infrastructure Future",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 5,
    section: "footer",
    key: "address",
    value: "Tallinn, Estonia",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 6,
    section: "footer",
    key: "email",
    value: "<EMAIL>",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 7,
    section: "footer",
    key: "phone",
    value: "+372 XXX XXXX",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 8,
    section: "contact",
    key: "contact_text",
    value: "Get in touch with our team for inquiries about our products and services.",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// GET /api/admin/content - Fetch all site content
app.get('/api/admin/content', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching site content');
  res.json(siteContent);
});

// POST /api/admin/content - Create new content item
app.post('/api/admin/content', requireAuth, (req, res) => {
  const { section, key, value, language = 'en' } = req.body;

  console.log('[Admin API] Creating new content item:', { section, key, language });

  // Validate required fields
  if (!section || !key || !value) {
    return res.status(400).json({ error: 'Missing required fields: section, key, value' });
  }

  // Check for duplicate content (same section, key, language)
  const existingContent = siteContent.find(c =>
    c.section === section && c.key === key && c.language === language
  );

  if (existingContent) {
    return res.status(409).json({ error: 'Content with this section, key, and language already exists' });
  }

  // Create new content item
  const newContent = {
    id: Math.max(...siteContent.map(c => c.id), 0) + 1,
    section,
    key,
    value,
    language,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  siteContent.push(newContent);
  res.json(newContent);
});

// PUT /api/admin/content/:id - Update existing content item
app.put('/api/admin/content/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const { section, key, value, language } = req.body;

  console.log(`[Admin API] Updating content item ${id}:`, { section, key, language });

  // Find content item
  const contentIndex = siteContent.findIndex(c => c.id === id);
  if (contentIndex === -1) {
    return res.status(404).json({ error: 'Content item not found' });
  }

  // Validate required fields
  if (!section || !key || !value || !language) {
    return res.status(400).json({ error: 'Missing required fields: section, key, value, language' });
  }

  // Check for duplicate content (excluding current item)
  const existingContent = siteContent.find(c =>
    c.id !== id && c.section === section && c.key === key && c.language === language
  );

  if (existingContent) {
    return res.status(409).json({ error: 'Content with this section, key, and language already exists' });
  }

  // Update content item
  siteContent[contentIndex] = {
    ...siteContent[contentIndex],
    section,
    key,
    value,
    language,
    updated_at: new Date().toISOString()
  };

  res.json(siteContent[contentIndex]);
});

// DELETE /api/admin/content/:id - Delete content item
app.delete('/api/admin/content/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);

  console.log(`[Admin API] Deleting content item ${id}`);

  // Find and remove content item
  const contentIndex = siteContent.findIndex(c => c.id === id);
  if (contentIndex === -1) {
    return res.status(404).json({ error: 'Content item not found' });
  }

  siteContent.splice(contentIndex, 1);
  res.json({ success: true, message: 'Content item deleted successfully' });
});

// ===== PAGES MANAGEMENT ENDPOINTS =====

// Real MetaNord website pages
let pages = [
  {
    id: 1,
    title: "Home Page",
    slug: "home",
    content: `<div class="hero-section">
      <h1>MetaNord - European Infrastructure Solutions</h1>
      <p class="lead">Leading supplier of aluminum profiles, polyethylene systems, steel products, and comprehensive drainage solutions across Europe.</p>
      <div class="cta-buttons">
        <a href="/products" class="btn btn-primary">Explore Products</a>
        <a href="/contact" class="btn btn-secondary">Get Quote</a>
      </div>
    </div>

    <div class="features-section">
      <h2>Our Product Categories</h2>
      <div class="product-grid">
        <div class="product-card">
          <h3>Aluminum Profiles</h3>
          <p>High-quality aluminum solutions for construction and industrial applications.</p>
        </div>
        <div class="product-card">
          <h3>Polyethylene Systems</h3>
          <p>Durable polyethylene products for water management and infrastructure.</p>
        </div>
        <div class="product-card">
          <h3>Steel Products</h3>
          <p>Robust steel components for heavy-duty construction projects.</p>
        </div>
        <div class="product-card">
          <h3>Drainage Systems</h3>
          <p>Comprehensive water management solutions for urban infrastructure.</p>
        </div>
      </div>
    </div>`,
    meta_title: "MetaNord - European Infrastructure Solutions | Aluminum Profiles & Steel Products",
    meta_description: "Leading European supplier of aluminum profiles, polyethylene systems, steel products, and drainage solutions. Quality infrastructure products for construction and industry.",
    status: "published",
    language: "en",
    template: "homepage",
    created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    title: "Products",
    slug: "products",
    content: `<div class="products-header">
      <h1>Our Product Range</h1>
      <p>MetaNord offers a comprehensive selection of high-quality infrastructure products designed to meet the demanding requirements of European construction and industrial markets.</p>
    </div>

    <div class="product-categories">
      <div class="category-section">
        <h2>Aluminum Profiles</h2>
        <p>Our aluminum profile systems are engineered for precision and durability, suitable for architectural, industrial, and specialized applications across Europe.</p>
        <ul>
          <li>U-Profiles for structural applications</li>
          <li>T-Profiles for machine building</li>
          <li>LED Profiles for lighting systems</li>
          <li>Custom profiles for specialized projects</li>
        </ul>
      </div>

      <div class="category-section">
        <h2>Polyethylene Systems</h2>
        <p>High-density polyethylene products designed for water management, chemical resistance, and long-term durability in harsh environments.</p>
      </div>

      <div class="category-section">
        <h2>Steel Products</h2>
        <p>Premium steel components manufactured to European standards, providing exceptional strength and reliability for construction projects.</p>
      </div>

      <div class="category-section">
        <h2>Drainage Systems</h2>
        <p>Complete drainage solutions for urban infrastructure, including manholes, grates, and water management systems.</p>
      </div>
    </div>`,
    meta_title: "Products - MetaNord Infrastructure Solutions | Aluminum, Steel & Drainage",
    meta_description: "Explore MetaNord's comprehensive range of aluminum profiles, polyethylene systems, steel products, and drainage solutions for European markets.",
    status: "published",
    language: "en",
    template: "products",
    created_at: new Date(Date.now() - 50 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3,
    title: "Services",
    slug: "services",
    content: `<div class="services-header">
      <h1>Our Services</h1>
      <p>MetaNord provides comprehensive services to support your infrastructure projects from conception to completion.</p>
    </div>

    <div class="services-grid">
      <div class="service-item">
        <h3>Technical Consultation</h3>
        <p>Our experienced engineers provide expert guidance on product selection, specifications, and application requirements.</p>
      </div>

      <div class="service-item">
        <h3>Custom Manufacturing</h3>
        <p>We offer custom manufacturing services for specialized profiles and components to meet unique project requirements.</p>
      </div>

      <div class="service-item">
        <h3>Quality Assurance</h3>
        <p>All products undergo rigorous quality testing to ensure compliance with European standards and specifications.</p>
      </div>

      <div class="service-item">
        <h3>Logistics & Delivery</h3>
        <p>Efficient logistics network ensuring timely delivery across Europe with flexible shipping options.</p>
      </div>

      <div class="service-item">
        <h3>Technical Support</h3>
        <p>Ongoing technical support and assistance throughout your project lifecycle.</p>
      </div>

      <div class="service-item">
        <h3>Documentation</h3>
        <p>Comprehensive technical documentation, certifications, and compliance reports for all products.</p>
      </div>
    </div>`,
    meta_title: "Services - MetaNord | Technical Consultation & Custom Manufacturing",
    meta_description: "MetaNord offers technical consultation, custom manufacturing, quality assurance, and comprehensive support services for infrastructure projects.",
    status: "published",
    language: "en",
    template: "services",
    created_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 4,
    title: "About Us",
    slug: "about-us",
    content: `<div class="about-header">
      <h1>About MetaNord</h1>
      <p class="lead">MetaNord is a leading supplier of high-quality industrial infrastructure products across Europe, specializing in aluminum profiles, polyethylene systems, steel products, and comprehensive drainage solutions.</p>
    </div>

    <div class="company-info">
      <div class="mission-section">
        <h2>Our Mission</h2>
        <p>To provide innovative, sustainable infrastructure solutions that build stronger communities and support economic growth across Europe. We are committed to delivering products that meet the highest standards of quality, durability, and environmental responsibility.</p>
      </div>

      <div class="values-section">
        <h2>Our Values</h2>
        <ul>
          <li><strong>Quality:</strong> Uncompromising commitment to product excellence and customer satisfaction</li>
          <li><strong>Innovation:</strong> Continuous development of cutting-edge solutions for modern infrastructure</li>
          <li><strong>Sustainability:</strong> Environmental responsibility in all aspects of our operations</li>
          <li><strong>Customer Focus:</strong> Dedicated support and service throughout the project lifecycle</li>
          <li><strong>European Excellence:</strong> Adherence to the highest European standards and regulations</li>
        </ul>
      </div>

      <div class="history-section">
        <h2>Company History</h2>
        <p>Founded with a vision to serve the growing infrastructure needs of Europe, MetaNord has established itself as a trusted partner for construction companies, architects, and industrial manufacturers across the continent.</p>
        <p>Our strategic location in Estonia provides excellent access to both Nordic and Baltic markets, while our extensive logistics network ensures efficient delivery throughout Europe.</p>
      </div>

      <div class="expertise-section">
        <h2>Our Expertise</h2>
        <p>With years of experience in the infrastructure industry, our team combines technical expertise with deep market knowledge to deliver solutions that meet the specific needs of European markets.</p>
        <ul>
          <li>Aluminum profile systems for architectural and industrial applications</li>
          <li>Polyethylene products for water management and chemical applications</li>
          <li>Steel components for heavy-duty construction projects</li>
          <li>Comprehensive drainage systems for urban infrastructure</li>
        </ul>
      </div>
    </div>`,
    meta_title: "About MetaNord - European Infrastructure Solutions Company",
    meta_description: "Learn about MetaNord's mission to provide innovative, sustainable infrastructure solutions across Europe. Quality aluminum profiles, steel products, and drainage systems.",
    status: "published",
    language: "en",
    template: "about",
    created_at: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 5,
    title: "Contact",
    slug: "contact",
    content: `<div class="contact-header">
      <h1>Contact MetaNord</h1>
      <p>Get in touch with our team for inquiries about our products and services. We're here to help with your infrastructure needs.</p>
    </div>

    <div class="contact-info">
      <div class="office-info">
        <h2>Head Office</h2>
        <div class="address">
          <h3>MetaNord OÜ</h3>
          <p>Tallinn, Estonia</p>
          <p>European Union</p>
        </div>

        <div class="contact-details">
          <h3>Contact Information</h3>
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Phone:</strong> +372 XXX XXXX</p>
          <p><strong>Business Hours:</strong> Monday - Friday, 9:00 - 17:00 EET</p>
        </div>
      </div>

      <div class="departments">
        <h2>Department Contacts</h2>
        <div class="dept-list">
          <div class="dept-item">
            <h3>Sales & Inquiries</h3>
            <p><EMAIL></p>
            <p>For product information, quotes, and general sales inquiries</p>
          </div>

          <div class="dept-item">
            <h3>Technical Support</h3>
            <p><EMAIL></p>
            <p>For technical specifications, engineering support, and product guidance</p>
          </div>

          <div class="dept-item">
            <h3>Customer Service</h3>
            <p><EMAIL></p>
            <p>For order status, delivery information, and customer support</p>
          </div>
        </div>
      </div>

      <div class="service-areas">
        <h2>Service Areas</h2>
        <p>MetaNord serves customers throughout Europe, with particular focus on:</p>
        <ul>
          <li>Nordic Countries (Norway, Sweden, Finland, Denmark)</li>
          <li>Baltic States (Estonia, Latvia, Lithuania)</li>
          <li>Central Europe (Poland, Germany, Czech Republic)</li>
          <li>Other European markets upon request</li>
        </ul>
      </div>
    </div>`,
    meta_title: "Contact MetaNord - Get in Touch | European Infrastructure Solutions",
    meta_description: "Contact MetaNord for inquiries about our infrastructure products and services. We serve clients across Europe with quality solutions.",
    status: "published",
    language: "en",
    template: "contact",
    created_at: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 6,
    title: "Careers",
    slug: "careers",
    content: `<div class="careers-header">
      <h1>Join Our Team</h1>
      <p>MetaNord is always looking for talented individuals to join our growing team. We offer exciting opportunities in the infrastructure industry across Europe.</p>
    </div>

    <div class="company-culture">
      <h2>Why Work at MetaNord?</h2>
      <div class="benefits-grid">
        <div class="benefit-item">
          <h3>Growth Opportunities</h3>
          <p>Advance your career in a dynamic, growing company with opportunities across Europe.</p>
        </div>

        <div class="benefit-item">
          <h3>Innovation Focus</h3>
          <p>Work with cutting-edge technologies and contribute to innovative infrastructure solutions.</p>
        </div>

        <div class="benefit-item">
          <h3>European Reach</h3>
          <p>Be part of projects that span across European markets and make a real impact.</p>
        </div>

        <div class="benefit-item">
          <h3>Team Environment</h3>
          <p>Collaborate with experienced professionals in a supportive, team-oriented culture.</p>
        </div>
      </div>
    </div>

    <div class="open-positions">
      <h2>Current Openings</h2>
      <p>We are currently seeking qualified candidates for the following positions:</p>

      <div class="job-listings">
        <div class="job-item">
          <h3>Sales Manager</h3>
          <p><strong>Location:</strong> Tallinn, Estonia</p>
          <p><strong>Department:</strong> Sales</p>
          <p>Lead our sales efforts across European markets, developing client relationships and driving revenue growth.</p>
        </div>

        <div class="job-item">
          <h3>Production Engineer</h3>
          <p><strong>Location:</strong> Tallinn, Estonia</p>
          <p><strong>Department:</strong> Engineering</p>
          <p>Oversee production processes and quality control for our aluminum profiles and infrastructure products.</p>
        </div>

        <div class="job-item">
          <h3>Logistics Coordinator</h3>
          <p><strong>Location:</strong> Tallinn, Estonia</p>
          <p><strong>Department:</strong> Operations</p>
          <p>Coordinate international shipments and supply chain operations across European markets.</p>
        </div>
      </div>
    </div>

    <div class="application-info">
      <h2>How to Apply</h2>
      <p>Interested in joining our team? Send your CV and cover letter to <strong><EMAIL></strong></p>
      <p>Please include the position title in your email subject line.</p>
    </div>`,
    meta_title: "Careers at MetaNord - Join Our European Infrastructure Team",
    meta_description: "Explore career opportunities at MetaNord. Join our team working on innovative infrastructure solutions across Europe. Current openings available.",
    status: "published",
    language: "en",
    template: "careers",
    created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 7,
    title: "Projects",
    slug: "projects",
    content: `<div class="projects-header">
      <h1>Our Projects</h1>
      <p>Discover how MetaNord's infrastructure solutions have been successfully implemented in projects across Europe.</p>
    </div>

    <div class="project-showcase">
      <h2>Featured Projects</h2>

      <div class="project-grid">
        <div class="project-item">
          <h3>Modern Office Complex - Oslo, Norway</h3>
          <p><strong>Year:</strong> 2023</p>
          <p><strong>Products Used:</strong> Aluminum Profiles, Facade Systems</p>
          <p>A state-of-the-art office building featuring our aluminum facade systems, demonstrating contemporary design with sustainable materials.</p>
        </div>

        <div class="project-item">
          <h3>Residential Tower - Bergen, Norway</h3>
          <p><strong>Year:</strong> 2022</p>
          <p><strong>Products Used:</strong> Polyethylene Components, Drainage Systems</p>
          <p>High-rise residential building with advanced polyethylene components and comprehensive water management systems.</p>
        </div>

        <div class="project-item">
          <h3>Industrial Complex - Tallinn, Estonia</h3>
          <p><strong>Year:</strong> 2023</p>
          <p><strong>Products Used:</strong> Steel Products, Aluminum Profiles</p>
          <p>Large-scale industrial facility utilizing our steel components and aluminum profile systems for structural applications.</p>
        </div>

        <div class="project-item">
          <h3>Urban Infrastructure - Stockholm, Sweden</h3>
          <p><strong>Year:</strong> 2023</p>
          <p><strong>Products Used:</strong> Drainage Systems, Cast Iron Products</p>
          <p>Comprehensive urban drainage project featuring our advanced water management solutions and durable cast iron components.</p>
        </div>
      </div>
    </div>

    <div class="project-types">
      <h2>Project Categories</h2>
      <ul>
        <li><strong>Commercial Buildings:</strong> Office complexes, retail centers, mixed-use developments</li>
        <li><strong>Residential Projects:</strong> Apartment buildings, housing complexes, urban developments</li>
        <li><strong>Industrial Facilities:</strong> Manufacturing plants, warehouses, logistics centers</li>
        <li><strong>Infrastructure:</strong> Urban drainage, water management, transportation infrastructure</li>
        <li><strong>Specialized Applications:</strong> Custom solutions for unique project requirements</li>
      </ul>
    </div>`,
    meta_title: "Projects - MetaNord Infrastructure Solutions Portfolio",
    meta_description: "Explore MetaNord's successful infrastructure projects across Europe. Commercial, residential, and industrial applications of our products.",
    status: "published",
    language: "en",
    template: "projects",
    created_at: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 8,
    title: "Privacy Policy",
    slug: "privacy-policy",
    content: `<div class="legal-header">
      <h1>Privacy Policy</h1>
      <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>
    </div>

    <div class="legal-content">
      <h2>1. Information We Collect</h2>
      <p>MetaNord OÜ ("we," "our," or "us") collects information you provide directly to us, such as when you:</p>
      <ul>
        <li>Fill out contact forms or request quotes</li>
        <li>Subscribe to our newsletters or communications</li>
        <li>Participate in surveys or provide feedback</li>
        <li>Contact us for customer support</li>
      </ul>

      <h2>2. How We Use Your Information</h2>
      <p>We use the information we collect to:</p>
      <ul>
        <li>Respond to your inquiries and provide customer service</li>
        <li>Process and fulfill your orders or requests</li>
        <li>Send you technical updates, security alerts, and administrative messages</li>
        <li>Communicate with you about products, services, and promotional offers</li>
        <li>Improve our website and services</li>
      </ul>

      <h2>3. Information Sharing</h2>
      <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy or as required by law.</p>

      <h2>4. Data Security</h2>
      <p>We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

      <h2>5. Your Rights</h2>
      <p>Under GDPR, you have the right to:</p>
      <ul>
        <li>Access your personal data</li>
        <li>Correct inaccurate data</li>
        <li>Request deletion of your data</li>
        <li>Object to processing of your data</li>
        <li>Data portability</li>
      </ul>

      <h2>6. Contact Information</h2>
      <p>If you have questions about this Privacy Policy, please contact us at:</p>
      <p><strong>Email:</strong> <EMAIL><br>
      <strong>Address:</strong> MetaNord OÜ, Tallinn, Estonia</p>
    </div>`,
    meta_title: "Privacy Policy - MetaNord | Data Protection & GDPR Compliance",
    meta_description: "MetaNord's privacy policy and data protection information. Learn how we collect, use, and protect your personal information in compliance with GDPR.",
    status: "published",
    language: "en",
    template: "legal",
    created_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 9,
    title: "Terms of Service",
    slug: "terms-of-service",
    content: `<div class="legal-header">
      <h1>Terms of Service</h1>
      <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>
    </div>

    <div class="legal-content">
      <h2>1. Acceptance of Terms</h2>
      <p>By accessing and using the MetaNord website and services, you accept and agree to be bound by the terms and provision of this agreement.</p>

      <h2>2. Use License</h2>
      <p>Permission is granted to temporarily download one copy of the materials on MetaNord's website for personal, non-commercial transitory viewing only.</p>

      <h2>3. Disclaimer</h2>
      <p>The materials on MetaNord's website are provided on an 'as is' basis. MetaNord makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>

      <h2>4. Limitations</h2>
      <p>In no event shall MetaNord or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on MetaNord's website.</p>

      <h2>5. Product Information</h2>
      <p>Product specifications, availability, and pricing are subject to change without notice. We strive to provide accurate information but cannot guarantee the accuracy of all product details.</p>

      <h2>6. Governing Law</h2>
      <p>These terms and conditions are governed by and construed in accordance with the laws of Estonia and you irrevocably submit to the exclusive jurisdiction of the courts in that State or location.</p>

      <h2>7. Contact Information</h2>
      <p>Questions about the Terms of Service should be sent to <NAME_EMAIL></p>
    </div>`,
    meta_title: "Terms of Service - MetaNord | Website Terms & Conditions",
    meta_description: "MetaNord's terms of service and usage conditions. Read our website terms, conditions, and legal information.",
    status: "published",
    language: "en",
    template: "legal",
    created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 10,
    title: "Cookie Policy",
    slug: "cookie-policy",
    content: `<div class="legal-header">
      <h1>Cookie Policy</h1>
      <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>
    </div>

    <div class="legal-content">
      <h2>What Are Cookies</h2>
      <p>Cookies are small text files that are placed on your computer or mobile device when you visit our website. They are widely used to make websites work more efficiently and provide information to website owners.</p>

      <h2>How We Use Cookies</h2>
      <p>MetaNord uses cookies for the following purposes:</p>

      <h3>Essential Cookies</h3>
      <p>These cookies are necessary for the website to function properly. They enable basic functions like page navigation and access to secure areas of the website.</p>

      <h3>Analytics Cookies</h3>
      <p>We use analytics cookies to understand how visitors interact with our website. This helps us improve our website's performance and user experience.</p>

      <h3>Marketing Cookies</h3>
      <p>These cookies track your online activity to help advertisers deliver more relevant advertising or to limit how many times you see an ad.</p>

      <h2>Managing Cookies</h2>
      <p>You can control and/or delete cookies as you wish. You can delete all cookies that are already on your computer and you can set most browsers to prevent them from being placed.</p>

      <h2>Cookie Categories</h2>
      <ul>
        <li><strong>Strictly Necessary:</strong> Required for basic website functionality</li>
        <li><strong>Performance:</strong> Help us understand how visitors use our website</li>
        <li><strong>Functionality:</strong> Remember your preferences and settings</li>
        <li><strong>Marketing:</strong> Used to deliver relevant advertisements</li>
      </ul>

      <h2>Your Consent</h2>
      <p>By using our website, you consent to our use of cookies in accordance with this Cookie Policy. You can withdraw your consent at any time by adjusting your cookie preferences.</p>

      <h2>Contact Us</h2>
      <p>If you have any questions about our use of cookies, please contact <NAME_EMAIL></p>
    </div>`,
    meta_title: "Cookie Policy - MetaNord | GDPR Compliant Cookie Information",
    meta_description: "MetaNord's cookie policy explaining how we use cookies on our website. GDPR compliant information about cookie types and your choices.",
    status: "published",
    language: "en",
    template: "legal",
    created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  }
];

// GET /api/admin/pages - Fetch all pages
app.get('/api/admin/pages', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching pages');
  res.json(pages);
});

// GET /api/admin/pages/:id - Get single page for editing
app.get('/api/admin/pages/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);

  console.log(`[Admin API] Fetching page ${id}`);

  const page = pages.find(p => p.id === id);
  if (!page) {
    return res.status(404).json({ error: 'Page not found' });
  }

  res.json(page);
});

// POST /api/admin/pages - Create new page
app.post('/api/admin/pages', requireAuth, (req, res) => {
  const { title, slug, content, meta_title, meta_description, status = 'draft', language = 'en', template = 'default' } = req.body;

  console.log('[Admin API] Creating new page:', { title, slug, language });

  // Validate required fields
  if (!title || !slug) {
    return res.status(400).json({ error: 'Missing required fields: title, slug' });
  }

  // Check for duplicate slug
  const existingPage = pages.find(p => p.slug === slug);
  if (existingPage) {
    return res.status(409).json({ error: 'Page with this slug already exists' });
  }

  // Create new page
  const newPage = {
    id: Math.max(...pages.map(p => p.id), 0) + 1,
    title,
    slug,
    content: content || '',
    meta_title: meta_title || title,
    meta_description: meta_description || '',
    status,
    language,
    template,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  pages.push(newPage);
  res.json(newPage);
});

// PUT /api/admin/pages/:id - Update existing page
app.put('/api/admin/pages/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const { title, slug, content, meta_title, meta_description, status, language, template } = req.body;

  console.log(`[Admin API] Updating page ${id}:`, { title, slug, language });

  // Find page
  const pageIndex = pages.findIndex(p => p.id === id);
  if (pageIndex === -1) {
    return res.status(404).json({ error: 'Page not found' });
  }

  // Validate required fields
  if (!title || !slug) {
    return res.status(400).json({ error: 'Missing required fields: title, slug' });
  }

  // Check for duplicate slug (excluding current page)
  const existingPage = pages.find(p => p.id !== id && p.slug === slug);
  if (existingPage) {
    return res.status(409).json({ error: 'Page with this slug already exists' });
  }

  // Update page
  pages[pageIndex] = {
    ...pages[pageIndex],
    title,
    slug,
    content: content || pages[pageIndex].content,
    meta_title: meta_title || title,
    meta_description: meta_description || pages[pageIndex].meta_description,
    status: status || pages[pageIndex].status,
    language: language || pages[pageIndex].language,
    template: template || pages[pageIndex].template,
    updated_at: new Date().toISOString()
  };

  res.json(pages[pageIndex]);
});

// DELETE /api/admin/pages/:id - Delete page
app.delete('/api/admin/pages/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);

  console.log(`[Admin API] Deleting page ${id}`);

  // Find and remove page
  const pageIndex = pages.findIndex(p => p.id === id);
  if (pageIndex === -1) {
    return res.status(404).json({ error: 'Page not found' });
  }

  pages.splice(pageIndex, 1);
  res.json({ success: true, message: 'Page deleted successfully' });
});

// ===== PAGE BUILDER & TEMPLATES ENDPOINTS =====

// Mock page templates data
let pageTemplates = [
  {
    id: 1,
    name: "Landing Page",
    description: "Standard landing page template with hero section, features, and CTA",
    thumbnail: "/templates/landing.jpg",
    components: [
      {
        type: "hero",
        props: {
          title: "Welcome to MetaNord",
          subtitle: "European Infrastructure Solutions",
          backgroundImage: "/images/hero-bg.jpg",
          ctaText: "Learn More",
          ctaLink: "/about"
        }
      },
      {
        type: "features",
        props: {
          title: "Our Products",
          items: [
            { title: "Aluminum Profiles", description: "High-quality aluminum solutions", icon: "layers" },
            { title: "Steel Products", description: "Durable steel infrastructure", icon: "wrench" },
            { title: "Drainage Systems", description: "Comprehensive water management", icon: "droplets" }
          ]
        }
      },
      {
        type: "cta",
        props: {
          title: "Ready to Get Started?",
          description: "Contact us for your infrastructure needs",
          buttonText: "Contact Us",
          buttonLink: "/contact"
        }
      }
    ],
    created_at: new Date().toISOString()
  },
  {
    id: 2,
    name: "Product Showcase",
    description: "Template for showcasing products with detailed specifications",
    thumbnail: "/templates/product-showcase.jpg",
    components: [
      {
        type: "hero",
        props: {
          title: "Product Name",
          subtitle: "Product Category",
          backgroundImage: "/images/product-hero.jpg"
        }
      },
      {
        type: "product-gallery",
        props: {
          images: [],
          mainImage: "/images/product-main.jpg"
        }
      },
      {
        type: "specifications",
        props: {
          title: "Technical Specifications",
          specs: []
        }
      },
      {
        type: "applications",
        props: {
          title: "Applications",
          items: []
        }
      }
    ],
    created_at: new Date().toISOString()
  },
  {
    id: 3,
    name: "Content Page",
    description: "Simple content page template for articles and information",
    thumbnail: "/templates/content-page.jpg",
    components: [
      {
        type: "page-header",
        props: {
          title: "Page Title",
          breadcrumbs: []
        }
      },
      {
        type: "content",
        props: {
          content: "<p>Your content goes here...</p>"
        }
      },
      {
        type: "sidebar",
        props: {
          widgets: [
            { type: "related-links", title: "Related Pages", links: [] },
            { type: "contact-info", title: "Contact Information" }
          ]
        }
      }
    ],
    created_at: new Date().toISOString()
  }
];

// Mock page builder data (stores page-specific component configurations)
let pageBuilderData = {};

// GET /api/admin/page-templates - Fetch available page templates
app.get('/api/admin/page-templates', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching page templates');
  res.json(pageTemplates);
});

// GET /api/admin/page-builder/:pageId - Get page builder data for specific page
app.get('/api/admin/page-builder/:pageId', requireAuth, (req, res) => {
  const pageId = parseInt(req.params.pageId);

  console.log(`[Admin API] Fetching page builder data for page ${pageId}`);

  // Check if page exists
  const page = pages.find(p => p.id === pageId);
  if (!page) {
    return res.status(404).json({ error: 'Page not found' });
  }

  // Return page builder data or default template
  const builderData = pageBuilderData[pageId] || {
    pageId,
    template: page.template || 'default',
    components: [],
    settings: {
      layout: 'default',
      theme: 'light'
    }
  };

  res.json(builderData);
});

// POST /api/admin/page-builder - Save page builder content
app.post('/api/admin/page-builder', requireAuth, (req, res) => {
  const { pageId, template, components, settings } = req.body;

  console.log(`[Admin API] Saving page builder data for page ${pageId}`);

  // Validate required fields
  if (!pageId || !components) {
    return res.status(400).json({ error: 'Missing required fields: pageId, components' });
  }

  // Check if page exists
  const page = pages.find(p => p.id === pageId);
  if (!page) {
    return res.status(404).json({ error: 'Page not found' });
  }

  // Save page builder data
  pageBuilderData[pageId] = {
    pageId,
    template: template || 'default',
    components,
    settings: settings || { layout: 'default', theme: 'light' },
    updated_at: new Date().toISOString()
  };

  res.json(pageBuilderData[pageId]);
});

// PUT /api/admin/page-builder/:pageId - Update page builder content
app.put('/api/admin/page-builder/:pageId', requireAuth, (req, res) => {
  const pageId = parseInt(req.params.pageId);
  const { template, components, settings } = req.body;

  console.log(`[Admin API] Updating page builder data for page ${pageId}`);

  // Check if page exists
  const page = pages.find(p => p.id === pageId);
  if (!page) {
    return res.status(404).json({ error: 'Page not found' });
  }

  // Validate required fields
  if (!components) {
    return res.status(400).json({ error: 'Missing required field: components' });
  }

  // Update page builder data
  pageBuilderData[pageId] = {
    ...pageBuilderData[pageId],
    pageId,
    template: template || pageBuilderData[pageId]?.template || 'default',
    components,
    settings: settings || pageBuilderData[pageId]?.settings || { layout: 'default', theme: 'light' },
    updated_at: new Date().toISOString()
  };

  res.json(pageBuilderData[pageId]);
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
// ===== SEO MANAGEMENT ENDPOINTS =====

// Mock SEO data
let seoSettings = [
  {
    id: 1,
    page: "home",
    title: "MetaNord - European Infrastructure Solutions",
    description: "Leading supplier of aluminum profiles, steel products, and drainage systems across Europe. Quality infrastructure solutions for construction and industry.",
    keywords: "aluminum profiles, steel products, drainage systems, infrastructure, construction, Europe",
    canonical: "https://metanord.eu/",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    page: "products",
    title: "Products - MetaNord Infrastructure Solutions",
    description: "Explore our comprehensive range of aluminum profiles, polyethylene systems, steel products, and drainage solutions for European markets.",
    keywords: "aluminum profiles, polyethylene systems, steel products, cast iron, fittings, drainage, urban infrastructure",
    canonical: "https://metanord.eu/products",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 3,
    page: "contact",
    title: "Contact MetaNord - Get in Touch",
    description: "Contact MetaNord for inquiries about our infrastructure products and services. We serve clients across Europe with quality solutions.",
    keywords: "contact, inquiry, quote, MetaNord, infrastructure, Europe",
    canonical: "https://metanord.eu/contact",
    language: "en",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// GET /api/admin/seo - Fetch all SEO settings
app.get('/api/admin/seo', requireAuth, (req, res) => {
  console.log('[Admin API] Fetching SEO settings');
  res.json(seoSettings);
});

// POST /api/admin/seo - Create new SEO setting
app.post('/api/admin/seo', requireAuth, (req, res) => {
  const { page, title, description, keywords, canonical, language = 'en' } = req.body;

  console.log('[Admin API] Creating new SEO setting:', { page, title, language });

  // Validate required fields
  if (!page || !title || !description) {
    return res.status(400).json({ error: 'Missing required fields: page, title, description' });
  }

  // Check for duplicate page/language combination
  const existingSeo = seoSettings.find(s => s.page === page && s.language === language);
  if (existingSeo) {
    return res.status(409).json({ error: 'SEO settings for this page and language already exist' });
  }

  // Create new SEO setting
  const newSeo = {
    id: Math.max(...seoSettings.map(s => s.id), 0) + 1,
    page,
    title,
    description,
    keywords: keywords || '',
    canonical: canonical || '',
    language,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  seoSettings.push(newSeo);
  res.json(newSeo);
});

// PUT /api/admin/seo/:id - Update existing SEO setting
app.put('/api/admin/seo/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);
  const { page, title, description, keywords, canonical, language } = req.body;

  console.log(`[Admin API] Updating SEO setting ${id}:`, { page, title, language });

  // Find SEO setting
  const seoIndex = seoSettings.findIndex(s => s.id === id);
  if (seoIndex === -1) {
    return res.status(404).json({ error: 'SEO setting not found' });
  }

  // Validate required fields
  if (!page || !title || !description) {
    return res.status(400).json({ error: 'Missing required fields: page, title, description' });
  }

  // Check for duplicate page/language combination (excluding current item)
  const existingSeo = seoSettings.find(s =>
    s.id !== id && s.page === page && s.language === language
  );
  if (existingSeo) {
    return res.status(409).json({ error: 'SEO settings for this page and language already exist' });
  }

  // Update SEO setting
  seoSettings[seoIndex] = {
    ...seoSettings[seoIndex],
    page,
    title,
    description,
    keywords: keywords || seoSettings[seoIndex].keywords,
    canonical: canonical || seoSettings[seoIndex].canonical,
    language: language || seoSettings[seoIndex].language,
    updated_at: new Date().toISOString()
  };

  res.json(seoSettings[seoIndex]);
});

// DELETE /api/admin/seo/:id - Delete SEO setting
app.delete('/api/admin/seo/:id', requireAuth, (req, res) => {
  const id = parseInt(req.params.id);

  console.log(`[Admin API] Deleting SEO setting ${id}`);

  // Find and remove SEO setting
  const seoIndex = seoSettings.findIndex(s => s.id === id);
  if (seoIndex === -1) {
    return res.status(404).json({ error: 'SEO setting not found' });
  }

  seoSettings.splice(seoIndex, 1);
  res.json({ success: true, message: 'SEO setting deleted successfully' });
});

// 404 handler
app.use((req, res) => {
  console.log(`404 - Route not found: ${req.method} ${req.path}`);
  res.status(404).json({ error: 'Route not found' });
});

const server = app.listen(PORT, () => {
  console.log(`Backend server listening on http://localhost:${PORT}`);
  console.log(`CORS enabled for: http://localhost:5173, http://localhost:5174`);
  console.log(`Loaded ${products.length} products and ${projects.length} projects`);
});

// Keep the server running
server.on('error', (error) => {
  console.error('Server error:', error);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
