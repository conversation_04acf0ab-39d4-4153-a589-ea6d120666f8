# MetaNord Address Update Summary

## Overview
Successfully updated the MetaNord company address from the old format to the new standardized format across all components and pages of the MetaNord frontend website.

**Old Address Format:** `Tornimäe tn 5, 10145 Tallinn, Estonia`
**New Address Format:** `Tornimäe 5, Tallinn, 10145, Estonia`

## Files Updated

### 1. Translation Files (Multi-language Support)
Updated address in all 7 supported languages:

#### English (`client/src/locales/en.json`)
- Line 830: `contact.info.address`

#### Estonian (`client/src/locales/et.json`)
- Line 500: `contact.info.address`

#### Russian (`client/src/locales/ru.json`)
- Line 431: `contact.info.address`

#### Lithuanian (`client/src/locales/lt.json`)
- Line 200: `contact.info.address`

#### Latvian (`client/src/locales/lv.json`)
- Line 144: `contact.info.address`

#### Polish (`client/src/locales/pl.json`)
- Line 162: `contact.info.address`

#### Chinese Simplified (`client/src/locales/zh-CN.json`)
- Line 85: `contact.info.address`

### 2. Contact Page Components
#### Contact Page (`client/src/pages/Contact.tsx`)
- Line 30: Updated fallback address in contactInfo array
- Line 141: Updated Google Maps embed URL
- Line 148: Updated iframe title

#### Contact Section (`client/src/components/sections/ContactSection.tsx`)
- Line 22: Updated fallback address in contactInfo array
- Line 93: Updated Google Maps embed URL
- Line 100: Updated iframe title

### 3. Map Components
#### Map Component (`client/src/components/page-components/Map.tsx`)
- Line 12: Updated default address parameter

### 4. SEO and Structured Data
#### Schema.org Component (`client/src/components/seo/SchemaOrg.tsx`)
- Line 13: Updated streetAddress in COMPANY_DATA (removed "tn")

#### Sitemap Component (`client/src/components/seo/Sitemap.tsx`)
- Line 50: Updated streetAddress in organization schema (removed "tn")

### 5. Layout Components
#### Footer Component (`client/src/components/layout/Footer.tsx`)
- Line 56: Updated address display format

#### Mobile Menu (`client/src/components/layout/MobileMenu.tsx`)
- Line 448: Updated Google Maps link URL

### 6. Career Pages
#### Careers Page (`client/src/pages/Careers.tsx`)
- Line 930: Updated Google Maps embed URL
- Line 937: Updated iframe title

### 7. Configuration Files
#### Contact Info Constants (`client/src/constants/contactInfo.ts`)
- Line 5: Updated Google Maps link from shortened URL to direct URL with new address

### 8. Admin Dashboard Components
#### Page Builder Properties (`client/src/components/admin/page-builder/PageBuilderProperties.tsx`)
- Line 1228: Updated placeholder text for map address input

#### Page Builder Properties Backup (`client/src/components/admin/page-builder/PageBuilderProperties.backup.tsx`)
- Line 1217: Updated placeholder text for map address input

#### Settings Manager (`client/src/components/admin/SettingsManager.tsx`)
- Line 184: Updated default address in site settings form

## Address Format Changes by Language

| Language | Old Format | New Format |
|----------|------------|------------|
| English | Tornimäe tn 5, 10145 Tallinn, Estonia | Tornimäe 5, Tallinn, 10145, Estonia |
| Estonian | Tornimäe tn 5, 10145 Tallinn, Eesti | Tornimäe 5, Tallinn, 10145, Eesti |
| Russian | Торнимяэ 5, 10145 Таллинн, Эстония | Торнимяэ 5, Таллинн, 10145, Эстония |
| Lithuanian | Tornimäe tn 5, 10145 Talinas, Estija | Tornimäe 5, Talinas, 10145, Estija |
| Latvian | Tornimäe tn 5, 10145 Tallina, Igaunija | Tornimäe 5, Tallina, 10145, Igaunija |
| Polish | Tornimäe tn 5, 10145 Tallinn, Estonia | Tornimäe 5, Tallinn, 10145, Estonia |
| Chinese | Tornimäe tn 5, 10145 Tallinn, 爱沙尼亚 | Tornimäe 5, Tallinn, 10145, 爱沙尼亚 |

## Google Maps URLs Updated

### Old URL Format:
```
https://maps.google.com/maps?q=Tornimäe%20tn%205,%2010145%20Tallinn,%20Estonia
```

### New URL Format:
```
https://maps.google.com/maps?q=Tornimäe%205,%20Tallinn,%2010145,%20Estonia
```

## Structured Data Updates

### Schema.org Organization Data:
- Updated `streetAddress` from "Tornimäe tn 5" to "Tornimäe 5"
- Maintained proper postal address structure with:
  - `addressLocality`: "Tallinn"
  - `postalCode`: "10145"
  - `addressCountry`: "Estonia" / "EE"

## Components Affected

### Frontend Pages:
- Contact page
- Careers page
- Footer (all pages)
- Mobile menu

### Admin Dashboard:
- Page builder map component properties
- Site settings manager
- Default address configurations

### SEO Components:
- Meta tags and structured data
- Sitemap organization schema
- Open Graph data

## Verification Checklist

✅ **Translation Files**: All 7 languages updated
✅ **Contact Components**: Both contact page and section updated
✅ **Map Components**: Default address and Google Maps URLs updated
✅ **SEO Data**: Schema.org and structured data updated
✅ **Layout Components**: Footer and mobile menu updated
✅ **Admin Components**: Page builder and settings updated
✅ **Career Pages**: Maps and location references updated
✅ **Configuration Files**: Constants and default values updated

## Notes

1. **Job Locations**: Career job listings maintain "Tallinn, Estonia" format which is appropriate for job location display
2. **Consistency**: All address formats now follow the standardized "Tornimäe 5, Tallinn, 10145, Estonia" pattern
3. **SEO Compliance**: Structured data maintains proper postal address schema
4. **Multi-language**: Address translations preserve local language conventions while updating the format
5. **Google Maps**: All embedded maps and links point to the correct new address location

## Impact

- **User Experience**: Consistent address display across all touchpoints
- **SEO**: Improved local search optimization with standardized address format
- **Maps Integration**: Accurate location display in all Google Maps integrations
- **Admin Interface**: Updated default values for new content creation
- **Multi-language**: Consistent address format across all supported languages

All address references have been successfully updated to the new format: **Tornimäe 5, Tallinn, 10145, Estonia**
