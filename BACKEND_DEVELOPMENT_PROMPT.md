# MetaNord Backend Development Prompt for Augment

## 🎯 MISSION
Complete the MetaNord admin dashboard backend by implementing missing API endpoints and database integration. The frontend is fully functional and waiting for these backend endpoints.

## 📁 PROJECT CONTEXT
- **Repository**: MetaNord Backend (separate from frontend)
- **Current Backend**: `backend-server.js` with basic Express.js setup
- **Database**: PostgreSQL (needs to be set up)
- **Deployment**: Railway platform
- **Frontend URL**: https://metanord-frontend.vercel.app
- **Admin Dashboard**: https://metanord-frontend.vercel.app/admin

## 🚨 CRITICAL ISSUES TO FIX

### 1. **Site Content Management - BROKEN TAB**
**Problem**: Site Content tab shows blank page
**Root Cause**: Missing backend endpoints

**Required Implementation**:
```javascript
// POST /api/admin/content
app.post('/api/admin/content', requireAuth, async (req, res) => {
  const { section, key, value, language } = req.body;
  
  // Validate required fields
  if (!section || !key || !value || !language) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  
  try {
    // Insert or update content in database
    const result = await db.query(`
      INSERT INTO site_content (section, key, value, language, updated_at)
      VALUES ($1, $2, $3, $4, NOW())
      ON CONFLICT (section, key, language)
      DO UPDATE SET value = $3, updated_at = NOW()
      RETURNING *
    `, [section, key, value, language]);
    
    res.json({ success: true, content: result.rows[0] });
  } catch (error) {
    res.status(500).json({ error: 'Database error' });
  }
});

// GET /api/admin/content
app.get('/api/admin/content', requireAuth, async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM site_content ORDER BY section, key, language');
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Database error' });
  }
});

// PUT /api/admin/content/:id
app.put('/api/admin/content/:id', requireAuth, async (req, res) => {
  const { id } = req.params;
  const { section, key, value, language } = req.body;
  
  try {
    const result = await db.query(`
      UPDATE site_content 
      SET section = $1, key = $2, value = $3, language = $4, updated_at = NOW()
      WHERE id = $5
      RETURNING *
    `, [section, key, value, language, id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Content not found' });
    }
    
    res.json({ success: true, content: result.rows[0] });
  } catch (error) {
    res.status(500).json({ error: 'Database error' });
  }
});

// DELETE /api/admin/content/:id
app.delete('/api/admin/content/:id', requireAuth, async (req, res) => {
  const { id } = req.params;
  
  try {
    const result = await db.query('DELETE FROM site_content WHERE id = $1 RETURNING *', [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Content not found' });
    }
    
    res.json({ success: true, message: 'Content deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Database error' });
  }
});
```

### 2. **Enhanced User Management - MISSING CRUD**
**Problem**: User management needs full CRUD + invitation system

**Required Implementation**:
```javascript
// POST /api/admin/users (enhance existing)
app.post('/api/admin/users', requireAuth, requireRole(['admin']), async (req, res) => {
  const { username, password, fullName, email, role } = req.body;
  
  // Validate and hash password
  const hashedPassword = await bcrypt.hash(password, 10);
  
  try {
    const result = await db.query(`
      INSERT INTO users (username, password, full_name, email, role, created_at)
      VALUES ($1, $2, $3, $4, $5, NOW())
      RETURNING id, username, full_name, email, role, created_at
    `, [username, hashedPassword, fullName, email, role]);
    
    res.json({ success: true, user: result.rows[0] });
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      res.status(400).json({ error: 'Username or email already exists' });
    } else {
      res.status(500).json({ error: 'Database error' });
    }
  }
});

// PATCH /api/admin/users/:id
app.patch('/api/admin/users/:id', requireAuth, requireRole(['admin']), async (req, res) => {
  const { id } = req.params;
  const { fullName, email, role, password } = req.body;
  
  const updates = [];
  const values = [];
  let paramCount = 1;
  
  if (fullName !== undefined) {
    updates.push(`full_name = $${paramCount++}`);
    values.push(fullName);
  }
  if (email !== undefined) {
    updates.push(`email = $${paramCount++}`);
    values.push(email);
  }
  if (role !== undefined) {
    updates.push(`role = $${paramCount++}`);
    values.push(role);
  }
  if (password) {
    const hashedPassword = await bcrypt.hash(password, 10);
    updates.push(`password = $${paramCount++}`);
    values.push(hashedPassword);
  }
  
  if (updates.length === 0) {
    return res.status(400).json({ error: 'No fields to update' });
  }
  
  updates.push(`updated_at = NOW()`);
  values.push(id);
  
  try {
    const result = await db.query(`
      UPDATE users 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, username, full_name, email, role, last_login, created_at
    `, values);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json({ success: true, user: result.rows[0] });
  } catch (error) {
    res.status(500).json({ error: 'Database error' });
  }
});

// DELETE /api/admin/users/:id
app.delete('/api/admin/users/:id', requireAuth, requireRole(['admin']), async (req, res) => {
  const { id } = req.params;
  
  // Prevent deletion of main admin
  const user = await db.query('SELECT username FROM users WHERE id = $1', [id]);
  if (user.rows[0]?.username === 'admin') {
    return res.status(403).json({ error: 'Cannot delete main admin user' });
  }
  
  try {
    const result = await db.query('DELETE FROM users WHERE id = $1 RETURNING username', [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Database error' });
  }
});

// POST /api/admin/users/invite
app.post('/api/admin/users/invite', requireAuth, requireRole(['admin']), async (req, res) => {
  const { email, role, fullName } = req.body;
  const invitedBy = req.user.id;
  
  // Generate unique token
  const token = crypto.randomBytes(32).toString('hex');
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
  
  try {
    // Check if user already exists
    const existingUser = await db.query('SELECT id FROM users WHERE email = $1', [email]);
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'User with this email already exists' });
    }
    
    // Create invitation
    const result = await db.query(`
      INSERT INTO user_invitations (email, full_name, role, token, invited_by, expires_at)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [email, fullName, role, token, invitedBy, expiresAt]);
    
    // Send invitation email
    await emailService.sendInvitation(email, token, req.user.fullName);
    
    res.json({ success: true, invitation: result.rows[0] });
  } catch (error) {
    res.status(500).json({ error: 'Failed to send invitation' });
  }
});
```

### 3. **Database Setup**
**Required Database Schema**:
```sql
-- Site Content Table
CREATE TABLE site_content (
  id SERIAL PRIMARY KEY,
  section VARCHAR(50) NOT NULL,
  key VARCHAR(100) NOT NULL,
  value TEXT NOT NULL,
  language VARCHAR(5) NOT NULL DEFAULT 'en',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(section, key, language)
);

-- Enhanced Users Table
ALTER TABLE users ADD COLUMN IF NOT EXISTS full_name VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active';

-- User Invitations Table
CREATE TABLE user_invitations (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  full_name VARCHAR(255),
  role VARCHAR(20) NOT NULL,
  token VARCHAR(255) NOT NULL UNIQUE,
  invited_by INTEGER REFERENCES users(id),
  expires_at TIMESTAMP NOT NULL,
  accepted_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Contact Inquiries Table (replace mock data)
CREATE TABLE contact_inquiries (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  company VARCHAR(255),
  phone VARCHAR(50),
  subject VARCHAR(255),
  message TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'new',
  archived BOOLEAN DEFAULT FALSE,
  inquiry_type VARCHAR(50) DEFAULT 'general',
  product_interest VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Notifications Table
CREATE TABLE notifications (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(20) NOT NULL,
  read BOOLEAN DEFAULT FALSE,
  resource_type VARCHAR(50),
  resource_id INTEGER,
  action_text VARCHAR(100),
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 IMPLEMENTATION STEPS

### Step 1: Database Setup
1. Set up PostgreSQL database on Railway
2. Create all required tables using the schema above
3. Insert sample data for testing

### Step 2: Content Management
1. Implement all `/api/admin/content` endpoints
2. Test with frontend Site Content tab
3. Verify CRUD operations work correctly

### Step 3: User Management Enhancement
1. Enhance existing user endpoints
2. Implement invitation system
3. Add role-based permissions middleware

### Step 4: Real Data Integration
1. Replace mock data with database queries
2. Update contact form to save to database
3. Implement inquiry status management

### Step 5: Testing & Deployment
1. Test all endpoints with frontend
2. Deploy to Railway
3. Update frontend API URL if needed

## 🎯 SUCCESS CRITERIA

### ✅ Site Content Tab Working:
- Can view existing content
- Can create new content items
- Can edit existing content
- Can delete content items
- Preview functionality works

### ✅ User Management Complete:
- Full CRUD operations
- User invitation system
- Role-based permissions
- Search and filtering

### ✅ Real Data Integration:
- No more mock data
- Contact inquiries saved to database
- Notifications system functional

## 🚀 DEPLOYMENT NOTES

### Environment Variables:
```
DATABASE_URL=postgresql://...
JWT_SECRET=your-secret-key
EMAIL_SERVICE_API_KEY=your-email-key
FRONTEND_URL=https://metanord-frontend.vercel.app
```

### CORS Configuration:
```javascript
app.use(cors({
  origin: ['https://metanord-frontend.vercel.app', 'http://localhost:5173'],
  credentials: true
}));
```

This implementation will fix all broken admin dashboard tabs and provide complete functionality for the MetaNord admin system.
