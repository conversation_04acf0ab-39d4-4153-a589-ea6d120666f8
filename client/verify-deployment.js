#!/usr/bin/env node

/**
 * Deployment Verification Script for MetaNord Frontend
 * Verifies that the production deployment is working correctly
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

const PRODUCTION_URL = process.env.PRODUCTION_URL || 'https://metanord-frontend.vercel.app';
const API_URL = 'https://api.metanord.eu';

console.log('🚀 MetaNord Frontend Deployment Verification');
console.log('===========================================');
console.log(`Production URL: ${PRODUCTION_URL}`);
console.log(`API URL: ${API_URL}\n`);

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testFrontendDeployment() {
  console.log('🔍 Testing Frontend Deployment...');
  
  try {
    const response = await makeRequest(PRODUCTION_URL);
    
    if (response.statusCode === 200) {
      console.log('✅ Frontend is accessible');
      
      // Check if the HTML contains the correct API URL
      if (response.data.includes('api.metanord.eu')) {
        console.log('✅ API URL is correctly embedded in build');
      } else if (response.data.includes('localhost:3001')) {
        console.log('❌ Build contains localhost API URL - needs rebuild');
        return false;
      } else {
        console.log('⚠️  Cannot verify API URL in HTML');
      }
      
      return true;
    } else {
      console.log(`❌ Frontend not accessible: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Frontend test failed: ${error.message}`);
    return false;
  }
}

async function testCareersPage() {
  console.log('\n🔍 Testing Careers Page...');
  
  try {
    const response = await makeRequest(`${PRODUCTION_URL}/careers`);
    
    if (response.statusCode === 200) {
      console.log('✅ Careers page is accessible');
      
      // Check for error indicators in the HTML
      if (response.data.includes('something went wrong')) {
        console.log('❌ Careers page shows error message');
        return false;
      } else {
        console.log('✅ Careers page loads without error message');
      }
      
      return true;
    } else {
      console.log(`❌ Careers page not accessible: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Careers page test failed: ${error.message}`);
    return false;
  }
}

async function testAPIConnectivity() {
  console.log('\n🔍 Testing API Connectivity...');
  
  try {
    const response = await makeRequest(`${API_URL}/api/careers`, {
      headers: {
        'Origin': PRODUCTION_URL,
        'Accept': 'application/json'
      }
    });
    
    if (response.statusCode === 200) {
      console.log('✅ API is accessible from production domain');
      
      try {
        const data = JSON.parse(response.data);
        console.log(`✅ API returns ${data.length} job postings`);
        return true;
      } catch (e) {
        console.log('❌ API response is not valid JSON');
        return false;
      }
    } else {
      console.log(`❌ API not accessible: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ API test failed: ${error.message}`);
    return false;
  }
}

function checkBuildConfiguration() {
  console.log('\n🔍 Checking Build Configuration...');
  
  const envProdPath = path.join(__dirname, '.env.production');
  const packageJsonPath = path.join(__dirname, 'package.json');
  
  // Check .env.production
  if (fs.existsSync(envProdPath)) {
    const envContent = fs.readFileSync(envProdPath, 'utf8');
    if (envContent.includes('VITE_API_URL=https://api.metanord.eu')) {
      console.log('✅ .env.production has correct API URL');
    } else {
      console.log('❌ .env.production missing or incorrect API URL');
      return false;
    }
  } else {
    console.log('❌ .env.production file not found');
    return false;
  }
  
  // Check package.json build script
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    if (packageJson.scripts && packageJson.scripts.build) {
      console.log('✅ Build script exists');
    } else {
      console.log('❌ Build script not found');
      return false;
    }
  }
  
  return true;
}

function generateFixInstructions(results) {
  console.log('\n🛠️  Fix Instructions:');
  console.log('====================');
  
  if (!results.buildConfig) {
    console.log('1. Fix build configuration:');
    console.log('   - Ensure .env.production contains: VITE_API_URL=https://api.metanord.eu');
    console.log('   - Run: npm run build');
  }
  
  if (!results.api) {
    console.log('2. Fix API connectivity:');
    console.log('   - Check backend deployment status');
    console.log('   - Verify CORS configuration');
    console.log('   - Test API manually: curl https://api.metanord.eu/api/careers');
  }
  
  if (!results.frontend) {
    console.log('3. Fix frontend deployment:');
    console.log('   - Rebuild with correct environment variables');
    console.log('   - Redeploy to Vercel/Netlify');
    console.log('   - Verify deployment includes production build');
  }
  
  if (!results.careers) {
    console.log('4. Fix careers page:');
    console.log('   - Check browser console for errors');
    console.log('   - Verify API calls are using correct URL');
    console.log('   - Test error boundary functionality');
  }
  
  console.log('\n📋 Quick Fix Commands:');
  console.log('cd client');
  console.log('npm run build');
  console.log('vercel --prod  # or your deployment command');
}

async function runVerification() {
  console.log('Starting verification tests...\n');
  
  const results = {
    buildConfig: checkBuildConfiguration(),
    api: await testAPIConnectivity(),
    frontend: await testFrontendDeployment(),
    careers: await testCareersPage()
  };
  
  const allPassed = Object.values(results).every(result => result === true);
  
  console.log('\n🎯 Verification Summary:');
  console.log('========================');
  console.log(`Build Configuration: ${results.buildConfig ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`API Connectivity: ${results.api ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Frontend Deployment: ${results.frontend ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Careers Page: ${results.careers ? '✅ PASS' : '❌ FAIL'}`);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! Deployment is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. See fix instructions below.');
    generateFixInstructions(results);
  }
  
  return allPassed;
}

// Run verification
runVerification()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Verification failed:', error);
    process.exit(1);
  });
