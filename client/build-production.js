#!/usr/bin/env node

// Production build script for MetaNord frontend
const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 Starting MetaNord production build...');

// Ensure clean build environment
if (fs.existsSync('dist')) {
  console.log('📁 Cleaning existing dist directory...');
  fs.rmSync('dist', { recursive: true, force: true });
}

// Set production environment
process.env.NODE_ENV = 'production';
process.env.VITE_API_URL = process.env.VITE_API_URL || 'https://api.metanord.eu';

console.log('⚙️  Building with Vite...');
try {
  execSync('npx vite build --outDir dist', { 
    stdio: 'inherit',
    env: { ...process.env }
  });
  
  console.log('✅ Build completed successfully!');
  console.log('📦 Static files ready in dist/ directory');
  console.log('🌐 API configured for: https://api.metanord.eu');
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}