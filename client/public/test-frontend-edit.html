<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Product Edit Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Product Edit Functionality Test</h1>
        <p>This page tests the product edit functionality that was fixed in the admin dashboard.</p>

        <div id="status" class="test-section info">
            <h3>Status</h3>
            <p>Ready to test...</p>
        </div>

        <div class="test-section">
            <h3>1. Fetch Products</h3>
            <button onclick="fetchProducts()">Fetch All Products</button>
            <div id="products-result"></div>
        </div>

        <div class="test-section">
            <h3>2. Select Product to Edit</h3>
            <select id="product-select" onchange="selectProduct()">
                <option value="">Select a product...</option>
            </select>
            <div id="selected-product"></div>
        </div>

        <div class="test-section">
            <h3>3. Simulate Edit Form Population</h3>
            <button onclick="simulateEdit()" disabled id="edit-btn">Simulate Edit Modal</button>
            <div id="edit-form" style="display: none;">
                <h4>Edit Product Form (Simulated)</h4>
                <div class="form-group">
                    <label>Product ID:</label>
                    <input type="text" id="form-productId" readonly>
                </div>
                <div class="form-group">
                    <label>Title:</label>
                    <input type="text" id="form-title">
                </div>
                <div class="form-group">
                    <label>Description:</label>
                    <textarea id="form-description"></textarea>
                </div>
                <div class="form-group">
                    <label>Category:</label>
                    <input type="text" id="form-category">
                </div>
                <div class="form-group">
                    <label>Status:</label>
                    <input type="text" id="form-status">
                </div>
                <div class="form-group">
                    <label>Features:</label>
                    <textarea id="form-features" readonly></textarea>
                </div>
                <div class="form-group">
                    <label>Applications:</label>
                    <textarea id="form-applications" readonly></textarea>
                </div>
                <div class="form-group">
                    <label>Specifications:</label>
                    <textarea id="form-specifications" readonly></textarea>
                </div>
                <button onclick="testUpdate()">Test Update</button>
            </div>
        </div>

        <div id="test-results" class="test-section" style="display: none;">
            <h3>Test Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';

        // Add CORS headers for testing
        const fetchWithCORS = async (url, options = {}) => {
            const response = await fetch(url, {
                ...options,
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });
            return response;
        };
        let products = [];
        let selectedProduct = null;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `test-section ${type}`;
            statusDiv.innerHTML = `<h3>Status</h3><p>${message}</p>`;
        }

        async function fetchProducts() {
            try {
                updateStatus('Fetching products...', 'info');
                const response = await fetchWithCORS(`${API_BASE}/api/admin/products?language=en`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                products = await response.json();
                
                const select = document.getElementById('product-select');
                select.innerHTML = '<option value="">Select a product...</option>';
                
                products.forEach((product, index) => {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = `${product.productId} - ${product.title}`;
                    select.appendChild(option);
                });
                
                document.getElementById('products-result').innerHTML = 
                    `<div class="success">✅ Successfully fetched ${products.length} products</div>`;
                
                updateStatus(`Found ${products.length} products. Select one to test edit functionality.`, 'success');
                
            } catch (error) {
                document.getElementById('products-result').innerHTML = 
                    `<div class="error">❌ Error: ${error.message}</div>`;
                updateStatus(`Error fetching products: ${error.message}`, 'error');
            }
        }

        function selectProduct() {
            const select = document.getElementById('product-select');
            const index = select.value;
            
            if (index === '') {
                selectedProduct = null;
                document.getElementById('selected-product').innerHTML = '';
                document.getElementById('edit-btn').disabled = true;
                return;
            }
            
            selectedProduct = products[index];
            document.getElementById('edit-btn').disabled = false;
            
            document.getElementById('selected-product').innerHTML = `
                <div class="info">
                    <h4>Selected Product: ${selectedProduct.title}</h4>
                    <pre>${JSON.stringify(selectedProduct, null, 2)}</pre>
                </div>
            `;
        }

        async function simulateEdit() {
            if (!selectedProduct) return;
            
            try {
                updateStatus('Fetching product data for edit...', 'info');
                
                // Simulate the exact API call that the edit modal makes
                const response = await fetchWithCORS(`${API_BASE}/api/admin/products/${selectedProduct.productId}?language=en`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const productData = await response.json();
                
                // Populate the form fields (simulating what the edit modal should do)
                document.getElementById('form-productId').value = productData.productId || '';
                document.getElementById('form-title').value = productData.title || '';
                document.getElementById('form-description').value = productData.description || '';
                document.getElementById('form-category').value = productData.category || '';
                document.getElementById('form-status').value = productData.status || '';
                document.getElementById('form-features').value = JSON.stringify(productData.features || [], null, 2);
                document.getElementById('form-applications').value = JSON.stringify(productData.applications || [], null, 2);
                document.getElementById('form-specifications').value = JSON.stringify(productData.specifications || {}, null, 2);
                
                document.getElementById('edit-form').style.display = 'block';
                
                updateStatus('✅ Edit form populated successfully! This simulates what should happen in the admin dashboard.', 'success');
                
            } catch (error) {
                updateStatus(`Error simulating edit: ${error.message}`, 'error');
            }
        }

        async function testUpdate() {
            try {
                updateStatus('Testing product update...', 'info');
                
                const formData = new FormData();
                formData.append('productId', document.getElementById('form-productId').value);
                formData.append('title', document.getElementById('form-title').value + ' (Test)');
                formData.append('description', document.getElementById('form-description').value);
                formData.append('category', document.getElementById('form-category').value);
                formData.append('status', document.getElementById('form-status').value);
                formData.append('language', 'en');
                
                const response = await fetch(`${API_BASE}/api/admin/products/${selectedProduct.productId}`, {
                    method: 'PUT',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                updateStatus('✅ Product update test successful!', 'success');
                
                // Show test results
                document.getElementById('test-results').style.display = 'block';
                document.getElementById('results-content').innerHTML = `
                    <div class="success">
                        <h4>🎉 All Tests Passed!</h4>
                        <ul>
                            <li>✅ Products fetch working</li>
                            <li>✅ Individual product fetch working</li>
                            <li>✅ Form population working</li>
                            <li>✅ Product update working</li>
                        </ul>
                        <p><strong>The edit functionality fix is working correctly!</strong></p>
                        <p>The admin dashboard should now properly populate the edit form with existing product data.</p>
                    </div>
                `;
                
            } catch (error) {
                updateStatus(`Error testing update: ${error.message}`, 'error');
            }
        }

        // Auto-start the test
        window.onload = function() {
            updateStatus('Click "Fetch All Products" to begin testing...', 'info');
        };
    </script>
</body>
</html>
