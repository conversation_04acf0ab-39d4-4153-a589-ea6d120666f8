{"name": "metanord-frontend", "version": "1.0.0", "scripts": {"dev": "./node_modules/.bin/vite --host 0.0.0.0", "build": "./node_modules/.bin/vite build", "preview": "./node_modules/.bin/vite preview", "test": "./node_modules/.bin/vitest"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.0.0", "@radix-ui/react-aspect-ratio": "^1.0.0", "@radix-ui/react-avatar": "^1.0.0", "@radix-ui/react-checkbox": "^1.0.0", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^1.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.0.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.0.0", "@radix-ui/react-navigation-menu": "^1.0.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.0.0", "@radix-ui/react-radio-group": "^1.0.0", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.0.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.0.0", "@radix-ui/react-toggle-group": "^1.0.0", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.27.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.0.0", "flag-icons": "^7.3.2", "framer-motion": "^12.12.2", "html2canvas": "^1.4.1", "i18next": "^23.10.1", "i18next-browser-languagedetector": "^8.1.0", "input-otp": "^1.0.0", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "node-fetch": "^2.7.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.0", "react-color": "^2.19.3", "react-countup": "^6.5.3", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-dropzone": "^14.0.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "react-resizable-panels": "^1.0.0", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "wouter": "^3.7.0", "zod": "^3.25.8"}, "devDependencies": {"@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "jsdom": "^24.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "terser": "^5.40.0", "typescript": "^5.4.3", "vite": "^4.5.2", "vitest": "^1.0.0"}}