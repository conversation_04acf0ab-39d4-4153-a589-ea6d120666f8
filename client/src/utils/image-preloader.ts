/**
 * Image Preloader Utility
 * 
 * Preloads images in the background to prevent flickering during filter changes
 */

// Global cache to track preloaded images
const preloadedImages = new Set<string>();

/**
 * Preload a single image
 */
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    // Skip if already preloaded
    if (preloadedImages.has(src)) {
      resolve();
      return;
    }

    const img = new Image();
    
    img.onload = () => {
      preloadedImages.add(src);
      console.log(`[ImagePreloader] Successfully preloaded: ${src}`);
      resolve();
    };
    
    img.onerror = () => {
      console.warn(`[ImagePreloader] Failed to preload: ${src}`);
      reject(new Error(`Failed to preload image: ${src}`));
    };
    
    img.src = src;
  });
}

/**
 * Preload multiple images with concurrency control
 */
export function preloadImages(sources: string[], maxConcurrent = 3): Promise<void[]> {
  const chunks: string[][] = [];
  
  // Split into chunks for controlled concurrency
  for (let i = 0; i < sources.length; i += maxConcurrent) {
    chunks.push(sources.slice(i, i + maxConcurrent));
  }
  
  // Process chunks sequentially, but images within each chunk concurrently
  return chunks.reduce(async (promise, chunk) => {
    await promise;
    return Promise.allSettled(chunk.map(preloadImage));
  }, Promise.resolve([]));
}

/**
 * Check if an image is already preloaded
 */
export function isImagePreloaded(src: string): boolean {
  return preloadedImages.has(src);
}

/**
 * Preload product images based on current visible products
 */
export function preloadProductImages(products: any[]): void {
  const imageSources = products
    .map(product => product.image)
    .filter(Boolean)
    .slice(0, 12); // Limit to first 12 images to avoid overwhelming the browser
  
  if (imageSources.length > 0) {
    console.log(`[ImagePreloader] Starting preload of ${imageSources.length} product images`);
    preloadImages(imageSources, 2); // Lower concurrency for product images
  }
}
