/**
 * Analytics Manager
 * Conditionally loads and manages analytics scripts based on cookie consent
 */

import { canUseAnalytics, canUseMarketing } from './cookie-storage';

// Track if analytics has been initialized
let analyticsInitialized = false;
let marketingInitialized = false;

// Analytics configuration
const ANALYTICS_CONFIG = {
  gtmId: import.meta.env.VITE_GTM_ID || '',
  gaId: import.meta.env.VITE_GA_ID || '',
};

/**
 * Initialize Google Tag Manager if consent is given
 */
export const initializeGTM = (): void => {
  if (!canUseAnalytics() || analyticsInitialized || !ANALYTICS_CONFIG.gtmId) {
    return;
  }

  try {
    // Create GTM script
    const script = document.createElement('script');
    script.innerHTML = `
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','${ANALYTICS_CONFIG.gtmId}');
    `;
    document.head.appendChild(script);

    // Create noscript fallback
    const noscript = document.createElement('noscript');
    noscript.innerHTML = `
      <iframe src="https://www.googletagmanager.com/ns.html?id=${ANALYTICS_CONFIG.gtmId}"
      height="0" width="0" style="display:none;visibility:hidden"></iframe>
    `;
    document.body.appendChild(noscript);

    analyticsInitialized = true;
    
    if (import.meta.env.DEV) {
      console.log('Google Tag Manager initialized');
    }
  } catch (error) {
    console.error('Error initializing Google Tag Manager:', error);
  }
};

/**
 * Initialize Google Analytics if consent is given
 */
export const initializeGA = (): void => {
  if (!canUseAnalytics() || analyticsInitialized || !ANALYTICS_CONFIG.gaId) {
    return;
  }

  try {
    // Create GA script
    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${ANALYTICS_CONFIG.gaId}`;
    document.head.appendChild(script1);

    // Create GA configuration script
    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${ANALYTICS_CONFIG.gaId}');
    `;
    document.head.appendChild(script2);

    analyticsInitialized = true;
    
    if (import.meta.env.DEV) {
      console.log('Google Analytics initialized');
    }
  } catch (error) {
    console.error('Error initializing Google Analytics:', error);
  }
};

/**
 * Track an event (only if analytics consent is given)
 */
export const trackEvent = (eventName: string, eventParams?: Record<string, any>): void => {
  if (!canUseAnalytics()) {
    if (import.meta.env.DEV) {
      console.log('Analytics tracking blocked - no consent:', eventName, eventParams);
    }
    return;
  }

  try {
    // Check if dataLayer exists (GTM/GA)
    if (typeof window !== 'undefined' && (window as any).dataLayer) {
      (window as any).dataLayer.push({
        event: eventName,
        ...eventParams,
      });
      
      if (import.meta.env.DEV) {
        console.log('Analytics event tracked:', eventName, eventParams);
      }
    } else if (import.meta.env.DEV) {
      console.warn('dataLayer not found. Analytics event not tracked:', eventName);
    }
  } catch (error) {
    console.error('Error tracking analytics event:', error);
  }
};

/**
 * Track page view (only if analytics consent is given)
 */
export const trackPageView = (path: string, title?: string): void => {
  if (!canUseAnalytics()) {
    return;
  }

  trackEvent('page_view', {
    page_path: path,
    page_title: title || document.title,
  });
};

/**
 * Initialize marketing scripts if consent is given
 */
export const initializeMarketing = (): void => {
  if (!canUseMarketing() || marketingInitialized) {
    return;
  }

  try {
    // Add marketing scripts here when needed
    // Example: Facebook Pixel, LinkedIn Insight Tag, etc.
    
    marketingInitialized = true;
    
    if (import.meta.env.DEV) {
      console.log('Marketing scripts initialized');
    }
  } catch (error) {
    console.error('Error initializing marketing scripts:', error);
  }
};

/**
 * Remove analytics scripts if consent is withdrawn
 */
export const removeAnalyticsScripts = (): void => {
  try {
    // Remove GTM scripts
    const gtmScripts = document.querySelectorAll('script[src*="googletagmanager.com"]');
    gtmScripts.forEach(script => script.remove());

    // Remove GA scripts
    const gaScripts = document.querySelectorAll('script[src*="google-analytics.com"], script[src*="googletagmanager.com/gtag"]');
    gaScripts.forEach(script => script.remove());

    // Clear dataLayer
    if (typeof window !== 'undefined') {
      (window as any).dataLayer = [];
    }

    analyticsInitialized = false;
    
    if (import.meta.env.DEV) {
      console.log('Analytics scripts removed');
    }
  } catch (error) {
    console.error('Error removing analytics scripts:', error);
  }
};

/**
 * Remove marketing scripts if consent is withdrawn
 */
export const removeMarketingScripts = (): void => {
  try {
    // Remove marketing scripts here when needed
    
    marketingInitialized = false;
    
    if (import.meta.env.DEV) {
      console.log('Marketing scripts removed');
    }
  } catch (error) {
    console.error('Error removing marketing scripts:', error);
  }
};

/**
 * Initialize all analytics based on current consent
 */
export const initializeAnalytics = (): void => {
  if (canUseAnalytics()) {
    // Initialize GTM if available, otherwise fall back to GA
    if (ANALYTICS_CONFIG.gtmId) {
      initializeGTM();
    } else if (ANALYTICS_CONFIG.gaId) {
      initializeGA();
    }
  }

  if (canUseMarketing()) {
    initializeMarketing();
  }
};

/**
 * Update analytics based on consent changes
 */
export const updateAnalyticsConsent = (): void => {
  if (canUseAnalytics()) {
    initializeAnalytics();
  } else {
    removeAnalyticsScripts();
  }

  if (canUseMarketing()) {
    initializeMarketing();
  } else {
    removeMarketingScripts();
  }
};

/**
 * Initialize analytics on app startup if consent exists
 */
export const initializeAnalyticsOnStartup = (): void => {
  // Small delay to ensure consent context is initialized
  setTimeout(() => {
    updateAnalyticsConsent();
  }, 100);
};
