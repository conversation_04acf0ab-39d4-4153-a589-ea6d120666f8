/**
 * <PERSON><PERSON>sent Storage Utilities
 * Manages localStorage for GDPR cookie consent preferences
 */

export interface CookieConsent {
  essential: boolean;
  analytics: boolean;
  marketing: boolean;
  timestamp: number;
  version: string;
}

export interface CookieConsentState extends CookieConsent {
  hasConsented: boolean;
  isExpired: boolean;
}

// Storage key for cookie consent
const STORAGE_KEY = 'metanord-cookie-consent';

// Current consent version (increment when policy changes)
const CONSENT_VERSION = '1.0';

// Consent expiration time (12 months in milliseconds)
const CONSENT_EXPIRY = 12 * 30 * 24 * 60 * 60 * 1000; // 12 months

/**
 * Default consent state - only essential cookies enabled
 */
export const DEFAULT_CONSENT: CookieConsent = {
  essential: true, // Always true, cannot be disabled
  analytics: false,
  marketing: false,
  timestamp: Date.now(),
  version: CONSENT_VERSION,
};

/**
 * Check if we're in a browser environment
 */
const isBrowser = (): boolean => {
  return typeof window !== 'undefined' && typeof localStorage !== 'undefined';
};

/**
 * Get stored cookie consent from localStorage
 */
export const getStoredConsent = (): CookieConsentState => {
  if (!isBrowser()) {
    return {
      ...DEFAULT_CONSENT,
      hasConsented: false,
      isExpired: false,
    };
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return {
        ...DEFAULT_CONSENT,
        hasConsented: false,
        isExpired: false,
      };
    }

    const consent: CookieConsent = JSON.parse(stored);
    
    // Check if consent is expired (older than 12 months)
    const isExpired = Date.now() - consent.timestamp > CONSENT_EXPIRY;
    
    // Check if consent version is outdated
    const isOutdated = consent.version !== CONSENT_VERSION;
    
    if (isExpired || isOutdated) {
      // Clear expired/outdated consent
      localStorage.removeItem(STORAGE_KEY);
      return {
        ...DEFAULT_CONSENT,
        hasConsented: false,
        isExpired: true,
      };
    }

    return {
      ...consent,
      essential: true, // Always ensure essential is true
      hasConsented: true,
      isExpired: false,
    };
  } catch (error) {
    console.error('Error reading cookie consent from localStorage:', error);
    // Clear corrupted data
    localStorage.removeItem(STORAGE_KEY);
    return {
      ...DEFAULT_CONSENT,
      hasConsented: false,
      isExpired: false,
    };
  }
};

/**
 * Save cookie consent to localStorage
 */
export const saveConsent = (consent: Partial<CookieConsent>): void => {
  if (!isBrowser()) {
    return;
  }

  try {
    const consentToSave: CookieConsent = {
      essential: true, // Always true
      analytics: consent.analytics ?? false,
      marketing: consent.marketing ?? false,
      timestamp: Date.now(),
      version: CONSENT_VERSION,
    };

    localStorage.setItem(STORAGE_KEY, JSON.stringify(consentToSave));
    
    if (import.meta.env.DEV) {
      console.log('Cookie consent saved:', consentToSave);
    }
  } catch (error) {
    console.error('Error saving cookie consent to localStorage:', error);
  }
};

/**
 * Clear all cookie consent data
 */
export const clearConsent = (): void => {
  if (!isBrowser()) {
    return;
  }

  try {
    localStorage.removeItem(STORAGE_KEY);
    if (import.meta.env.DEV) {
      console.log('Cookie consent cleared');
    }
  } catch (error) {
    console.error('Error clearing cookie consent:', error);
  }
};

/**
 * Accept all cookies
 */
export const acceptAllCookies = (): CookieConsent => {
  const consent: CookieConsent = {
    essential: true,
    analytics: true,
    marketing: true,
    timestamp: Date.now(),
    version: CONSENT_VERSION,
  };
  
  saveConsent(consent);
  return consent;
};

/**
 * Reject non-essential cookies
 */
export const rejectNonEssentialCookies = (): CookieConsent => {
  const consent: CookieConsent = {
    essential: true,
    analytics: false,
    marketing: false,
    timestamp: Date.now(),
    version: CONSENT_VERSION,
  };
  
  saveConsent(consent);
  return consent;
};

/**
 * Check if a specific cookie category is enabled
 */
export const isCookieCategoryEnabled = (category: keyof CookieConsent): boolean => {
  if (category === 'essential') {
    return true; // Essential cookies are always enabled
  }

  const consent = getStoredConsent();
  if (!consent.hasConsented) {
    return false; // No consent given yet
  }

  return consent[category] as boolean;
};

/**
 * Get consent status for analytics tracking
 */
export const canUseAnalytics = (): boolean => {
  return isCookieCategoryEnabled('analytics');
};

/**
 * Get consent status for marketing tracking
 */
export const canUseMarketing = (): boolean => {
  return isCookieCategoryEnabled('marketing');
};
