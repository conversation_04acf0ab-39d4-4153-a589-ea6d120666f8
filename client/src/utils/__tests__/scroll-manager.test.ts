import { describe, it, expect, vi } from 'vitest';
import { setupScrollPositionManagement } from '../scroll-manager';

// Helper to replace window methods
const originalAdd = window.addEventListener;
const originalRemove = window.removeEventListener;

describe('setupScrollPositionManagement', () => {
  it('adds and removes popstate listener correctly', () => {
    const addSpy = vi.fn();
    const removeSpy = vi.fn();
    (window as any).addEventListener = addSpy;
    (window as any).removeEventListener = removeSpy;

    const cleanup = setupScrollPositionManagement();

    const popstateCall = addSpy.mock.calls.find(([type]) => type === 'popstate');
    expect(popstateCall).toBeDefined();
    const handler = popstateCall?.[1];
    expect(typeof handler).toBe('function');

    cleanup();

    expect(removeSpy).toHaveBeenCalledWith('popstate', handler);

    (window as any).addEventListener = originalAdd;
    (window as any).removeEventListener = originalRemove;
  });
});
