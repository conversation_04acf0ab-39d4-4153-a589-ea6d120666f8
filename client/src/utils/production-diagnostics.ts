/**
 * Production Diagnostics Tool for MetaNord Careers Page
 * Helps identify and debug production-specific issues
 */

interface DiagnosticResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

interface EnvironmentInfo {
  isDev: boolean;
  apiBaseUrl: string;
  userAgent: string;
  hostname: string;
  protocol: string;
  port: string;
}

export class ProductionDiagnostics {
  private results: DiagnosticResult[] = [];

  /**
   * Get current environment information
   */
  getEnvironmentInfo(): EnvironmentInfo {
    return {
      isDev: import.meta.env.DEV,
      apiBaseUrl: import.meta.env.VITE_API_URL || (
        import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
      ),
      userAgent: navigator.userAgent,
      hostname: window.location.hostname,
      protocol: window.location.protocol,
      port: window.location.port
    };
  }

  /**
   * Test API connectivity
   */
  async testApiConnectivity(): Promise<DiagnosticResult> {
    const env = this.getEnvironmentInfo();
    
    try {
      const response = await fetch(`${env.apiBaseUrl}/api/careers`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        return {
          test: 'API Connectivity',
          status: 'pass',
          message: `Successfully connected to ${env.apiBaseUrl}`,
          details: {
            status: response.status,
            jobCount: Array.isArray(data) ? data.length : 0,
            responseHeaders: Object.fromEntries(response.headers.entries())
          }
        };
      } else {
        return {
          test: 'API Connectivity',
          status: 'fail',
          message: `API request failed with status ${response.status}`,
          details: {
            status: response.status,
            statusText: response.statusText,
            url: `${env.apiBaseUrl}/api/careers`
          }
        };
      }
    } catch (error) {
      return {
        test: 'API Connectivity',
        status: 'fail',
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: {
          error: error instanceof Error ? error.stack : error,
          url: `${env.apiBaseUrl}/api/careers`
        }
      };
    }
  }

  /**
   * Test CORS configuration
   */
  async testCorsConfiguration(): Promise<DiagnosticResult> {
    const env = this.getEnvironmentInfo();
    
    try {
      const response = await fetch(`${env.apiBaseUrl}/api/careers`, {
        method: 'OPTIONS',
        headers: {
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      });

      const corsHeaders = {
        'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
        'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
        'access-control-allow-headers': response.headers.get('access-control-allow-headers'),
        'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
      };

      if (corsHeaders['access-control-allow-origin']) {
        return {
          test: 'CORS Configuration',
          status: 'pass',
          message: 'CORS headers are present',
          details: corsHeaders
        };
      } else {
        return {
          test: 'CORS Configuration',
          status: 'warning',
          message: 'CORS headers may not be properly configured',
          details: corsHeaders
        };
      }
    } catch (error) {
      return {
        test: 'CORS Configuration',
        status: 'fail',
        message: `CORS preflight failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      };
    }
  }

  /**
   * Test environment variables
   */
  testEnvironmentVariables(): DiagnosticResult {
    const env = this.getEnvironmentInfo();
    const issues: string[] = [];

    if (!import.meta.env.VITE_API_URL && !env.isDev) {
      issues.push('VITE_API_URL not set in production');
    }

    if (env.apiBaseUrl.includes('localhost') && !env.isDev) {
      issues.push('API URL points to localhost in production');
    }

    if (env.protocol === 'https:' && env.apiBaseUrl.startsWith('http:')) {
      issues.push('Mixed content: HTTPS frontend trying to access HTTP API');
    }

    if (issues.length === 0) {
      return {
        test: 'Environment Variables',
        status: 'pass',
        message: 'Environment variables are properly configured',
        details: env
      };
    } else {
      return {
        test: 'Environment Variables',
        status: 'fail',
        message: `Environment issues found: ${issues.join(', ')}`,
        details: { issues, env }
      };
    }
  }

  /**
   * Test browser console for errors
   */
  testConsoleErrors(): DiagnosticResult {
    const errors: any[] = [];
    
    // Override console.error to capture errors
    const originalError = console.error;
    console.error = (...args) => {
      errors.push(args);
      originalError(...args);
    };

    // Restore after a short delay
    setTimeout(() => {
      console.error = originalError;
    }, 1000);

    return {
      test: 'Console Errors',
      status: errors.length === 0 ? 'pass' : 'warning',
      message: errors.length === 0 ? 'No console errors detected' : `${errors.length} console errors found`,
      details: { errors }
    };
  }

  /**
   * Run all diagnostic tests
   */
  async runAllTests(): Promise<DiagnosticResult[]> {
    this.results = [];

    // Environment check
    this.results.push(this.testEnvironmentVariables());

    // Console errors check
    this.results.push(this.testConsoleErrors());

    // API connectivity test
    this.results.push(await this.testApiConnectivity());

    // CORS test
    this.results.push(await this.testCorsConfiguration());

    return this.results;
  }

  /**
   * Generate diagnostic report
   */
  generateReport(): string {
    const env = this.getEnvironmentInfo();
    const passCount = this.results.filter(r => r.status === 'pass').length;
    const failCount = this.results.filter(r => r.status === 'fail').length;
    const warningCount = this.results.filter(r => r.status === 'warning').length;

    let report = `
🔍 MetaNord Careers Page - Production Diagnostics Report
========================================================

Environment Information:
- Environment: ${env.isDev ? 'Development' : 'Production'}
- Hostname: ${env.hostname}
- Protocol: ${env.protocol}
- API Base URL: ${env.apiBaseUrl}
- User Agent: ${env.userAgent}

Test Results Summary:
- ✅ Passed: ${passCount}
- ❌ Failed: ${failCount}
- ⚠️  Warnings: ${warningCount}

Detailed Results:
`;

    this.results.forEach(result => {
      const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
      report += `\n${icon} ${result.test}: ${result.message}`;
      
      if (result.details) {
        report += `\n   Details: ${JSON.stringify(result.details, null, 2)}`;
      }
    });

    return report;
  }
}

/**
 * Quick diagnostic function for immediate use
 */
export async function runQuickDiagnostics(): Promise<void> {
  const diagnostics = new ProductionDiagnostics();
  await diagnostics.runAllTests();
  const report = diagnostics.generateReport();
  
  console.log(report);
  
  // Also display in a modal for easy viewing
  if (typeof window !== 'undefined') {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.8);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: monospace;
    `;
    
    const content = document.createElement('pre');
    content.style.cssText = `
      background: white;
      padding: 20px;
      border-radius: 8px;
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
      white-space: pre-wrap;
      font-size: 12px;
    `;
    content.textContent = report;
    
    const closeBtn = document.createElement('button');
    closeBtn.textContent = 'Close';
    closeBtn.style.cssText = `
      position: absolute;
      top: 10px;
      right: 10px;
      padding: 5px 10px;
      background: #2D7EB6;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    `;
    closeBtn.onclick = () => document.body.removeChild(modal);
    
    modal.appendChild(content);
    modal.appendChild(closeBtn);
    document.body.appendChild(modal);
  }
}

// Auto-run diagnostics in development
if (import.meta.env.DEV) {
  console.log('🔍 MetaNord Production Diagnostics available. Run runQuickDiagnostics() to test.');
}
