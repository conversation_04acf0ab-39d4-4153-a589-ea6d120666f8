import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { apiRequest } from '../lib/queryClient';
import { ProductData } from './use-product-api';

// Admin-specific product interface
export interface AdminProduct extends ProductData {
  id: number;
  createdAt?: string;
  updatedAt?: string;
}

// Admin-specific project interface
export interface AdminProject {
  id: number;
  title: string;
  language: string;
  slug: string;
  description: string;
  summary: string;
  location: string;
  year: number;
  published: boolean;
  productTags: string[];
  images: {
    main: string;
    gallery: string[];
  };
  createdAt: string;
  updatedAt: string;
}

// Admin Products Hook
export function useAdminProducts() {
  const { i18n } = useTranslation();
  const language = i18n.language;

  return useQuery({
    queryKey: ['admin', 'products', language],
    queryFn: async () => {
      if (import.meta.env.DEV) console.log('[Admin API] Fetching admin products for language:', language);
      try {
        const response = await apiRequest('GET', `/api/admin/products?language=${language}`);
        const data = await response.json();
        if (import.meta.env.DEV) console.log('[Admin API] Received admin products:', data?.length || 0);
        return Array.isArray(data) ? data : [];
      } catch (error) {
        console.error('[Admin API] Error fetching admin products:', error);
        // Fallback to regular products endpoint if admin endpoint fails
        try {
          if (import.meta.env.DEV) console.log('[Admin API] Trying fallback to regular products endpoint');
          const fallbackResponse = await apiRequest('GET', `/api/products?language=${language}`);
          const fallbackData = await fallbackResponse.json();
          if (import.meta.env.DEV) console.log('[Admin API] Fallback products received:', fallbackData?.length || 0);
          return Array.isArray(fallbackData) ? fallbackData : [];
        } catch (fallbackError) {
          console.error('[Admin API] Fallback also failed:', fallbackError);
          return [];
        }
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin Projects Hook
export function useAdminProjects() {
  const { i18n } = useTranslation();
  const language = i18n.language;

  return useQuery({
    queryKey: ['admin', 'projects', language],
    queryFn: async () => {
      if (import.meta.env.DEV) console.log('[Admin API] Fetching admin projects for language:', language);
      try {
        const response = await apiRequest('GET', `/api/admin/projects?language=${language}`);
        const data = await response.json();
        if (import.meta.env.DEV) console.log('[Admin API] Received admin projects:', data?.length || 0);
        return Array.isArray(data) ? data : [];
      } catch (error) {
        console.error('[Admin API] Error fetching admin projects:', error);
        // Fallback to regular projects endpoint if admin endpoint fails
        try {
          if (import.meta.env.DEV) console.log('[Admin API] Trying fallback to regular projects endpoint');
          const fallbackResponse = await apiRequest('GET', `/api/projects?language=${language}`);
          const fallbackData = await fallbackResponse.json();
          if (import.meta.env.DEV) console.log('[Admin API] Fallback projects received:', fallbackData?.length || 0);
          return Array.isArray(fallbackData) ? fallbackData : [];
        } catch (fallbackError) {
          console.error('[Admin API] Fallback also failed:', fallbackError);
          return [];
        }
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Product Mutations
export function useCreateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await apiRequest('POST', '/api/admin/products', formData);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
}

export function useUpdateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, formData }: { id: string | number; formData: FormData }) => {
      const response = await apiRequest('PUT', `/api/admin/products/${id}`, formData);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
}

export function useDeleteProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string | number) => {
      const response = await apiRequest('DELETE', `/api/admin/products/${id}`);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
}

// Admin Inquiries Interface
export interface AdminInquiry {
  id: number;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'new' | 'read' | 'replied' | 'archived';
  archived?: boolean;
  createdAt: string;
  company?: string;
  inquiryType?: string;
}

// Admin Offers Interface
export interface AdminOffer {
  id: number;
  clientName: string;
  clientEmail: string;
  amount: number;
  status: 'draft' | 'sent' | 'accepted' | 'declined';
  description: string;
  validUntil: string;
  createdAt: string;
  updatedAt: string;
}

// Admin Users Interface
export interface AdminUser {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  status: 'active' | 'inactive';
  lastLogin?: string;
  createdAt: string;
}

// Admin Documents Interface
export interface AdminDocument {
  id: number;
  title: string;
  description?: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  uploadDate: string;
  category?: string;
}

// Admin Inquiries Hook
export function useAdminInquiries() {
  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  return useQuery({
    queryKey: ['/api/admin/inquiries'],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/inquiries`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (import.meta.env.DEV) {
          console.log('[Admin API] Inquiries response:', data);
        }

        // Handle both direct array and wrapped response formats
        if (Array.isArray(data)) {
          return data;
        } else if (data.success && Array.isArray(data.inquiries)) {
          return data.inquiries;
        } else {
          console.error('[Admin API] Error fetching inquiries:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin inquiries:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin Offers Hook
export function useAdminOffers() {
  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  return useQuery({
    queryKey: ['/api/admin/offers'],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/offers`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (import.meta.env.DEV) {
          console.log('[Admin API] Offers response:', data);
        }

        // Handle both direct array and wrapped response formats
        if (Array.isArray(data)) {
          return data;
        } else if (data.success && Array.isArray(data.offers)) {
          return data.offers;
        } else {
          console.error('[Admin API] Error fetching offers:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin offers:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin Users Hook
export function useAdminUsers() {
  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  return useQuery({
    queryKey: ['/api/admin/users'],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/users`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (import.meta.env.DEV) {
          console.log('[Admin API] Users response:', data);
        }

        // Handle both direct array and wrapped response formats
        if (Array.isArray(data)) {
          return data;
        } else if (data.success && Array.isArray(data.users)) {
          return data.users;
        } else {
          console.error('[Admin API] Error fetching users:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin users:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin Documents Hook
export function useAdminDocuments() {
  return useQuery({
    queryKey: ['/api/admin/documents'],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/documents`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (data.success) {
          return Array.isArray(data.documents) ? data.documents : [];
        } else {
          console.error('[Admin API] Error fetching documents:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin documents:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin Quote Requests Hook
export function useAdminQuotes() {
  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  return useQuery({
    queryKey: ['/api/admin/quotes'],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/quotes`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (import.meta.env.DEV) {
          console.log('[Admin API] Quotes response:', data);
        }

        // Handle both direct array and wrapped response formats
        if (Array.isArray(data)) {
          return data;
        } else if (data.success && Array.isArray(data.quotes)) {
          return data.quotes;
        } else {
          console.error('[Admin API] Error fetching quotes:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin quotes:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin CRM Clients Hook
export function useAdminCrmClients() {
  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  return useQuery({
    queryKey: ['/api/admin/crm/clients'],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/crm/clients`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (import.meta.env.DEV) {
          console.log('[Admin API] CRM Clients response:', data);
        }

        // Handle both direct array and wrapped response formats
        if (Array.isArray(data)) {
          return data;
        } else if (data.success && Array.isArray(data.clients)) {
          return data.clients;
        } else {
          console.error('[Admin API] Error fetching CRM clients:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin CRM clients:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin Navigation Hook
export function useAdminNavigation() {
  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  return useQuery({
    queryKey: ['/api/admin/navigation'],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/navigation`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (import.meta.env.DEV) {
          console.log('[Admin API] Navigation response:', data);
        }

        // Handle both direct array and wrapped response formats
        if (Array.isArray(data)) {
          return data;
        } else if (data.success && Array.isArray(data.navigation)) {
          return data.navigation;
        } else {
          console.error('[Admin API] Error fetching navigation:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin navigation:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin Media Hook
export function useAdminMedia(folder = '', type = 'all', search = '') {
  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  return useQuery({
    queryKey: ['/api/admin/media', folder, type, search],
    queryFn: async () => {
      try {
        const url = new URL(`${API_BASE_URL}/api/admin/media`);
        if (folder) url.searchParams.append('folder', folder);
        if (type !== 'all') url.searchParams.append('type', type);
        if (search) url.searchParams.append('search', search);

        const response = await fetch(url.toString(), {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (import.meta.env.DEV) {
          console.log('[Admin API] Media response:', data);
        }

        // Handle both direct array and wrapped response formats
        if (Array.isArray(data)) {
          return data;
        } else if (data.success && Array.isArray(data.media)) {
          return data.media;
        } else {
          console.error('[Admin API] Error fetching media:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin media:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Admin Notifications Hook
export function useAdminNotifications() {
  return useQuery({
    queryKey: ['/api/admin/notifications'],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/notifications`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          window.location.href = '/admin/login';
          return [];
        }

        const data = await response.json();
        if (data.success) {
          return Array.isArray(data.notifications) ? data.notifications : [];
        } else {
          console.error('[Admin API] Error fetching notifications:', data.message);
          return [];
        }
      } catch (error) {
        console.error('[Admin API] Error fetching admin notifications:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

// Project Mutations
export function useCreateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (projectData: Partial<AdminProject>) => {
      const response = await apiRequest('POST', '/api/admin/projects', projectData);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}

export function useUpdateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, projectData }: { id: number; projectData: Partial<AdminProject> }) => {
      const response = await apiRequest('PATCH', `/api/admin/projects/${id}`, projectData);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}

export function useDeleteProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest('DELETE', `/api/admin/projects/${id}`);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}
