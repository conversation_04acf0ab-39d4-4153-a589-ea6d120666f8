import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

type SupportedLanguage = 'en' | 'et' | 'ru' | 'lv' | 'lt' | 'pl' | 'zh-CN';

export function useLanguage() {
  const { i18n } = useTranslation();
  const [language, setLanguage] = useState<SupportedLanguage>('en');
  const [isChanging, setIsChanging] = useState(false);

  useEffect(() => {
    const currentLang = (i18n.language || 'en') as SupportedLanguage;
    setLanguage(currentLang);
  }, [i18n.language]);

  const changeLanguage = useCallback(async (newLanguage: SupportedLanguage) => {
    if (isChanging || newLanguage === language) {
      return;
    }

    try {
      setIsChanging(true);
      await i18n.changeLanguage(newLanguage);
      setLanguage(newLanguage);

      // Update document language for accessibility
      document.documentElement.lang = newLanguage;

    } catch (error) {
      console.error('Error changing language:', error);
      // Fallback to English on error
      if (newLanguage !== 'en') {
        try {
          await i18n.changeLanguage('en');
          setLanguage('en');
          document.documentElement.lang = 'en';
        } catch (fallbackError) {
          console.error('Fallback language change failed:', fallbackError);
        }
      }
    } finally {
      setIsChanging(false);
    }
  }, [i18n, language, isChanging]);

  return { language, changeLanguage, isChanging };
}