/**
 * Complete Product Loading Hook
 * 
 * This hook ensures we load all 40+ products from the definitive source
 * and properly categorize them for display in the MetaNord product catalog.
 */
import { useState, useEffect } from 'react';
import { ProductData } from './use-product-api';
import { loadAllProducts } from '../data/definitive-product-source';

export function useFullProducts() {
  const [products, setProducts] = useState<ProductData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [categoryCount, setCategoryCount] = useState<Record<string, number>>({});

  useEffect(() => {
    let isMounted = true;

    async function loadProducts() {
      try {
        // Load products from the definitive source
        if (import.meta.env.DEV) console.log('Loading complete product catalog from definitive source...');
        const allProducts = await loadAllProducts();
        
        // Count products by category for verification
        const categories: Record<string, number> = {};
        allProducts.forEach(product => {
          const category = product.category || 'uncategorized';
          categories[category] = (categories[category] || 0) + 1;
        });
        
        if (import.meta.env.DEV) console.log(`Loaded ${allProducts.length} products across ${Object.keys(categories).length} categories`);
        
        // Only update state if component is still mounted
        if (isMounted) {
          setProducts(allProducts);
          setCategoryCount(categories);
          setIsLoading(false);
        }
      } catch (err) {
        console.error('Failed to load products:', err);
        if (isMounted) {
          setError(err instanceof Error ? err : new Error(String(err)));
          setIsLoading(false);
        }
      }
    }

    loadProducts();

    // Cleanup
    return () => {
      isMounted = false;
    };
  }, []);

  return {
    data: products,
    isLoading,
    error,
    categoryCount,
    hasFullCatalog: products.length >= 40
  };
}