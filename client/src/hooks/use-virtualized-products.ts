import { useState, useEffect, useRef, useCallback } from 'react';

// Configuration for different screen sizes
const GRID_CONFIG = {
  xs: 1,  // Extra small screens (<640px): 1 column
  sm: 2,  // Small screens (≥640px): 2 columns
  md: 2,  // Medium screens (≥768px): 2 columns
  lg: 3,  // Large screens (≥1024px): 3 columns
  xl: 4,  // Extra large screens (≥1280px): 4 columns
};

// Virtualization hook for products grid - only renders what's visible
export function useVirtualizedProducts<T>(
  allProducts: T[],
  itemHeight: number = 450, // Default item height based on product card
  buffer: number = 2, // Number of rows to render above and below visible area
) {
  // Track scroll position and viewport dimensions
  const [scrollY, setScrollY] = useState(0);
  const [viewportHeight, setViewportHeight] = useState(0);
  const gridRef = useRef<HTMLDivElement>(null);
  
  // Calculate current number of columns based on screen width
  const [columns, setColumns] = useState(2); // Default to 2 columns
  
  // Update layout dimensions on screen size changes
  const updateLayoutDimensions = useCallback(() => {
    const width = window.innerWidth;
    
    // Determine columns based on screen width - match the grid in CSS
    if (width >= 1280) setColumns(GRID_CONFIG.xl);
    else if (width >= 1024) setColumns(GRID_CONFIG.lg);
    else if (width >= 768) setColumns(GRID_CONFIG.md);
    else if (width >= 640) setColumns(GRID_CONFIG.sm);
    else setColumns(GRID_CONFIG.xs);
    
    setViewportHeight(window.innerHeight);
  }, []);
  
  // Update scroll position using requestAnimationFrame for smoothness
  const updateScrollPosition = useCallback(() => {
    requestAnimationFrame(() => {
      setScrollY(window.scrollY);
    });
  }, []);
  
  // Set up event listeners for scroll and resize
  useEffect(() => {
    // Initial setup
    updateLayoutDimensions();
    updateScrollPosition();
    
    // Add event listeners with passive option for better performance
    window.addEventListener('scroll', updateScrollPosition, { passive: true });
    window.addEventListener('resize', updateLayoutDimensions, { passive: true });
    
    // Clean up
    return () => {
      window.removeEventListener('scroll', updateScrollPosition);
      window.removeEventListener('resize', updateLayoutDimensions);
    };
  }, [updateLayoutDimensions, updateScrollPosition]);
  
  // Calculate which items should be rendered based on viewport and scroll position
  const visibleIndices = useCallback(() => {
    if (!gridRef.current) {
      return { startIndex: 0, endIndex: Math.min(allProducts.length, columns * 3) };
    }
    
    // Calculate the grid's position relative to the viewport
    const gridRect = gridRef.current.getBoundingClientRect();
    const gridTop = gridRect.top + scrollY;
    
    // Calculate visible row range
    const startRow = Math.max(0, Math.floor((scrollY - gridTop) / itemHeight) - buffer);
    const endRow = Math.ceil((scrollY + viewportHeight - gridTop) / itemHeight) + buffer;
    
    // Convert to item indices
    const startIndex = startRow * columns;
    const endIndex = Math.min(allProducts.length, endRow * columns);
    
    return { startIndex, endIndex };
  }, [allProducts.length, buffer, columns, itemHeight, scrollY, viewportHeight]);
  
  // Get the items that should be currently visible
  const { startIndex, endIndex } = visibleIndices();
  const visibleItems = allProducts.slice(startIndex, endIndex);
  
  // Total grid height to maintain scroll position/layout
  const rowCount = Math.ceil(allProducts.length / columns);
  const totalHeight = rowCount * itemHeight;
  
  // Function to get absolute position for a specific item
  const getItemPosition = useCallback((localIndex: number) => {
    const actualIndex = startIndex + localIndex;
    const row = Math.floor(actualIndex / columns);
    const col = actualIndex % columns;
    
    return {
      top: row * itemHeight,
      left: `${(col / columns) * 100}%`,
      width: `${100 / columns}%`,
    };
  }, [columns, itemHeight, startIndex]);
  
  return {
    visibleItems,
    startIndex,
    totalHeight,
    getItemPosition,
    gridRef,
  };
}