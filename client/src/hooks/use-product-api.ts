import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

export interface ProductDocument {
  id: number;
  title: string;
  description?: string;
  fileUrl?: string;
  fileType?: string;
  language?: string;
}

export interface ProductData {
  productId: string;
  id?: number;
  slug?: string;
  title: string;
  description: string;
  image: string;
  category: string;
  status: string;
  language: string;
  features?: string[];
  applications?: string[];
  specifications?: Record<string, string>;
  link?: string;
  type?: string;
  documents?: ProductDocument[];
  // ... другие поля если нужно
}

function prepareProductForDisplay(product: ProductData): ProductData {
  return {
    ...product,
    id: (product as any).id,
    slug: product.productId,
    link: product.link || `/products/${product.productId}`,
    image: product.image || '/images/product-placeholder.jpg',
    category: product.category || 'product',
    status: product.status || 'in_stock',
    features: product.features || [],
    applications: product.applications || [],
    specifications: product.specifications || {},
  };
}

const productCache = new Map<string, ProductData[]>();

export function useAllProducts() {
  const { i18n } = useTranslation();
  const language = i18n.language;

  return useQuery({
    queryKey: ['products', language],
    queryFn: async () => {
      const cacheKey = `products_${language}`;
      if (productCache.has(cacheKey)) {
        if (import.meta.env.DEV) console.log(`[API] Using cached products for ${language}: ${productCache.get(cacheKey)?.length || 0} items`);
        return productCache.get(cacheKey) || [];
      }

      const API_BASE_URL = import.meta.env.VITE_API_URL || (
        import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
      );
      const apiUrl = `${API_BASE_URL}/api/products?language=${language}`;
      const fallbackApiUrl = `${API_BASE_URL}/api/products?language=en`;

      if (import.meta.env.DEV) console.log(`[API] Fetching products for ${language} from ${apiUrl}`);

      try {
        // Always fetch both current language and English as fallback
        const [currentLangResponse, englishResponse] = await Promise.all([
          fetch(apiUrl, {
            headers: { 'Cache-Control': 'no-cache' }
          }),
          language !== 'en' ? fetch(fallbackApiUrl, {
            headers: { 'Cache-Control': 'no-cache' }
          }) : Promise.resolve(null),
        ]);

        if (!currentLangResponse.ok) throw new Error(`Failed to fetch products: ${currentLangResponse.status}`);
        const currentLangData = await currentLangResponse.json();

        let products = Array.isArray(currentLangData)
          ? currentLangData.map(prepareProductForDisplay)
          : [];

        if (import.meta.env.DEV) console.log(`[API] Received ${products.length} products in ${language}`);

        // If we're not in English, merge with English products that don't exist in current language
        if (language !== 'en' && englishResponse && englishResponse.ok) {
          const englishData = await englishResponse.json();
          
          if (Array.isArray(englishData)) {
            // Get English products that aren't already in the current language
            const currentProductIds = new Set(products.map(p => p.productId));
            const englishProducts = englishData
              .filter(engProduct => !currentProductIds.has(engProduct.productId))
              .map(p => ({
                ...p,
                language: 'en',
                _usingEnglishFallback: true,
              }));
            
            if (englishProducts.length > 0) {
              if (import.meta.env.DEV) console.log(`[API] Adding ${englishProducts.length} English fallback products`);
              products = [...products, ...englishProducts.map(prepareProductForDisplay)];
            }
          }
        }

        if (import.meta.env.DEV) {
          console.log(`[API] Total products after language handling: ${products.length}`);
          
          // Log category distribution
          const categories = {};
          products.forEach(p => {
            const cat = p.category || 'uncategorized';
            categories[cat] = (categories[cat] || 0) + 1;
          });
          console.log('[API] Products by category:', categories);
        }

        productCache.set(cacheKey, products);
        return products;
      } catch (error) {
        console.error('[API] Error fetching products:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    gcTime: 1000 * 60 * 60, // 1 hour
    retry: 3,
    networkMode: 'always',
  });
}

// 👇 Вот добавленный хук для категорий продуктов
export function useProductCategories() {
  const { data: products = [] } = useAllProducts();
  const categories = Array.from(new Set(products.map(p => p.category))).filter(Boolean);
  return categories.map(category => ({
    id: category,
    label: category.charAt(0).toUpperCase() + category.slice(1).replace(/-/g, ' ')
  }));
}
