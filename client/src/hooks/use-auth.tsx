import {
  createContext,
  ReactN<PERSON>,
  useContext,
  useEffect,
  useState,
} from "react";
import { apiRequest, queryClient } from "../lib/queryClient";
import { useToast } from "./use-toast";

interface User {
  id: number;
  username: string;
  isAdmin: boolean;
  role?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  loginAdmin: (
    username: string,
    password: string,
  ) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (import.meta.env.DEV) {
          console.log('[Auth] 🔍 Checking authentication...');
          console.log('[Auth] 🌐 API_BASE_URL:', API_BASE_URL);
        }

        const res = await fetch(`${API_BASE_URL}/api/admin/me`, {
          method: "GET",
          credentials: "include",
          headers: {
            Accept: "application/json",
          },
          cache: "no-cache",
        });

        if (import.meta.env.DEV) {
          console.log('[Auth] 📡 Response status:', res.status);
        }

        if (res.ok) {
          const data = await res.json();
          if (import.meta.env.DEV) {
            console.log('[Auth] ✅ Auth response:', data);
          }
          if (data.success && data.user) {
            setUser(data.user);
            if (import.meta.env.DEV) {
              console.log('[Auth] 👤 User set:', data.user);
            }
          } else {
            setUser(null);
            if (import.meta.env.DEV) {
              console.log('[Auth] ❌ Invalid response structure');
            }
          }
        } else {
          if (import.meta.env.DEV) {
            console.log('[Auth] ❌ Auth failed with status:', res.status);
          } else if (import.meta.env.PROD && res.status !== 401) {
            // Log unexpected errors in production (401 is expected when not logged in)
            console.error('[Auth] Unexpected auth check failure:', {
              status: res.status,
              statusText: res.statusText,
              url: res.url,
              timestamp: new Date().toISOString()
            });
          }
          setUser(null);
        }
      } catch (error) {
        console.error("[Auth] ❌ Auth check failed:", {
          error: error instanceof Error ? error.message : 'Unknown error',
          apiUrl: API_BASE_URL,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent.substring(0, 50) + '...'
        });
        setUser(null);
      } finally {
        setLoading(false);
        if (import.meta.env.DEV) {
          console.log('[Auth] ✅ Auth check completed, loading set to false');
        }
      }
    };

    checkAuth();
  }, [API_BASE_URL]);

  const loginAdmin = async (
    username: string,
    password: string,
  ): Promise<{ success: boolean; message?: string }> => {
    try {
      if (import.meta.env.DEV) console.log('[Auth] Attempting login with:', username);

      const res = await fetch(`${API_BASE_URL}/api/admin/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          username: username,
          password
        }),
        cache: "no-cache",
      });

      const data = await res.json();
      if (import.meta.env.DEV) console.log('[Auth] Login response:', data);

      if (!res.ok) {
        return {
          success: false,
          message: data.message || "Invalid username or password",
        };
      }

      if (data.success && data.user) {
        setUser(data.user);

        // Verify session
        try {
          const verifyRes = await fetch(`${API_BASE_URL}/api/admin/me`, {
            method: "GET",
            credentials: "include",
            headers: { Accept: "application/json" },
            cache: "no-cache",
          });

          const verifyData = await verifyRes.json();
          if (import.meta.env.DEV) console.log("Session verify result:", verifyData);
        } catch (verifyErr) {
          console.warn("Session verification failed", verifyErr);
        }

        queryClient.invalidateQueries({
          predicate: (query) =>
            query.queryKey[0].toString().startsWith("/api/admin"),
        });

        return { success: true };
      }

      return { success: false, message: "Unexpected response from server" };
    } catch (error) {
      const errorDetails = {
        error: error instanceof Error ? error.message : 'Unknown error',
        apiUrl: API_BASE_URL,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent.substring(0, 50) + '...'
      };

      console.error('[Auth] Login error:', errorDetails);

      // Production-friendly error messages
      const isNetworkError = error instanceof Error && (
        error.message.includes('fetch') ||
        error.message.includes('network') ||
        error.message.includes('Failed to fetch')
      );

      const userMessage = isNetworkError
        ? "Unable to connect to the server. Please check your internet connection."
        : "There was a problem logging in. Please try again.";

      toast({
        title: "Login error",
        description: userMessage,
        variant: "destructive",
      });

      return {
        success: false,
        message: isNetworkError ? "Network connection failed" : "Login failed"
      };
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await apiRequest("POST", "/api/admin/logout");
      setUser(null);
      queryClient.invalidateQueries({ queryKey: ["/api/admin"] });
    } catch (error) {
      toast({
        title: "Logout error",
        description: "There was a problem logging out.",
        variant: "destructive",
      });
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, loginAdmin, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
