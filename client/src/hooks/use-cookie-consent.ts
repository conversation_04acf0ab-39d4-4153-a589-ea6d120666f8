/**
 * <PERSON><PERSON> Hook
 * Provides easy access to cookie consent functionality
 */

export { useCookieConsent } from '../contexts/CookieConsentContext';

// Re-export utilities for convenience
export {
  canUseAnalytics,
  canUseMarketing,
  isCookieCategoryEnabled,
  getStoredConsent,
} from '../utils/cookie-storage';

export type {
  CookieConsent,
  CookieConsentState,
} from '../utils/cookie-storage';
