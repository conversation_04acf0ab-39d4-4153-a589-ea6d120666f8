import { QueryClient, QueryFunction } from "@tanstack/react-query";

// Enhanced API base URL configuration with production debugging
const getApiBaseUrl = () => {
  const envUrl = import.meta.env.VITE_API_URL;
  const isDev = import.meta.env.DEV;
  const isProduction = import.meta.env.PROD;

  // In development, use empty string to leverage Vite proxy
  // In production, use the environment variable or default production URL
  const defaultUrl = isDev ? '' : 'https://api.metanord.eu';
  const finalUrl = envUrl || defaultUrl;

  // Log configuration in development only
  if (isDev) {
    console.log('[API Config] Development Environment:', {
      isDev,
      VITE_API_URL: envUrl,
      defaultUrl,
      finalUrl,
      hostname: window.location.hostname,
      protocol: window.location.protocol
    });
  }

  return finalUrl;
};

const API_BASE_URL = getApiBaseUrl();

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;

    // Enhanced error logging for production debugging
    const errorDetails = {
      status: res.status,
      statusText: res.statusText,
      url: res.url,
      method: res.type,
      headers: Object.fromEntries(res.headers.entries()),
      responseText: text,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent.substring(0, 50) + '...'
    };

    // Log errors in both development and production for debugging
    console.error('[API Error] Request failed:', errorDetails);

    // Additional production-specific error handling
    if (import.meta.env.PROD) {
      // Check for common production issues
      if (res.status === 0 || res.status === 502 || res.status === 503) {
        console.error('[API Error] Backend connectivity issue detected');
      } else if (res.status === 401) {
        console.error('[API Error] Authentication issue - session may have expired');
      } else if (res.status === 403) {
        console.error('[API Error] Authorization issue - insufficient permissions');
      } else if (res.status === 404) {
        console.error('[API Error] Endpoint not found - check API URL configuration');
      }
    }

    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;

  // Handle different data types
  let headers: Record<string, string> = {};
  let body: string | FormData | undefined;

  if (data) {
    if (data instanceof FormData) {
      // Don't set Content-Type for FormData - let browser set it with boundary
      body = data;
    } else {
      headers["Content-Type"] = "application/json";
      body = JSON.stringify(data);
    }
  }

  // Enhanced request configuration for production compatibility
  const requestConfig: RequestInit = {
    method,
    headers,
    body,
    credentials: "include", // Essential for session cookies
    mode: "cors", // Explicit CORS mode
    cache: "no-cache", // Prevent caching issues in production
  };

  // Development debugging only
  if (import.meta.env.DEV && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    console.log(`[API Request] ${method} ${fullUrl}`, {
      hasBody: !!body,
      headers: Object.keys(headers)
    });
  }

  try {
    const res = await fetch(fullUrl, requestConfig);
    await throwIfResNotOk(res);
    return res;
  } catch (error) {
    // Enhanced error handling for production debugging
    console.error(`[API Request] Failed ${method} ${fullUrl}:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      url: fullUrl,
      method
    });
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const endpoint = queryKey[0] as string;
    const fullUrl = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
    const res = await fetch(fullUrl, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: import.meta.env.PROD ? 1000 * 60 * 5 : Infinity, // 5 minutes in production
      retry: (failureCount, error: any) => {
        // Enhanced retry logic for production
        if (import.meta.env.PROD) {
          // Don't retry on authentication errors
          if (error?.message?.includes('401') || error?.message?.includes('403')) {
            return false;
          }
          // Retry network errors up to 2 times
          if (error?.message?.includes('fetch') || error?.message?.includes('network')) {
            return failureCount < 2;
          }
          // Don't retry other errors in production
          return false;
        }
        // No retries in development
        return false;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: (failureCount, error: any) => {
        // Only retry mutations on network errors in production
        if (import.meta.env.PROD) {
          if (error?.message?.includes('fetch') || error?.message?.includes('network')) {
            return failureCount < 1; // Only one retry for mutations
          }
        }
        return false;
      },
    },
  },
});
