/**
 * Admin Theme Management
 * This file contains utilities for managing the admin panel theme
 */

/**
 * Load the admin CSS stylesheet - CSS is now imported directly in index.css
 * This function is kept for compatibility but doesn't need to do anything
 */
export const loadAdminCSS = () => {
  // CSS is now imported directly in index.css, so this is a no-op
  // We keep this function for compatibility with existing code
};

/**
 * Unload the admin CSS stylesheet - CSS is now imported directly in index.css
 * This function is kept for compatibility but doesn't need to do anything
 */
export const unloadAdminCSS = () => {
  // CSS is now imported directly in index.css, so this is a no-op
  // We keep this function for compatibility with existing code
};

/**
 * Configuration for admin theme root CSS variables
 * Using MetaNord brand colors: #2D7EB6 (primary blue) and #40BFB9 (secondary teal)
 */
export const adminThemeConfig = {
  light: {
    '--admin-bg': '#f8fafc',
    '--admin-text': '#1e293b',
    '--admin-text-muted': '#64748b',
    '--admin-border': '#e2e8f0',
    '--admin-header-bg': '#ffffff',
    '--admin-sidebar-bg': '#ffffff',
    '--admin-sidebar-hover': '#f1f5f9',
    '--admin-sidebar-active': 'rgba(45, 126, 182, 0.1)', /* MetaNord primary blue with opacity */
    '--admin-card-bg': '#ffffff',
    '--admin-highlight': 'rgba(45, 126, 182, 0.05)', /* MetaNord primary blue with opacity */
    '--admin-panel-shadow': '0 1px 3px rgba(0, 0, 0, 0.1)',
    '--admin-primary': '#2D7EB6', /* MetaNord primary blue */
    '--admin-secondary': '#40BFB9', /* MetaNord secondary teal */
    '--admin-accent': 'linear-gradient(90deg, #2D7EB6 0%, #40BFB9 100%)', /* MetaNord gradient */
  },
  dark: {
    '--admin-bg': '#0f172a',
    '--admin-text': '#f1f5f9',
    '--admin-text-muted': '#94a3b8',
    '--admin-border': '#334155',
    '--admin-header-bg': '#1e293b',
    '--admin-sidebar-bg': '#1e293b',
    '--admin-sidebar-hover': '#334155',
    '--admin-sidebar-active': 'rgba(45, 126, 182, 0.2)', /* MetaNord primary blue with opacity */
    '--admin-card-bg': '#1e293b',
    '--admin-highlight': 'rgba(45, 126, 182, 0.1)', /* MetaNord primary blue with opacity */
    '--admin-panel-shadow': '0 1px 3px rgba(0, 0, 0, 0.3)',
    '--admin-primary': '#3B82F6', /* Brighter blue for dark mode */
    '--admin-secondary': '#40BFB9', /* MetaNord secondary teal */
    '--admin-accent': 'linear-gradient(90deg, #3B82F6 0%, #40BFB9 100%)', /* Brighter gradient for dark mode */
  }
};

/**
 * Apply admin theme based on dark mode preference
 * @param isDark Whether to apply dark theme (true) or light theme (false)
 */
export const applyAdminTheme = (isDark: boolean) => {
  // Set data-theme attribute on html element
  document.documentElement.setAttribute('data-admin-theme', isDark ? 'dark' : 'light');
  
  // Apply CSS variables
  const theme = isDark ? adminThemeConfig.dark : adminThemeConfig.light;
  Object.entries(theme).forEach(([key, value]) => {
    document.documentElement.style.setProperty(key, value);
  });
  
  // Add admin-mode class to body
  document.body.classList.add('admin-mode');
};

/**
 * Reset admin theme by removing all admin-specific CSS variables
 */
export const resetAdminTheme = () => {
  // Remove data-admin-theme attribute
  document.documentElement.removeAttribute('data-admin-theme');
  
  // Remove CSS variables
  Object.keys(adminThemeConfig.light).forEach((key) => {
    document.documentElement.style.removeProperty(key);
  });
  
  // Remove admin-mode class from body
  document.body.classList.remove('admin-mode');
};