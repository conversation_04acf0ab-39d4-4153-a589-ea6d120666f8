/**
 * Simplified i18n utilities
 *
 * This module provides simple, stable language switching functionality
 * without complex optimizations that could cause UI issues.
 */

import i18n from 'i18next';

/**
 * Simple language change function
 * - Direct language change without complex optimizations
 * - Maintains basic functionality without UI blocking
 */
export const changeLanguageOptimized = async (language: string): Promise<void> => {
  try {
    await i18n.changeLanguage(language);
    document.documentElement.lang = language;
  } catch (error) {
    console.error('Error changing language:', error);
    throw error;
  }
};

/**
 * Simple preload function (no-op for now to maintain compatibility)
 */
export const preloadLanguage = (language: string): void => {
  // Simple no-op to maintain compatibility
  // Actual preloading can be added later if needed
};

/**
 * Get basic i18n metrics
 */
export const getI18nPerformanceMetrics = (): Record<string, any> => {
  return {
    currentLanguage: i18n.language,
    loadedLanguages: i18n.languages || [],
  };
};

/**
 * Simple initialization (no-op for now)
 */
export const initI18nPerformanceOptimizations = (): void => {
  // Simple no-op to maintain compatibility
};