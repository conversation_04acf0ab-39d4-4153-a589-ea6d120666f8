import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

// Import translations
import en from "./locales/en.json";
import et from "./locales/et.json";
import ru from "./locales/ru.json";
import lv from "./locales/lv.json";
import lt from "./locales/lt.json";
import pl from "./locales/pl.json";
import zhCN from "./locales/zh-CN.json";

// Log translation resource loading for debugging
if (import.meta.env.DEV) console.log("Loading i18n resources...");

// Initialize i18next with performance optimizations
i18n
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    // Default language
    fallbackLng: "en",
    // Debug in development (reduce to improve performance)
    debug: false,
    // Resources containing translations
    resources: {
      en: { 
        translation: {
          ...en,
          products: {
            ...en.products,
            filter: {
              all: "All Products",
              aluminum: "Aluminum",
              polyethylene: "Polyethylene",
              steel: "Steel",
              castIron: "Cast Iron"
            },
            castIron: {
              ...en.products?.castIron,
              drainageGrates: {
                title: "Drainage Grates",
                description: "Cast iron drainage grates for stormwater collection systems, designed for varying traffic loads and flow capacities."
              }
            },
            categories: {
              ...en.products?.categories,
              "Cast Iron": "Cast Iron"
            }
          },
          common: {
            ...en.common,
            learnMore: "Learn More",
            viewDetails: "View Details"
          },
          header: {
            ...en.header,
            getPrice: "Request Quote",
            requestCallback: "Request Callback"
          }
        } 
      },
      et: { translation: et },
      ru: { translation: ru },
      lv: { translation: lv },
      lt: { translation: lt },
      pl: { translation: pl },
      "zh-CN": { translation: zhCN },
    },
    // Common options
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    // Remember language selection
    detection: {
      order: ["localStorage", "navigator"],
      caches: ["localStorage"],
    },
    // Simplified configuration for better stability
    returnObjects: false,
    saveMissing: false,
  });

// Log when i18n is ready
i18n.on('initialized', () => {
  if (import.meta.env.DEV) console.log('i18n initialized successfully');
});

// Simple language change handler
i18n.on('languageChanged', (lng) => {
  if (import.meta.env.DEV) console.log(`Language changed to: ${lng}`);

  // Update document language for accessibility
  if (typeof document !== 'undefined') {
    document.documentElement.lang = lng;
  }
});

// Simplified safe translation function
export function safeTranslate(key: string, defaultValue: string = '', options: any = {}): string {
  try {
    const translated = i18n.t(key, { defaultValue, ...options });
    return typeof translated === 'string' ? translated : defaultValue || key;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn(`Translation error for key "${key}":`, error);
    }
    return defaultValue || key;
  }
}

// Simplified category translation
export const translateProductCategory = (category: string): string => {
  if (!category) return "Other";

  try {
    // Try direct translation first
    const translated = i18n.t(`products.categories.${category}`, { defaultValue: null });
    if (translated && translated !== `products.categories.${category}`) {
      return translated;
    }

    // Try lowercase version
    const lowercaseTranslated = i18n.t(`products.categories.${category.toLowerCase()}`, { defaultValue: null });
    if (lowercaseTranslated && lowercaseTranslated !== `products.categories.${category.toLowerCase()}`) {
      return lowercaseTranslated;
    }

    // Return formatted category name as fallback
    return category.split(/[-_ ]/).map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');

  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn("Error in translateProductCategory:", error);
    }
    return category;
  }
};

export default i18n;