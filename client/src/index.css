@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import MetaNord theme styles */
@import './styles/metanord-theme.css';
@import './styles/branding.css';
@import './styles/admin.css';

:root {
  --background: 0 0% 100%;
  --foreground: 225 15% 16%;
  --muted: 220 14% 96%;
  --muted-foreground: 220 8% 46%;
  --popover: 0 0% 100%;
  --popover-foreground: 225 15% 16%;
  --card: 0 0% 100%;
  --card-foreground: 225 15% 16%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --primary: 210 100% 36%;
  --primary-foreground: 210 40% 98%;
  --secondary: 220 9% 46%;
  --secondary-foreground: 210 40% 98%;
  --accent: 174 84% 43%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --ring: 210 100% 36%;
  --radius: 0.75rem;

  /* Chart colors */
  --chart-1: 210 100% 36%;
  --chart-2: 174 84% 43%;
  --chart-3: 220 9% 46%;
  --chart-4: 210 90% 54%;
  --chart-5: 174 76% 60%;

  /* Accent gradients */
  --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--primary)) 100%);

  /* Sidebar colors (not used but required) */
  --sidebar-background: 210 100% 36%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 174 84% 43%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 0 0% 100%;
  --sidebar-accent-foreground: 225 15% 16%;
  --sidebar-border: 210 90% 45%;
  --sidebar-ring: 210 90% 45%;
}

.dark {
  --background: 222 14% 8%;
  --foreground: 210 20% 98%;
  --muted: 223 14% 15%;
  --muted-foreground: 215 16% 65%;
  --popover: 222 14% 8%;
  --popover-foreground: 210 20% 98%;
  --card: 222 14% 8%;
  --card-foreground: 210 20% 98%;
  --border: 223 14% 15%;
  --input: 223 14% 15%;
  --primary: 210 100% 50%;
  --primary-foreground: 0 0% 98%;
  --secondary: 215 14% 56%;
  --secondary-foreground: 0 0% 98%;
  --accent: 174 84% 45%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 224 76% 65%;

  /* Chart colors */
  --chart-1: 210 100% 50%;
  --chart-2: 174 84% 55%;
  --chart-3: 220 20% 65%;
  --chart-4: 210 90% 60%;
  --chart-5: 174 76% 65%;

  /* Sidebar colors */
  --sidebar-background: 223 14% 15%;
  --sidebar-foreground: 210 20% 98%;
  --sidebar-primary: 174 84% 45%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 0 0% 98%;
  --sidebar-accent-foreground: 223 14% 15%;
  --sidebar-border: 224 76% 65%;
  --sidebar-ring: 224 76% 65%;
}

@layer base {
  /* УБРАЛО: * { @apply border; } — не надо! */

  body {
    @apply bg-background text-foreground font-roboto antialiased;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-inter font-bold;
  }
}

@layer utilities {
  /* ... оставь все свои утилиты, ничего не ломай ... */
}

/* --------- ВАЖНО! --------- */
/* Не ставь здесь глобальный селектор, убирающий border у всех! */

/* Кастомный бордер только для кнопок */
.btn-primary,
.btn-gradient,
.btn-gradient-secondary,
button,
a.btn,
.button,
.btn {
  border: 2px solid hsl(var(--border));
  border-radius: 0.5rem;
}

/* Для специальных CTA можно настроить свой border-color, например: */
.btn-gradient, .btn-primary {
  border-color: #2D7EB6; /* или используем цвет из var(--primary) через Tailwind */
}

.btn-gradient-secondary {
  border-color: #40BFB9;
}

.btn-gradient:focus,
.btn-primary:focus,
button:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Оставь остальные стили без изменений */

a {
  text-decoration-color: transparent;
}
a:hover {
  text-decoration: none !important;
  text-decoration-color: transparent !important;
}

  /* Transitions */
  .transition-300 {
    @apply transition-all duration-300 ease-in-out;
  }

  .transition-500 {
    @apply transition-all duration-500 ease-in-out;
  }

  /* Glassmorphism effects */
  .glass-effect {
    @apply bg-white/20 backdrop-blur-md border border-white/30 shadow-lg rounded-2xl;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .glass-card {
    @apply bg-white/20 backdrop-blur-lg border border-white/40 shadow-xl rounded-xl;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  }

  .glass-nav {
    @apply bg-white/20 backdrop-blur-xl border-b border-white/20 shadow-md;
    backdrop-filter: blur(10px);
  }

  .dark .glass-effect {
    @apply bg-gray-900/40 backdrop-blur-xl border border-gray-700/30 shadow-[0_8px_32px_rgba(0, 0, 0, 0.24)];
    backdrop-filter: blur(12px);
  }

  .dark .glass-card {
    @apply bg-gray-900/30 backdrop-blur-xl border border-gray-700/30 shadow-[0_4px_24px_rgba(0, 0, 0, 0.2)];
    backdrop-filter: blur(12px);
  }

  .dark .glass-nav {
    @apply bg-gray-900/50 backdrop-blur-xl border-b border-gray-700/30 shadow-md;
    backdrop-filter: blur(12px);
  }

  /* Neumorphic UI elements */
  .neumorph {
    @apply bg-white rounded-xl transition-all duration-300;
    box-shadow: 8px 8px 16px rgba(174, 174, 192, 0.4),
      -8px -8px 16px rgba(255, 255, 255, 0.8);
  }

  .neumorph-inset {
    @apply bg-gray-50 rounded-xl transition-all duration-300;
    box-shadow: inset 6px 6px 12px rgba(174, 174, 192, 0.2),
      inset -6px -6px 12px rgba(255, 255, 255, 0.7);
  }

  .neumorph-btn {
    @apply bg-white rounded-xl transition-all duration-300;
    box-shadow: 5px 5px 10px rgba(174, 174, 192, 0.4),
      -5px -5px 10px rgba(255, 255, 255, 0.8);
  }

  .neumorph-btn:hover {
    box-shadow: 7px 7px 14px rgba(174, 174, 192, 0.4),
      -7px -7px 14px rgba(255, 255, 255, 0.8);
  }

  .neumorph-btn:active {
    @apply bg-gray-50;
    box-shadow: inset 5px 5px 10px rgba(174, 174, 192, 0.4),
      inset -5px -5px 10px rgba(255, 255, 255, 0.8);
  }

  .dark .neumorph {
    @apply bg-gray-800 rounded-xl transition-all duration-300;
    box-shadow: 8px 8px 16px rgba(0, 0, 0, 0.4),
      -8px -8px 16px rgba(30, 41, 59, 0.4);
  }

  .dark .neumorph-inset {
    @apply bg-gray-900 rounded-xl transition-all duration-300;
    box-shadow: inset 6px 6px 12px rgba(0, 0, 0, 0.4),
      inset -6px -6px 12px rgba(30, 41, 59, 0.3);
  }

  .dark .neumorph-btn {
    @apply bg-gray-800 rounded-xl transition-all duration-300;
    box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.4),
      -5px -5px 10px rgba(30, 41, 59, 0.4);
  }

  .dark .neumorph-btn:hover {
    box-shadow: 7px 7px 14px rgba(0, 0, 0, 0.5),
      -7px -7px 14px rgba(30, 41, 59, 0.5);
  }

  .dark .neumorph-btn:active {
    @apply bg-gray-900;
    box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.5),
      inset -5px -5px 10px rgba(30, 41, 59, 0.3);
  }

  /* Duotone gradients */
  .gradient-blue {
    background: linear-gradient(135deg, #005fa3 0%, #00c4ff 100%);
  }

  .gradient-teal {
    background: linear-gradient(135deg, #0d9488 0%, #14b8a6 100%);
  }

  .gradient-primary {
    background: linear-gradient(135deg, #005fa3 0%, #14b8a6 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, #14b8a6 0%, #22d3ee 100%);
  }

  .gradient-dark {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }

  /* Text effects */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent;
  }

  .text-gradient-blue {
    @apply bg-clip-text text-transparent;
    background: linear-gradient(135deg, #005fa3 0%, #00c4ff 100%);
  }

  /* Advanced hover effects */
  .hover-scale {
    @apply transition-all duration-300 ease-out;
    transform-origin: center;
  }

  .hover-scale:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  }

  .hover-scale-sm {
    @apply transition-all duration-300 ease-out;
  }

  .hover-scale-sm:hover {
    transform: scale(1.02);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  }

  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .hover-glow:hover {
    box-shadow: 0 0 25px rgba(20, 184, 166, 0.4);
    transform: scale(1.01);
  }

  /* Enhanced shadows */
  .shadow-soft {
    box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
  }

  .shadow-card {
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.1);
  }

  .shadow-accent {
    box-shadow: 0 10px 25px -5px rgba(20, 184, 166, 0.3);
  }

  .shadow-primary {
    box-shadow: 0 10px 25px -5px rgba(0, 95, 163, 0.3);
  }

  /* Border gradients */
  .border-gradient {
    border-width: 2px;
    border-style: solid;
    border-image: linear-gradient(to right, #005fa3, #14b8a6) 1;
  }

  /* Button styles */
  .btn-primary {
    @apply bg-primary text-white font-medium rounded-lg transition-all duration-300 shadow-primary hover:shadow-lg hover:-translate-y-1;
  }

  .btn-gradient {
    @apply text-white font-medium rounded-lg transition-all duration-300 shadow-primary hover:shadow-lg hover:-translate-y-1;
    background: linear-gradient(135deg, #005fa3 0%, #14b8a6 100%);
  }

  /* Input styles */
  .input-neumorph {
    @apply bg-gray-50 rounded-lg border border-gray-100 transition-all duration-300 focus:outline-none;
    box-shadow: inset 2px 2px 5px rgba(174, 174, 192, 0.2),
      inset -2px -2px 5px rgba(255, 255, 255, 0.7);
  }

  .input-neumorph:focus {
    box-shadow: inset 3px 3px 7px rgba(174, 174, 192, 0.3),
      inset -3px -3px 7px rgba(255, 255, 255, 0.8);
  }

  /* Scrollbar */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Enhanced animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-fade-in {
    animation: fadeIn 1s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-10px);
    }

    100% {
      transform: translateY(0px);
    }
  }

  @keyframes pulse {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.8;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }

    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideInRight {
    from {
      transform: translateX(20px);
      opacity: 0;
    }

    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}


