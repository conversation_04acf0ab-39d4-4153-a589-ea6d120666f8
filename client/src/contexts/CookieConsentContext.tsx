/**
 * <PERSON><PERSON> Consent Context
 * Provides cookie consent state and management throughout the application
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  CookieConsent,
  CookieConsentState,
  getStoredConsent,
  saveConsent,
  acceptAllCookies,
  rejectNonEssentialCookies,
  DEFAULT_CONSENT,
} from '../utils/cookie-storage';
import { updateAnalyticsConsent } from '../utils/analytics-manager';

interface CookieConsentContextType {
  // Current consent state
  consent: CookieConsentState;
  
  // Banner visibility
  showBanner: boolean;
  showModal: boolean;
  
  // Actions
  acceptAll: () => void;
  rejectNonEssential: () => void;
  updateConsent: (newConsent: Partial<CookieConsent>) => void;
  openSettings: () => void;
  closeSettings: () => void;
  closeBanner: () => void;
  
  // Utilities
  isLoading: boolean;
}

const CookieConsentContext = createContext<CookieConsentContextType | undefined>(undefined);

interface CookieConsentProviderProps {
  children: ReactNode;
}

export function CookieConsentProvider({ children }: CookieConsentProviderProps) {
  const [consent, setConsent] = useState<CookieConsentState>({
    ...DEFAULT_CONSENT,
    hasConsented: false,
    isExpired: false,
  });
  
  const [showBanner, setShowBanner] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize consent state on mount
  useEffect(() => {
    const initializeConsent = () => {
      try {
        // Check if we're in admin mode - don't show cookie banner in admin
        const isAdminRoute = window.location.pathname.startsWith('/admin');

        const storedConsent = getStoredConsent();
        setConsent(storedConsent);

        // Show banner if user hasn't consented or consent is expired, but not in admin
        const shouldShowBanner = !isAdminRoute && (!storedConsent.hasConsented || storedConsent.isExpired);
        setShowBanner(shouldShowBanner);

        if (import.meta.env.DEV) {
          console.log('Cookie consent initialized:', {
            consent: storedConsent,
            showBanner: shouldShowBanner,
            isAdminRoute,
          });
        }
      } catch (error) {
        console.error('Error initializing cookie consent:', error);
        // Fallback to default state
        setConsent({
          ...DEFAULT_CONSENT,
          hasConsented: false,
          isExpired: false,
        });
        // Don't show banner in admin even on error
        const isAdminRoute = window.location.pathname.startsWith('/admin');
        setShowBanner(!isAdminRoute);
      } finally {
        setIsLoading(false);
      }
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(initializeConsent, 100);
    return () => clearTimeout(timer);
  }, []);

  // Accept all cookies
  const acceptAll = () => {
    try {
      const newConsent = acceptAllCookies();
      setConsent({
        ...newConsent,
        hasConsented: true,
        isExpired: false,
      });
      setShowBanner(false);
      setShowModal(false);

      // Update analytics based on new consent
      updateAnalyticsConsent();

      if (import.meta.env.DEV) {
        console.log('All cookies accepted');
      }
    } catch (error) {
      console.error('Error accepting all cookies:', error);
    }
  };

  // Reject non-essential cookies
  const rejectNonEssential = () => {
    try {
      const newConsent = rejectNonEssentialCookies();
      setConsent({
        ...newConsent,
        hasConsented: true,
        isExpired: false,
      });
      setShowBanner(false);
      setShowModal(false);

      // Update analytics based on new consent
      updateAnalyticsConsent();

      if (import.meta.env.DEV) {
        console.log('Non-essential cookies rejected');
      }
    } catch (error) {
      console.error('Error rejecting non-essential cookies:', error);
    }
  };

  // Update specific consent preferences
  const updateConsent = (newConsent: Partial<CookieConsent>) => {
    try {
      const updatedConsent = {
        essential: true, // Always true
        analytics: newConsent.analytics ?? consent.analytics,
        marketing: newConsent.marketing ?? consent.marketing,
        timestamp: Date.now(),
        version: consent.version,
      };

      saveConsent(updatedConsent);
      setConsent({
        ...updatedConsent,
        hasConsented: true,
        isExpired: false,
      });
      setShowBanner(false);
      setShowModal(false);

      // Update analytics based on new consent
      updateAnalyticsConsent();

      if (import.meta.env.DEV) {
        console.log('Cookie consent updated:', updatedConsent);
      }
    } catch (error) {
      console.error('Error updating cookie consent:', error);
    }
  };

  // Open settings modal
  const openSettings = () => {
    setShowModal(true);
  };

  // Close settings modal
  const closeSettings = () => {
    setShowModal(false);
  };

  // Close banner (only allowed after making a choice)
  const closeBanner = () => {
    if (consent.hasConsented) {
      setShowBanner(false);
    }
  };

  const contextValue: CookieConsentContextType = {
    consent,
    showBanner,
    showModal,
    acceptAll,
    rejectNonEssential,
    updateConsent,
    openSettings,
    closeSettings,
    closeBanner,
    isLoading,
  };

  return (
    <CookieConsentContext.Provider value={contextValue}>
      {children}
    </CookieConsentContext.Provider>
  );
}

/**
 * Hook to use cookie consent context
 */
export function useCookieConsent(): CookieConsentContextType {
  const context = useContext(CookieConsentContext);
  if (context === undefined) {
    throw new Error('useCookieConsent must be used within a CookieConsentProvider');
  }
  return context;
}
