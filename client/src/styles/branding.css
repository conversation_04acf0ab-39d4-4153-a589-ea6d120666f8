/**
 * MetaNord Brand Styles
 * 
 * This file contains unified styling for MetaNord branding elements
 * including gradients, buttons, badges, and other themed components.
 */

/* MetaNord Official Brand Gradient Variables */
:root {
  /* Primary gradient - official MetaNord brand gradient */
  --metanord-gradient: linear-gradient(90deg, #2980B9 0%, #3CAEA3 100%);
  --metanord-gradient-hover: linear-gradient(90deg, #3498DB 0%, #4DBFB3 100%);
  --metanord-gradient-active: linear-gradient(90deg, #1F6A93 0%, #2F9A8F 100%);
  
  /* Single colors from the gradient for accents */
  --metanord-teal: #3CAEA3;
  --metanord-blue: #2980B9;
  
  /* Text colors for contrast */
  --metanord-text-dark: #1F2937;
  --metanord-text-light: #F9FAFB;
}

/* MetaNord Button - Primary gradient style */
.metanord-button {
  background: var(--metanord-gradient);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1.25rem;
  border-radius: 0.5rem;
  border: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metanord-button:hover {
  background: var(--metanord-gradient-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.metanord-button:active {
  background: var(--metanord-gradient-active);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* MetaNord Badge - Category and tag styling */
.metanord-badge {
  background: var(--metanord-gradient);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  display: inline-block;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

/* MetaNord Feature Item - For feature lists with checkmarks */
.metanord-feature-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid rgba(229, 231, 235, 0.5);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.metanord-checkmark {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  background: var(--metanord-gradient);
  border-radius: 9999px;
  color: white;
  flex-shrink: 0;
}

/* Tab Navigation Styling */
.tab-active {
  background: linear-gradient(90deg, #2980B9, #3CAEA3);
  color: #ffffff;
  font-weight: 500;
  border-radius: 10px;
}

.tab-inactive {
  background-color: #f1f5f9;
  color: #1e293b;
  border-radius: 10px;
}

.tab-inactive:hover {
  background-color: #e2e8f0;
}

/* Quick Preview Modal Styling */
.quick-preview-modal {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  max-width: 95vw;
  width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  z-index: 9999 !important;
  pointer-events: auto;
}

/* Modal container to ensure proper positioning */
.modal-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
  pointer-events: none;
}

.modal-container > * {
  pointer-events: auto;
}

/* Mobile responsive adjustments */
@media (max-width: 1024px) {
  .quick-preview-modal {
    width: 800px;
    max-width: 90vw !important;
  }
}

@media (max-width: 768px) {
  .quick-preview-modal {
    width: 100% !important;
    max-width: 100vw !important;
    max-height: 95vh !important;
    top: auto !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    border-radius: 1rem 1rem 0 0 !important;
    margin: 0 !important;
  }
}

@media (max-width: 640px) {
  .quick-preview-modal {
    width: 100% !important;
    max-width: 100vw !important;
    max-height: 95vh !important;
    border-radius: 1rem 1rem 0 0 !important;
  }
}

.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 9998 !important;
  pointer-events: auto;
}

.view-details-button {
  background: var(--metanord-gradient);
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  border: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.view-details-button:hover {
  background: var(--metanord-gradient-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.view-details-button:active {
  background: var(--metanord-gradient-active);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* MetaNord Close Button */
.metanord-close-button {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: white;
  border: 1px solid #E5E7EB;
  color: #6B7280;
  padding: 0.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 60;
}

.metanord-close-button:hover {
  background: #F9FAFB;
  color: #4B5563;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.metanord-close-button:active {
  background: #F3F4F6;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transform: scale(0.98);
}