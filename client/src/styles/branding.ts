/**
 * MetaNord Brand Constants
 * 
 * This file contains color constants and gradient definitions
 * for consistent application of brand styling across components.
 */

// Main brand colors
export const COLORS = {
  // Primary colors
  primary: {
    main: '#2D7EB6',      // Primary blue
    light: '#5DA5D5',
    dark: '#1D5E8C',
    gradient: 'from-[#2D7EB6] to-[#40BFB9]',  // MetaNord gradient
  },
  
  // Accent colors
  accent: {
    main: '#40BFB9',      // Teal/cyan accent
    light: '#69D2CE',
    dark: '#2E9995',
  },
  
  // Neutrals
  neutral: {
    lightest: '#F7F9FC',
    light: '#E5E9F0',
    medium: '#C4C9D4',
    dark: '#6E7A94',
    darkest: '#2E3440',
  },
  
  // States
  state: {
    success: '#22C55E',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  }
};

// Gradient presets
export const GRADIENTS = {
  // Main brand gradient for CTAs and primary buttons
  primary: 'bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9]',
  
  // Alternative gradient
  secondary: 'bg-gradient-to-r from-[#40BFB9] to-[#2D7EB6]',
  
  // Gradient hover effects
  hover: {
    primary: 'hover:bg-gradient-to-r hover:from-[#2D7EB6] hover:to-[#40BFB9]',
    secondary: 'hover:bg-gradient-to-r hover:from-[#40BFB9] hover:to-[#2D7EB6]',
  },
};

// Button styles
export const BUTTONS = {
  primary: `${GRADIENTS.primary} text-white font-bold 
            rounded-xl shadow-md transition-all duration-300 
            hover:brightness-105 hover:shadow-lg 
            hover:scale-[1.02] active:scale-[0.98]`,
            
  secondary: 'bg-white text-neutral-800 border border-gray-200 font-medium rounded-xl shadow-sm',
};

// Badge styles
export const BADGES = {
  category: `${GRADIENTS.primary} inline-block px-3 py-1.5 
             text-sm font-semibold text-white rounded-full shadow-md`,
};

// Card styles
export const CARDS = {
  primary: 'bg-white/80 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm',
  feature: 'flex items-start gap-3 p-3 bg-white/80 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm',
};

export default {
  COLORS,
  GRADIENTS,
  BUTTONS,
  BADGES,
  CARDS
};