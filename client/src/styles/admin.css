/* 
 * MetaNord Admin Console - Custom Styling
 * This file contains admin-specific styling that should not affect the public site
 */

/* Admin theme variables are set via JavaScript in admin-theme.ts */
:root[data-admin-theme="light"] {
  color-scheme: light;
}

:root[data-admin-theme="dark"] {
  color-scheme: dark;
}

/* Admin Layout & Structure */
.admin-mode {
  background-color: var(--admin-bg);
  color: var(--admin-text);
}

.admin-dashboard {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: fixed; /* Fix position to prevent scrolling issues */
  top: 0;
  left: 0;
  background-color: var(--admin-bg);
  z-index: 1; /* Ensure admin dashboard is below cookie consent (z-index: 9999) */
}

/* Admin Sidebar */
.admin-sidebar {
  width: 280px;
  height: 100vh;
  background-color: var(--admin-sidebar-bg);
  border-right: 1px solid var(--admin-border);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 20;
  flex-shrink: 0;
}

.admin-sidebar-collapsed {
  width: 60px;
}

.admin-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--admin-border);
}

.admin-sidebar-logo {
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.admin-sidebar-groups {
  display: flex;
  flex-direction: column;
  padding: 0.5rem 0;
}

.admin-sidebar-group {
  margin-bottom: 0.5rem;
}

.admin-sidebar-group-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--admin-text-muted);
  cursor: pointer;
}

.admin-sidebar-group-header:hover {
  background-color: var(--admin-sidebar-hover);
}

.admin-sidebar-group-items {
  overflow: hidden;
  transition: height 0.3s ease;
}

.admin-sidebar-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--admin-text);
  border-radius: 0.25rem;
  margin: 0 0.5rem;
}

.admin-sidebar-item:hover {
  background-color: var(--admin-sidebar-hover);
}

.admin-sidebar-item.active {
  background-color: var(--admin-sidebar-active);
  color: var(--admin-primary);
  font-weight: 500;
  border-right: 3px solid var(--admin-primary);
}

.admin-sidebar-item-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
}

.admin-sidebar-collapsed .admin-sidebar-item-text,
.admin-sidebar-collapsed .admin-sidebar-group-text {
  display: none;
}

.admin-sidebar-collapsed .admin-sidebar-item {
  padding: 0.75rem;
  justify-content: center;
}

.admin-sidebar-collapsed .admin-sidebar-item-icon {
  margin-right: 0;
}

.admin-sidebar-collapsed .admin-sidebar-group-header {
  justify-content: center;
  padding: 0.5rem;
}

/* Admin Main Content Area */
.admin-content-with-sidebar {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.admin-content-with-sidebar.collapsed {
  margin-left: 0;
}

/* Admin Header */
.admin-header {
  background-color: var(--admin-header-bg);
  border-bottom: 1px solid var(--admin-border);
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  height: 64px;
  flex-shrink: 0;
  z-index: 10;
}

.admin-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.admin-header-left {
  display: flex;
  align-items: center;
}

/* Admin Main Content */
.admin-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: var(--admin-bg);
}

.admin-content-container {
  max-width: 1600px;
  margin: 0 auto;
}

.admin-page-header {
  margin-bottom: 2rem;
}

.admin-page-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.admin-page-description {
  color: var(--admin-text-muted);
  font-size: 0.875rem;
}

.admin-actions {
  display: flex;
  gap: 0.5rem;
}

/* Admin Cards & Panels */
.admin-panel {
  background-color: var(--admin-card-bg);
  border: 1px solid var(--admin-border);
  border-radius: 0.5rem;
  box-shadow: var(--admin-panel-shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.admin-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.admin-panel-title {
  font-size: 1.25rem;
  font-weight: 600;
}

/* Admin Data Table */
.admin-data-table-wrapper {
  overflow-x: auto;
}

.admin-data-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-data-table-header th {
  text-align: left;
  padding: 0.75rem 1rem;
  font-weight: 500;
  color: var(--admin-text-muted);
  border-bottom: 1px solid var(--admin-border);
}

.admin-data-table-body td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--admin-border);
}

.admin-data-table-body tr:last-child td {
  border-bottom: none;
}

/* Admin Badges */
.admin-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.admin-badge-primary {
  background-color: rgba(45, 126, 182, 0.1);
  color: var(--admin-primary);
}

.admin-badge-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
}

.admin-badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(245, 158, 11);
}

.admin-badge-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
}

/* MetaNord Admin Branding */
.admin-btn-gradient {
  background: linear-gradient(90deg, #2D7EB6 0%, #40BFB9 100%);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.admin-btn-gradient:hover {
  background: linear-gradient(90deg, #245a8a 0%, #359a95 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(45, 126, 182, 0.3);
}

.admin-header-title {
  color: var(--admin-primary);
  font-weight: 600;
}

.admin-sidebar-logo {
  color: var(--admin-primary);
}

/* MetaNord accent colors for links and highlights */
.admin-link-primary {
  color: var(--admin-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.admin-link-primary:hover {
  color: var(--admin-secondary);
}

/* Admin cards with MetaNord styling */
.admin-card-metanord {
  background: var(--admin-card-bg);
  border: 1px solid var(--admin-border);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(45, 126, 182, 0.08);
  transition: all 0.3s ease;
  color: var(--admin-text);
}

.admin-card-metanord:hover {
  box-shadow: 0 4px 16px rgba(45, 126, 182, 0.12);
  transform: translateY(-2px);
}

/* Dark mode specific overrides */
:root[data-admin-theme="dark"] .admin-card-metanord {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

:root[data-admin-theme="dark"] .admin-card-metanord:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .admin-sidebar {
    width: 240px;
  }

  .admin-content {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .admin-dashboard {
    position: relative;
    height: auto;
    min-height: 100vh;
  }

  .admin-sidebar {
    display: none; /* Hide sidebar on mobile, use mobile nav instead */
  }

  .admin-content-with-sidebar {
    margin-left: 0;
  }

  .admin-content {
    padding: 0.75rem;
  }

  .admin-header {
    padding: 0 1rem;
  }
}

/* Cookie consent integration - ensure admin content doesn't conflict */
.admin-mode .cookie-consent-banner,
.admin-mode .cookie-consent-modal {
  z-index: 10000; /* Higher than admin dashboard */
}

/* Ensure admin dashboard content is properly contained */
.admin-dashboard * {
  box-sizing: border-box;
}

.admin-badge-info {
  background-color: rgba(79, 70, 229, 0.1);
  color: rgb(79, 70, 229);
}

/* Additional dark mode improvements */
:root[data-admin-theme="dark"] {
  /* Ensure proper text contrast in dark mode */
  --admin-text-contrast: #ffffff;
  --admin-input-bg: #334155;
  --admin-input-border: #475569;
}

/* Dark mode input styling */
:root[data-admin-theme="dark"] input,
:root[data-admin-theme="dark"] textarea,
:root[data-admin-theme="dark"] select {
  background-color: var(--admin-input-bg);
  border-color: var(--admin-input-border);
  color: var(--admin-text);
}

/* Dark mode button styling */
:root[data-admin-theme="dark"] .admin-btn-gradient {
  background: linear-gradient(90deg, #3B82F6 0%, #40BFB9 100%);
}

:root[data-admin-theme="dark"] .admin-btn-gradient:hover {
  background: linear-gradient(90deg, #2563EB 0%, #359a95 100%);
}

/* Notification Center Styles */
.notification-center {
  position: relative;
}

.notification-icon {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 9999px;
  transition: all 0.2s ease;
}

.notification-icon:hover {
  background-color: var(--admin-sidebar-hover);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
  border-radius: 9999px;
  font-size: 0.65rem;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  pointer-events: none;
}

.notification-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 360px;
  max-width: calc(100vw - 2rem);
  max-height: 480px;
  background-color: var(--admin-card-bg);
  border: 1px solid var(--admin-border);
  border-radius: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 50;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notification-header {
  padding: 1rem;
  border-bottom: 1px solid var(--admin-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-title {
  font-size: 1rem;
  font-weight: 600;
}

.notification-list {
  overflow-y: auto;
  max-height: 360px;
}

.notification-item {
  padding: 1rem;
  border-bottom: 1px solid var(--admin-border);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: var(--admin-highlight);
}

.notification-item.unread {
  background-color: var(--admin-highlight);
}

.notification-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.notification-item-title {
  font-size: 0.875rem;
  font-weight: 500;
}

.notification-item-description {
  font-size: 0.8125rem;
  color: var(--admin-text-muted);
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--admin-text-muted);
  margin-top: 0.25rem;
}

.notification-footer {
  padding: 0.75rem;
  border-top: 1px solid var(--admin-border);
}

.notification-footer-link {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 0.875rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.notification-footer-link:hover {
  background-color: var(--admin-highlight);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    transform: translateX(-100%);
  }
  
  .admin-sidebar.active {
    transform: translateX(0);
  }
  
  .admin-dashboard {
    grid-template-columns: 1fr;
    grid-template-areas:
      "header"
      "main";
  }
  
  .notification-dropdown {
    width: 320px;
  }
}