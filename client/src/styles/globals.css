@import url("./transitions.css");
@import url("./optimizations.css");
@import url("./faq-optimizations.css");
@import url("./admin.css");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Special styles for language transitions to prevent UI freezing */
.lang-transition {
  opacity: 0.8;
  pointer-events: none;
  transition: opacity 300ms ease;
}

/* Fix dropdown menu position and style for mobile */
.menu-dropdown {
  position: absolute;
  z-index: 50;
  background: white;
  border: 1px solid rgba(229, 231, 235, 0.8);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* Fix dropdown menu content style */
.menu-dropdown-content {
  padding: 0.5rem 0;
  min-width: 10rem;
}

.menu-dropdown-content a {
  position: relative;
  transition: all 0.3s ease;
}

.menu-dropdown-content a:after {
  content: '';
  position: absolute;
  width: 0;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.menu-dropdown-content a:hover:after {
  width: 100%;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 225 15% 16%;
    --muted: 220 14% 96%;
    --muted-foreground: 220 8% 46%;
    --popover: 0 0% 100%;
    --popover-foreground: 225 15% 16%;
    --card: 0 0% 100%;
    --card-foreground: 225 15% 16%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --primary: 210 100% 36%;
    --primary-foreground: 210 40% 98%;
    --secondary: 220 9% 46%;
    --secondary-foreground: 210 40% 98%;
    --accent: 174 84% 43%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --ring: 210 100% 36%;
    --radius: 0.75rem;

    /* Chart colors */
    --chart-1: 210 100% 36%;
    --chart-2: 174 84% 43%;
    --chart-3: 220 9% 46%;
    --chart-4: 210 90% 54%;
    --chart-5: 174 76% 60%;

    /* Sidebar */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 225 15% 16%;
    --sidebar-primary: 210 100% 36%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 174 84% 43%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 210 100% 36%;
  }

  body {
    @apply bg-background text-foreground font-roboto antialiased;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-inter font-bold;
  }
}

@layer components {

  /* Glassmorphism effects */
  .glass-effect {
    @apply bg-white/20 dark:bg-gray-900/30 backdrop-blur-md border border-white/30 dark:border-gray-600/30 shadow-lg rounded-2xl;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .dark .glass-effect {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .glass-card {
    @apply bg-white/20 dark:bg-gray-900/30 backdrop-blur-lg border border-white/40 dark:border-gray-600/30 shadow-xl rounded-xl transition-all duration-300;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
  }

  .dark .glass-card {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  }

  .glass-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 80%);
    transform: rotate(30deg);
    pointer-events: none;
  }

  .dark .glass-card::before {
    background: radial-gradient(circle, rgba(80, 80, 80, 0.1) 0%, rgba(30, 30, 30, 0) 80%);
  }

  .glass-nav {
    @apply bg-white/20 dark:bg-gray-900/30 backdrop-blur-xl border-b border-white/20 dark:border-gray-600/30 shadow-md;
    backdrop-filter: blur(10px);
  }

  .dark .glass-nav {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }

  /* Enhanced Neumorphic UI elements */
  .neumorph {
    @apply bg-white dark:bg-gray-800 rounded-xl transition-all duration-300;
    box-shadow: 10px 10px 20px rgba(174, 174, 192, 0.45),
      -10px -10px 20px rgba(255, 255, 255, 0.9);
    position: relative;
    z-index: 1;
  }

  .dark .neumorph {
    box-shadow: 10px 10px 20px rgba(0, 0, 0, 0.5),
      -10px -10px 20px rgba(40, 40, 45, 0.3);
  }

  .neumorph:hover {
    transform: scale(1.02);
    box-shadow: 12px 12px 24px rgba(174, 174, 192, 0.5),
      -12px -12px 24px rgba(255, 255, 255, 0.95);
  }

  .dark .neumorph:hover {
    box-shadow: 12px 12px 24px rgba(0, 0, 0, 0.6),
      -12px -12px 24px rgba(40, 40, 45, 0.4);
  }

  .neumorph-inset {
    @apply bg-gray-50 dark:bg-gray-800 rounded-xl transition-all duration-300;
    box-shadow: inset 6px 6px 12px rgba(174, 174, 192, 0.2),
      inset -6px -6px 12px rgba(255, 255, 255, 0.7);
  }

  .dark .neumorph-inset {
    box-shadow: inset 6px 6px 12px rgba(0, 0, 0, 0.4),
      inset -6px -6px 12px rgba(40, 40, 45, 0.2);
  }

  .neumorph-btn {
    @apply bg-white dark:bg-gray-800 rounded-xl transition-all duration-300;
    box-shadow: 5px 5px 10px rgba(174, 174, 192, 0.4),
      -5px -5px 10px rgba(255, 255, 255, 0.8);
  }

  .dark .neumorph-btn {
    box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.5),
      -5px -5px 10px rgba(40, 40, 45, 0.3);
  }

  .neumorph-btn:hover {
    box-shadow: 7px 7px 14px rgba(174, 174, 192, 0.4),
      -7px -7px 14px rgba(255, 255, 255, 0.8);
  }

  .dark .neumorph-btn:hover {
    box-shadow: 7px 7px 14px rgba(0, 0, 0, 0.6),
      -7px -7px 14px rgba(40, 40, 45, 0.4);
  }

  .neumorph-btn:active {
    @apply bg-gray-50 dark:bg-gray-900;
    box-shadow: inset 5px 5px 10px rgba(174, 174, 192, 0.4),
      inset -5px -5px 10px rgba(255, 255, 255, 0.8);
  }

  .dark .neumorph-btn:active {
    box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.5),
      inset -5px -5px 10px rgba(40, 40, 45, 0.2);
  }

  /* Enhanced Duotone gradients */
  .gradient-blue {
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
  }

  .gradient-teal {
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
  }

  .gradient-primary {
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
    box-shadow: 0 10px 25px -5px rgba(45, 126, 182, 0.4);
  }

  .gradient-accent {
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
    box-shadow: 0 10px 25px -5px rgba(45, 126, 182, 0.4);
  }

  .gradient-dark {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }

  .gradient-nordic {
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
    box-shadow: 0 10px 25px -5px rgba(45, 126, 182, 0.4);
  }

  .gradient-frost {
    background: linear-gradient(135deg, #cbd5e1 0%, #f8fafc 100%);
    box-shadow: 0 10px 25px -5px rgba(203, 213, 225, 0.4);
  }

  /* Text effects */
  .text-gradient {
    @apply bg-clip-text text-transparent;
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
  }

  .text-gradient-blue {
    @apply bg-clip-text text-transparent;
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
  }

  /* Button styles */
  .btn-primary {
    @apply text-white font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
    box-shadow: 0 10px 25px -5px rgba(45, 126, 182, 0.3);
  }

  .btn-gradient {
    @apply text-white font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
    background: linear-gradient(90deg, #2D7EB6, #40BFB9);
    box-shadow: 0 10px 25px -5px rgba(45, 126, 182, 0.3);
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #40BFB9, #2D7EB6);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease-out;
  }

  .btn-gradient:hover::before {
    opacity: 1;
  }

  .btn-gradient-secondary {
    @apply bg-white font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 10px 25px -5px rgba(0, 95, 163, 0.15);
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .btn-gradient-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease-out;
  }

  .btn-gradient-secondary:hover::before {
    opacity: 1;
  }
}

@layer utilities {

  /* Font families */
  .font-inter {
    font-family: 'Inter', sans-serif;
  }

  .font-roboto {
    font-family: 'Roboto', sans-serif;
  }

  /* Text gradient for titles */
  .text-gradient {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    background-size: 100%;
    background-position: 0%;
  }

  /* Transitions */
  .transition-300 {
    @apply transition-all duration-300 ease-in-out;
  }

  .transition-500 {
    @apply transition-all duration-500 ease-in-out;
  }

  /* Enhanced Micro-animations & hover effects */
  .hover-scale {
    @apply transition-all duration-300 ease-out;
    transform-origin: center;
  }

  .hover-scale:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  }

  .hover-scale-sm {
    @apply transition-all duration-300 ease-out;
  }

  .hover-scale-sm:hover {
    transform: scale(1.02);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  }

  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .hover-glow:hover {
    box-shadow: 0 0 25px rgba(64, 191, 185, 0.5);
    transform: scale(1.02);
  }

  .hover-bounce {
    @apply transition-all duration-300 ease-out;
  }

  .hover-bounce:hover {
    animation: bounceSlight 0.6s ease-in-out;
  }

  /* Enhanced shadows */
  .shadow-soft {
    box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
  }

  .shadow-card {
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.1);
  }

  .shadow-accent {
    box-shadow: 0 10px 25px -5px rgba(64, 191, 185, 0.3);
  }

  .shadow-primary {
    box-shadow: 0 10px 25px -5px rgba(45, 126, 182, 0.3);
  }

  /* Border gradients */
  .border-gradient {
    border-width: 2px;
    border-style: solid;
    border-image: linear-gradient(90deg, #2D7EB6, #40BFB9) 1;
  }

  /* Input styles */
  .input-neumorph {
    @apply bg-gray-50 rounded-lg border border-gray-100 transition-all duration-300 focus:outline-none;
    box-shadow: inset 2px 2px 5px rgba(174, 174, 192, 0.2),
      inset -2px -2px 5px rgba(255, 255, 255, 0.7);
  }

  .input-neumorph:focus {
    box-shadow: inset 3px 3px 7px rgba(174, 174, 192, 0.3),
      inset -3px -3px 7px rgba(255, 255, 255, 0.8);
  }

  .neumorph-input {
    @apply bg-white/80 backdrop-blur-sm rounded-lg transition-all duration-300 focus:ring-2 focus:ring-accent/30 focus:shadow-accent/20;
    box-shadow: inset 2px 2px 5px rgba(174, 174, 192, 0.15),
      inset -2px -2px 5px rgba(255, 255, 255, 0.7);
  }

  .glass-input {
    @apply backdrop-blur-sm bg-white/70 border-white/30;
  }

  .shadow-glow {
    box-shadow: 0 0 15px rgba(64, 191, 185, 0.3);
  }

  .shadow-inner {
    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  }

  /* Scrollbar */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Mobile optimization utility classes */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-p {
    padding: 0.75rem 1rem;
  }

  @media (min-width: 640px) {
    .mobile-p {
      padding: 1rem 1.5rem;
    }
  }

  .mobile-mt {
    margin-top: 1.5rem;
  }

  @media (min-width: 640px) {
    .mobile-mt {
      margin-top: 2.5rem;
    }
  }

  .mobile-mb {
    margin-bottom: 1.5rem;
  }

  @media (min-width: 640px) {
    .mobile-mb {
      margin-bottom: 2.5rem;
    }
  }

  .mobile-gap {
    gap: 0.75rem;
  }

  @media (min-width: 640px) {
    .mobile-gap {
      gap: 1.5rem;
    }
  }

  /* Enhanced animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-fade-in {
    animation: fadeIn 1s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .animate-shimmer {
    background-size: 400% 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 25%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%);
    animation: shimmer 2.5s infinite;
    background-size: 400% 100%;
  }

  .animate-bounce-subtle {
    animation: bounceSlight 3s ease-in-out infinite;
  }

  .animate-rotate-slow {
    animation: rotateSlow 12s linear infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes bounceSlight {

    0%,
    100% {
      transform: translateY(0);
    }

    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes rotateSlow {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-10px);
    }

    100% {
      transform: translateY(0px);
    }
  }

  @keyframes pulse {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.8;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }

    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideInRight {
    from {
      transform: translateX(20px);
      opacity: 0;
    }

    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

/* Enhanced mobile-first design optimizations */
@media (max-width: 768px) {

  /* Enhanced touch targets for mobile */
  button,
  a.btn,
  .btn,
  input[type=button],
  input[type=submit] {
    @apply min-h-[44px] px-4;
  }

  /* Improved form fields for mobile */
  input,
  select,
  textarea {
    @apply text-base px-3 py-2.5 mb-1;
  }

  /* Better spacing for mobile content */
  .section {
    @apply py-8;
  }

  /* Adjusted card layouts for mobile */
  .card,
  .glass-card {
    @apply rounded-lg;
  }

  /* Ensure proper scrolling behavior on mobile */
  .overflow-auto,
  .overflow-y-auto {
    @apply overscroll-contain;
    -webkit-overflow-scrolling: touch;
  }

  /* Better tap state feedback */
  .tap-highlight {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Fix for iOS momentum scrolling */
  .momentum-scroll {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zooming on inputs in iOS */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  select,
  textarea {
    font-size: 16px;
  }

  /* Ensure buttons show active state on mobile */
  button:active,
  a:active {
    @apply opacity-80;
  }

  /* Mobile-optimized containers */
  .mobile-container {
    @apply px-3 sm:px-4 md:px-5;
  }

  /* Mobile optimization utilities */

  /* Optimize nav items for touch */
  .nav-item {
    @apply py-2.5 px-4;
  }

  /* Mobile menu height optimization */
  .mobile-menu-optimized {
    max-height: 65vh !important;
  }

  /* Product detail mobile hero optimization */
  .product-hero-mobile {
    margin-top: 44px;
    min-height: 220px;
  }

  /* Ensure hero content is visible on mobile */
  .hero-content-mobile {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

/* Special small mobile optimizations */
@media (max-width: 380px) {

  /* Extra compact UI for very small devices */
  .container {
    @apply px-3;
  }

  .xs-compact {
    @apply text-sm p-2;
  }

  h1 {
    @apply text-xl;
  }

  h2 {
    @apply text-lg;
  }

  /* Even more compact mobile menu for very small screens */
  .mobile-menu-optimized {
    max-height: 60vh !important;
  }

  /* Smaller hero section for very small screens */
  .product-hero-mobile {
    min-height: 180px;
  }
}

/* MOBILE LOGO BUG FIX: Prevent product detail page from overriding logo size */
img[data-logo="true"] {
  /* Protect against inline style overrides from product detail page */
  object-fit: contain !important;
}

/* Ensure footer logos maintain responsive sizing */
footer img[data-logo="true"] {
  width: 100% !important;
  height: auto !important;
}



