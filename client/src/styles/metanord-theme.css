/**
 * MetaNord Global Theme Styling
 * 
 * This file contains global styles that ensure consistency
 * across the MetaNord website according to brand guidelines.
 */

/* Primary Gradient Button - used for main call-to-action buttons */
.button-primary-gradient {
  @apply text-white font-bold 
         rounded-xl shadow-md
         transition-all duration-300
         hover:brightness-105 hover:shadow-lg
         hover:scale-[1.02] active:scale-[0.98];
  background: linear-gradient(90deg, #2980B9 0%, #3CAEA3 100%);
}

/* Category Badge - used for product category labels */
.category-badge {
  @apply inline-block px-3 py-1.5 
         text-sm font-semibold 
         text-white rounded-full 
         shadow-md;
  background: linear-gradient(90deg, #2980B9 0%, #3CAEA3 100%);
}

/* Tab Active State - used for active tabs in product views */
.tab-active {
  background: linear-gradient(90deg, #2980B9 0%, #3CAEA3 100%);
  color: white;
  font-weight: 500;
}

/* Tab Inactive State - used for inactive tabs */
.tab-inactive {
  background-color: #f1f5f9;
  color: #1e293b;
  @apply hover:bg-gray-100 active:bg-gray-200;
}

/* Feature Item - styled checkmark item for product features */
.feature-item {
  @apply flex items-start gap-3 p-3 
         bg-white/80 backdrop-blur-sm 
         rounded-xl border border-white/40 
         shadow-sm;
}

/* Feature Checkmark - styled checkmark icon container */
.feature-checkmark {
  @apply flex-shrink-0 w-6 h-6 
         rounded-full 
         bg-gradient-to-r from-cyan-500/20 to-teal-500/20 
         flex items-center justify-center mt-0.5;
}

/* Application Item - styled chevron item for applications */
.application-item {
  @apply flex items-start gap-3 p-3 
         bg-white/80 backdrop-blur-sm 
         rounded-xl border border-white/40 
         shadow-sm;
}

/* Specification Row - styled row for specification pairs */
.specification-row {
  @apply flex flex-col xs:flex-row 
         border-b border-neutral-100/60 
         pb-2.5 xs:pb-3 
         last:border-0 last:pb-0;
}

/* Specification Label - styled label for specification keys */
.specification-label {
  @apply font-medium text-cyan-600 
         w-full xs:w-1/3 
         text-sm xs:text-base mb-1 xs:mb-0;
}

/* Specification Value - styled value for specification values */
.specification-value {
  @apply text-neutral-800 font-roboto 
         w-full xs:w-2/3 
         text-sm xs:text-base break-words;
}