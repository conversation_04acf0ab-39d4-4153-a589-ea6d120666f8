import { createRoot } from "react-dom/client";
import { Suspense } from "react";
import App from "./App";
import "./styles/globals.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { TooltipProvider } from "./components/ui/tooltip";
import { Toaster } from "./components/ui/toaster";
import { Loader } from "./components/ui/loader";
import ErrorBoundary from "./components/ui/error-boundary";

// Import i18n configuration
import "./i18n";

// Loader component for when translations are being loaded
const I18nFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <Loader size="lg" variant="accent" text="Loading translations..." />
  </div>
);

console.log('🚀 Starting MetaNord application...');

const rootElement = document.getElementById("root");

if (rootElement) {
  console.log('✅ Root element found, creating React root...');
  try {
    const root = createRoot(rootElement);
    console.log('✅ React root created, rendering full app...');

    root.render(
      <ErrorBoundary>
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <Suspense fallback={<I18nFallback />}>
              <App />
              <Toaster />
            </Suspense>
          </TooltipProvider>
        </QueryClientProvider>
      </ErrorBoundary>
    );

    console.log('🎉 MetaNord app rendered successfully!');
  } catch (error) {
    console.error('❌ Error rendering app:', error);
    rootElement.innerHTML = `
      <div style="padding: 20px; font-family: Arial, sans-serif; background: white; min-height: 100vh;">
        <h1 style="color: #dc3545;">MetaNord - Error</h1>
        <p>Application failed to load. Error: ${error instanceof Error ? error.message : 'Unknown error'}</p>
        <button onclick="location.reload()" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Reload Page
        </button>
      </div>
    `;
  }
} else {
  console.error('❌ Root element not found!');
}
