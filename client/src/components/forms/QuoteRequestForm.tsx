import { useState } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "../../hooks/use-toast";
import { apiRequest } from "../../lib/queryClient";
// Define quote request schema directly for client-side build
const insertQuoteRequestSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Valid email is required"),
  company: z.string().optional(),
  phone: z.string().optional(),
  productId: z.string().optional(),
  productName: z.string().optional(),
  quantity: z.string().min(1, "Quantity is required"),
  comment: z.string().min(1, "Comment is required"),
  status: z.string().default("new")
});
import { useTranslation } from "react-i18next";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Button } from "../ui/button";
import { Loader2 } from "lucide-react";

// Extend the schema with validation
const formSchema = insertQuoteRequestSchema.extend({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  quantity: z.string().optional(),
});

interface QuoteRequestFormProps {
  productId?: string;
  productName?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function QuoteRequestForm({ productId, productName, onSuccess, onCancel }: QuoteRequestFormProps) {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      phone: "",
      productId: productId || "",
      productName: productName || "",
      quantity: "",
      comment: "",
      status: "new",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);
    
    try {
      const response = await apiRequest("POST", "/api/quote", values);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to submit quote request");
      }
      
      toast({
        title: t("quoteRequest.success.title", "Quote Request Submitted"),
        description: t("quoteRequest.success.description", "We'll get back to you with a quotation soon."),
      });
      
      form.reset();
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Quote request error:", error);
      toast({
        title: t("quoteRequest.error.title", "Submission Failed"),
        description: error instanceof Error 
          ? error.message 
          : t("quoteRequest.error.generic", "Failed to submit quote request. Please try again."),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel className="font-semibold text-gray-700 text-sm mb-2">
                  {t("quoteRequest.form.name", "Your Name")} *
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("quoteRequest.form.namePlaceholder", "Enter your full name")}
                    className="border border-gray-300 focus:ring-2 focus:ring-[#2D7EB6] focus:border-[#2D7EB6] rounded-lg px-4 py-3 bg-white text-gray-900"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel className="font-semibold text-gray-700 text-sm mb-2">
                  {t("quoteRequest.form.email", "Email Address")} *
                </FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={t("quoteRequest.form.emailPlaceholder", "Enter your email")}
                    className="border border-gray-300 focus:ring-2 focus:ring-[#2D7EB6] focus:border-[#2D7EB6] rounded-lg px-4 py-3 bg-white text-gray-900"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="company"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel className="font-semibold text-gray-700 text-sm mb-2">
                  {t("quoteRequest.form.company", "Company Name")}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("quoteRequest.form.companyPlaceholder", "Enter your company name")}
                    className="border border-gray-300 focus:ring-2 focus:ring-[#2D7EB6] focus:border-[#2D7EB6] rounded-lg px-4 py-3 bg-white text-gray-900"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel className="font-semibold text-gray-700 text-sm mb-2">
                  {t("quoteRequest.form.phone", "Phone Number")}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("quoteRequest.form.phonePlaceholder", "Enter your phone number")}
                    className="border border-gray-300 focus:ring-2 focus:ring-[#2D7EB6] focus:border-[#2D7EB6] rounded-lg px-4 py-3 bg-white text-gray-900"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="productName"
          render={({ field }) => (
            <FormItem className="mb-4">
              <FormLabel className="font-semibold text-gray-700 text-sm mb-2">
                {t("quoteRequest.form.product", "Product")} *
              </FormLabel>
              <FormControl>
                <Input
                  className="border border-gray-300 focus:ring-2 focus:ring-[#2D7EB6] focus:border-[#2D7EB6] rounded-lg px-4 py-3 bg-white text-gray-900"
                  {...field}
                  readOnly={!!productName}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="quantity"
          render={({ field }) => (
            <FormItem className="mb-4">
              <FormLabel className="font-semibold text-gray-700 text-sm mb-2">
                {t("quoteRequest.form.quantity", "Quantity")}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("quoteRequest.form.quantityPlaceholder", "Enter quantity needed")}
                  className="border border-gray-300 focus:ring-2 focus:ring-[#2D7EB6] focus:border-[#2D7EB6] rounded-lg px-4 py-3 bg-white text-gray-900"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="comment"
          render={({ field }) => (
            <FormItem className="mb-4">
              <FormLabel className="font-semibold text-gray-700 text-sm mb-2">
                {t("quoteRequest.form.comments", "Additional Comments")}
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t("quoteRequest.form.commentsPlaceholder", "Enter any specific requirements or questions")}
                  className="min-h-[120px] border border-gray-300 focus:ring-2 focus:ring-[#2D7EB6] focus:border-[#2D7EB6] rounded-lg px-4 py-3 bg-white text-gray-900 resize-none"
                  rows={4}
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg px-6 py-3 font-medium transition-all duration-300"
            >
              {t("common.cancel", "Cancel")}
            </Button>
          )}

          <Button
            type="submit"
            disabled={isSubmitting}
            className="btn-gradient text-white font-semibold px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("common.submitting", "Submitting...")}
              </>
            ) : (
              t("quoteRequest.form.submit", "Request Quote")
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}