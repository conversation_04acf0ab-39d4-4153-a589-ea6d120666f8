import { useState } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "../../hooks/use-toast";
import { apiRequest } from "../../lib/queryClient";
import { useTranslation } from "react-i18next";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Button } from "../ui/button";

// Contact form schema
const insertContactInquirySchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Valid email is required"),
  company: z.string().optional(),
  phone: z.string().optional(),
  subject: z.string().optional(),
  message: z.string().min(1, "Message is required"),
  productInterest: z.string().optional(),
  inquiryType: z.string().optional()
});

const formSchema = insertContactInquirySchema.extend({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  message: z.string().min(10, { message: "Message must be at least 10 characters" }),
});

type FormValues = z.infer<typeof formSchema>;

export function ContactForm({ labelColor = "#40BFB9" }: { labelColor?: string }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      phone: "",
      message: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      const response = await apiRequest("POST", "/api/contact", data);
      const result = await response.json();

      if (response.ok) {
        if (result.emailSent === false && result.emailError) {
          toast({
            title: t("contact.form.success"),
            description: "Your message was saved successfully, but we couldn't send an email confirmation. Our team will contact you soon.",
            variant: "default",
          });
        } else {
          toast({
            title: t("contact.form.success"),
            description: t("contact.form.successDescription", "Thank you for your message. We've received your inquiry and will respond shortly."),
            variant: "default",
          });
        }
        form.reset();
      } else {
        let errorMessage = t("contact.form.error");
        if (result.message) errorMessage = result.message;
        toast({
          title: t("contact.form.error"),
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: t("contact.form.error"),
        description: "We couldn't connect to our servers. Please try again later or contact us directly.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="glass-card bg-white/90 backdrop-blur-lg p-4 sm:p-6 md:p-8 rounded-xl border border-white/40 shadow-soft neumorph">
        {/* Name */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="mb-4 sm:mb-6">
              <FormLabel style={{ color: labelColor }} className="font-semibold text-base mb-0.5">
                {t("contact.form.name", "Name")}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("contact.form.namePlaceholder", "Enter your name")}
                  {...field}
                  className="neumorph-input glass-input border-white/30 focus:border-accent/40 rounded-lg shadow-inner focus:shadow-glow text-sm sm:text-base h-9 sm:h-10"
                />
              </FormControl>
              <FormMessage className="text-xs sm:text-sm" />
            </FormItem>
          )}
        />

        {/* Company */}
        <FormField
          control={form.control}
          name="company"
          render={({ field }) => (
            <FormItem className="mb-4 sm:mb-6">
              <FormLabel style={{ color: labelColor }} className="font-semibold text-base mb-0.5">
                {t("contact.form.company", "Company")}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("contact.form.companyPlaceholder", "Enter your company name (optional)")}
                  {...field}
                  className="neumorph-input glass-input border-white/30 focus:border-accent/40 rounded-lg shadow-inner focus:shadow-glow text-sm sm:text-base h-9 sm:h-10"
                />
              </FormControl>
              <FormMessage className="text-xs sm:text-sm" />
            </FormItem>
          )}
        />

        {/* Email */}
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="mb-4 sm:mb-6">
              <FormLabel style={{ color: labelColor }} className="font-semibold text-base mb-0.5">
                {t("contact.form.email", "Email")}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("contact.form.emailPlaceholder", "Enter your email address")}
                  {...field}
                  className="neumorph-input glass-input border-white/30 focus:border-accent/40 rounded-lg shadow-inner focus:shadow-glow text-sm sm:text-base h-9 sm:h-10"
                />
              </FormControl>
              <FormMessage className="text-xs sm:text-sm" />
            </FormItem>
          )}
        />

        {/* Phone */}
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="mb-4 sm:mb-6">
              <FormLabel style={{ color: labelColor }} className="font-semibold text-base mb-0.5">
                {t("contact.form.phone", "Phone")}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("contact.form.phonePlaceholder", "Enter your phone number")}
                  {...field}
                  className="neumorph-input glass-input border-white/30 focus:border-accent/40 rounded-lg shadow-inner focus:shadow-glow text-sm sm:text-base h-9 sm:h-10"
                />
              </FormControl>
              <FormMessage className="text-xs sm:text-sm" />
            </FormItem>
          )}
        />

        {/* Message */}
        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem className="mb-5 sm:mb-6">
              <FormLabel style={{ color: labelColor }} className="font-semibold text-base mb-0.5">
                {t("contact.form.message", "Message")}
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t("contact.form.messagePlaceholder", "Write your message here...")}
                  className="resize-none neumorph-input glass-input border-white/30 focus:border-accent/40 rounded-lg shadow-inner focus:shadow-glow text-sm sm:text-base"
                  rows={4}
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-xs sm:text-sm" />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full btn-gradient text-white hover-lift hover-glow shadow-accent px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg border border-white/20 font-medium transition-all duration-300 group relative overflow-hidden text-sm sm:text-base"
          disabled={isSubmitting}
        >
          <div className="absolute inset-0 animate-shimmer opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
          {isSubmitting ? t("contact.form.sending") || "Sending..." : t("contact.form.submit") || "Send Message"}
        </Button>
      </form>
    </Form>
  );
}
