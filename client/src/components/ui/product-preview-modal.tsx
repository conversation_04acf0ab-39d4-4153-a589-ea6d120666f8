import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Check, ExternalLink } from 'lucide-react';
import { Link } from 'wouter';
import { useTranslation } from 'react-i18next';
import i18n from '../../i18n';
import { useAllProducts, ProductData } from '../../hooks/use-product-api';

interface ProductPreviewData {
  slug: string;
  title: string;
  description: string;
  image: string;
  category: string;
  features?: string[];
  applications?: string[];
  specifications?: Record<string, string>;
  link?: string;
  status?: string;
  documents?: any[];
}

interface ProductPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: ProductPreviewData;
}

export const ProductPreviewModal: React.FC<ProductPreviewModalProps> = ({
  isOpen,
  onClose,
  product
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'features' | 'applications' | 'specifications'>('features');
  const [isMounted, setIsMounted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load all products through the API hook
  const { data: products, isLoading: isProductsLoading } = useAllProducts();

  // Find the detailed product from the array (fallback to props.product)
  const [detailedProduct, setDetailedProduct] = useState<ProductData | null>(null);

  useEffect(() => {
    if (product?.slug) {
      setIsLoading(true);
      
      setTimeout(() => {
        // Try to find by slug first, then by productId
        let found: ProductData | undefined = products?.find(p => p.slug === product.slug || p.productId === product.slug);
        
        if (found) {
          setDetailedProduct(found);
        } else {
          // Fallback to data from props - create a ProductData compatible object
          setDetailedProduct({
            slug: product.slug,
            productId: product.slug, // Use slug as productId fallback
            title: product.title || '',
            description: product.description || '',
            image: product.image || '',
            category: product.category || 'other',
            link: product.link || `/products/${product.slug}`,
            features: product.features || [],
            applications: product.applications || [],
            specifications: product.specifications || {},
            status: 'available',
            documents: [],
            language: i18n.language || 'en'
          });
        }
        setIsLoading(false);
      }, 200);
    }
  }, [product?.slug, i18n.language, products, product]);

  const displayProduct = detailedProduct || product;

  useEffect(() => {
    if (isOpen) {
      setIsMounted(true);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
      // Delay unmounting to allow exit animation
      const timer = setTimeout(() => setIsMounted(false), 300);
      return () => clearTimeout(timer);
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle keyboard events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  useEffect(() => {
    if (isOpen && typeof window !== 'undefined') {
      setTimeout(() => {
        try {
          const modalElement = document.querySelector('.quick-preview-modal');
          if (modalElement && modalElement instanceof HTMLElement) {
            modalElement.focus();
          }
        } catch (error) {}
      }, 50);
    }
  }, [isOpen]);

  // Render if open OR if mounted (for exit animation)
  if (!isOpen && !isMounted) return null;

  // Animation variants
  const backdrop = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  };

  // Desktop: centered modal, Mobile: slide up from bottom
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  const modal = {
    hidden: {
      opacity: 0,
      scale: 0.95,
      y: isMobile ? '100%' : 20
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: isMobile ? '100%' : 20
    },
  };

  // Render modal using React Portal to ensure it's at the document root
  const modalContent = (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm p-4"
          variants={backdrop}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          {/* Modal Content */}
          <motion.div
            className="bg-white rounded-xl shadow-xl max-w-4xl w-full p-4 sm:p-6 relative max-h-[90vh] overflow-y-auto"
            variants={modal}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
            onKeyDown={handleKeyDown}
            tabIndex={0}
          >
            {/* Close button */}
            <div className="absolute top-4 right-4 z-10">
              <button
                className="shadow-lg border border-gray-200 bg-white text-gray-600 hover:text-gray-800 transition-all duration-300 p-2 rounded-full hover:bg-gray-50 flex-shrink-0 hover:scale-105 active:scale-95"
                onClick={(e) => {
                  e.stopPropagation();
                  onClose();
                }}
                aria-label={t('common.close', 'Close')}
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Loading state overlay */}
            {(isLoading || isProductsLoading) && (
              <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10 rounded-xl">
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 border-4 border-[#2D7EB6] border-t-transparent rounded-full animate-spin"></div>
                  <p className="mt-3 text-gray-600 font-medium">{t('common.loading', 'Loading...')}</p>
                </div>
              </div>
            )}

            {/* Modal Content - padding already applied to container */}
            <div>
              {/* Main Content Layout - Side by Side */}
              <div className="flex flex-col md:flex-row gap-6 md:gap-8 mt-4">
                {/* Product Image - Left Side */}
                <div className="md:w-1/2">
                  <div className="bg-gray-50 p-4 rounded-xl border border-gray-100 shadow-sm w-full">
                    <img
                      src={
                        displayProduct.image?.startsWith('/images/products/')
                          ? displayProduct.image.replace('/images/products/', '/images/products/')
                          : displayProduct.image?.startsWith('/')
                            ? displayProduct.image
                            : `/${displayProduct.image}`
                      }
                      alt={displayProduct.title}
                      className="w-full object-contain rounded-md shadow-sm max-h-[300px] md:max-h-[400px]"
                      onError={(e) => {
                        if (product.image && product.image !== displayProduct.image) {
                          const fixedPath = product.image.startsWith('/images/products/')
                            ? product.image.replace('/images/products/', '/images/products/')
                            : product.image.startsWith('/')
                              ? product.image
                              : `/${product.image}`;
                          e.currentTarget.src = fixedPath;
                        }
                      }}
                    />
                  </div>
                </div>

                {/* Content Area - Right Side */}
                <div className="md:w-1/2 space-y-6">
                  {/* Product Info Header */}
                  <div>
                    <span
                      className="inline-block px-3 py-1.5 text-sm font-semibold rounded-full border shadow-sm mb-3"
                      style={{
                        backgroundColor: "white",
                        color: "#2D7EB6",
                        border: "1px solid #2D7EB6"
                      }}
                    >
                      {t(`products.categories.${displayProduct.category?.toLowerCase().replace(/\s+/g, '_')}`, displayProduct.category)}
                    </span>
                    <h2 className="text-xl md:text-2xl font-bold mb-3">
                      {displayProduct.title}
                    </h2>
                    <div
                      className="prose prose-neutral max-w-none mb-6 text-sm md:text-base"
                      dangerouslySetInnerHTML={{
                        __html: displayProduct.description || "",
                      }}
                    />
                  </div>

                  {/* Tab navigation */}
                  <div className="flex gap-1 mb-4 bg-gray-50 p-1 rounded-lg">
                    <button
                      className="flex-1 px-3 py-2 text-sm font-medium transition-all duration-300 rounded-md"
                      style={{
                        background: activeTab === 'features' ? 'linear-gradient(90deg, #2D7EB6, #40BFB9)' : 'transparent',
                        color: activeTab === 'features' ? '#ffffff' : '#1e293b'
                      }}
                      onClick={() => setActiveTab('features')}
                    >
                      {t('products.features', 'Features')}
                    </button>
                    <button
                      className="flex-1 px-3 py-2 text-sm font-medium transition-all duration-300 rounded-md"
                      style={{
                        background: activeTab === 'applications' ? 'linear-gradient(90deg, #2D7EB6, #40BFB9)' : 'transparent',
                        color: activeTab === 'applications' ? '#ffffff' : '#1e293b'
                      }}
                      onClick={() => setActiveTab('applications')}
                    >
                      {t('products.applications', 'Applications')}
                    </button>
                    <button
                      className="flex-1 px-3 py-2 text-sm font-medium transition-all duration-300 rounded-md"
                      style={{
                        background: activeTab === 'specifications' ? 'linear-gradient(90deg, #2D7EB6, #40BFB9)' : 'transparent',
                        color: activeTab === 'specifications' ? '#ffffff' : '#1e293b'
                      }}
                      onClick={() => setActiveTab('specifications')}
                    >
                      {t('products.specifications', 'Specifications')}
                    </button>
                  </div>

                  {/* Tab Content */}
                  <div className="max-h-64 overflow-y-auto mb-6">
                    {activeTab === 'features' && displayProduct.features && displayProduct.features.length > 0 && (
                      <ul className="space-y-3">
                        {displayProduct.features.map((feature, index) => (
                          <li
                            key={index}
                            className="flex items-start gap-3 p-3 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-100 shadow-sm"
                          >
                            <span className="flex-shrink-0">
                              <Check className="w-5 h-5" style={{ color: "#2980B9" }} />
                            </span>
                            <span className="text-neutral-800 font-roboto text-sm">
                              {feature}
                            </span>
                          </li>
                        ))}
                      </ul>
                    )}
                    {activeTab === 'applications' && displayProduct.applications && displayProduct.applications.length > 0 && (
                      <ul className="space-y-3">
                        {displayProduct.applications.map((application, index) => (
                          <li
                            key={index}
                            className="flex items-start gap-3 p-3 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-100 shadow-sm"
                          >
                            <span className="flex-shrink-0">
                              <Check className="w-5 h-5" style={{ color: "#2980B9" }} />
                            </span>
                            <span className="text-neutral-800 font-roboto text-sm">
                              {application}
                            </span>
                          </li>
                        ))}
                      </ul>
                    )}
                    {activeTab === 'specifications' && displayProduct.specifications && Object.keys(displayProduct.specifications).length > 0 && (
                      <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                        <table className="w-full text-sm">
                          <tbody>
                            {Object.entries(displayProduct.specifications).map(
                              ([key, value]) => (
                                <tr
                                  key={key}
                                  className="border-b border-gray-200 last:border-0"
                                >
                                  <td
                                    className="py-2.5 font-medium"
                                    style={{ color: "#2980B9" }}
                                  >
                                    {t(`products.specs.${key}`, key)}
                                  </td>
                                  <td className="py-2.5 text-gray-800">
                                    {value}
                                  </td>
                                </tr>
                              ),
                            )}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>

                  {/* Actions - Bottom button */}
                  <div className="pt-4 border-t border-gray-100">
                    <Link
                      to={`/products/${displayProduct.slug}`}
                      className="btn-gradient text-white font-semibold px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full flex items-center justify-center gap-2"
                      onClick={() => {
                        onClose();
                        if (typeof window !== 'undefined') window.scrollTo(0, 0);
                      }}
                    >
                      <span>{t('products.viewFullDetails', 'View Details')}</span>
                      <ExternalLink className="w-4 h-4" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  // Use React Portal to render modal at document root level
  // Add debugging to ensure portal is working correctly
  if (typeof document === 'undefined') {
    return null;
  }

  // Ensure the modal is always rendered via portal
  return createPortal(modalContent, document.body);
};
