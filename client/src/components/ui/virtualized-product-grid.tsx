import React, { useState, useEffect, useRef, useMemo } from 'react';
import { ProductCard } from './product-card';

interface VirtualizedProductGridProps {
  products: any[];
  className?: string;
}

export function VirtualizedProductGrid({ products, className = '' }: VirtualizedProductGridProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
  const [containerHeight, setContainerHeight] = useState(0);
  
  // Constants for virtualization
  const ITEM_HEIGHT = 400; // Approximate height of a product card in pixels
  const BUFFER_SIZE = 5; // Number of items to render above and below the visible area
  const COLUMNS = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 3,
    xl: 4
  };
  
  // Get current column count based on window width
  const [columnCount, setColumnCount] = useState(COLUMNS.sm);
  
  // Update column count on resize
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 640) setColumnCount(COLUMNS.xs);
      else if (width < 768) setColumnCount(COLUMNS.sm);
      else if (width < 1024) setColumnCount(COLUMNS.md);
      else if (width < 1280) setColumnCount(COLUMNS.lg);
      else setColumnCount(COLUMNS.xl);
    };
    
    handleResize(); // Initial call
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Calculate visible range based on scroll position
  const handleScroll = () => {
    if (!containerRef.current) return;
    
    const scrollTop = window.scrollY;
    const containerTop = containerRef.current.offsetTop;
    const viewportHeight = window.innerHeight;
    
    // Calculate visible range with buffer
    const rowHeight = ITEM_HEIGHT;
    const visibleStart = Math.max(0, Math.floor((scrollTop - containerTop) / rowHeight) - BUFFER_SIZE);
    const visibleEnd = Math.min(
      Math.ceil(products.length / columnCount),
      Math.ceil((scrollTop - containerTop + viewportHeight) / rowHeight) + BUFFER_SIZE
    );
    
    setVisibleRange({
      start: visibleStart * columnCount,
      end: Math.min(products.length, visibleEnd * columnCount)
    });
  };
  
  // Set up scroll listener
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial calculation
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [products.length, columnCount]);
  
  // Calculate total height based on number of rows
  useEffect(() => {
    const totalRows = Math.ceil(products.length / columnCount);
    setContainerHeight(totalRows * ITEM_HEIGHT);
  }, [products.length, columnCount]);
  
  // Memoize visible products to avoid unnecessary re-renders
  const visibleProducts = useMemo(() => {
    return products.slice(visibleRange.start, visibleRange.end);
  }, [products, visibleRange.start, visibleRange.end]);
  
  return (
    <div 
      ref={containerRef} 
      className={`relative ${className}`}
      style={{ height: `${containerHeight}px` }}
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8 absolute top-0 left-0 right-0">
        {visibleProducts.map((product, index) => (
          <div
            key={product.productId || `product-${visibleRange.start + index}`}
            style={{
              transform: `translateY(${Math.floor((visibleRange.start + index) / columnCount) * ITEM_HEIGHT}px)`
            }}
          >
            <ProductCard
              slug={product.productId}
              title={product.title}
              image={product.image}
              category={product.category}
              description={product.description}
              link={product.link}
              features={product.features}
              applications={product.applications}
              specifications={product.specifications}
            />
          </div>
        ))}
      </div>
    </div>
  );
}