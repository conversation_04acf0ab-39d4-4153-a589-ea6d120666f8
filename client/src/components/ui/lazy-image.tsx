import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from "../../lib/utils";
import { Skeleton } from "./skeleton";

// Global image cache for the entire application session
// This dramatically improves filter transition performance by not reloading already seen images
const imageLoadCache = new Map<string, boolean>();

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  loadingClassName?: string;
  placeholderClassName?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  priority?: boolean;  // For images above the fold which should load immediately
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  loading?: 'lazy' | 'eager'; // Native HTML loading attribute for browser-level lazy loading
  blurEffect?: boolean; // Whether to apply a blur effect while loading
  sizes?: string; // Responsive image sizes attribute
  decoding?: 'async' | 'sync' | 'auto'; // Image decoding hint
}

/**
 * High-Performance LazyImage component with advanced optimizations for product filtering transitions.
 * 
 * Features:
 * - Shows skeleton placeholders during loading for better UX
 * - Uses intersection observer to trigger loading when approaching viewport (300px margin)
 * - Supports native browser lazy loading
 * - Optimized smooth transitions with blur effects
 * - Fixed dimensions during loading to prevent layout shifts
 * - Memory caching of loaded images for instant reappearance during filtering
 * - Uses requestAnimationFrame for all state updates to ensure rendering performance
 * - Optimized network handling with AbortController for better resource management
 */
const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  loadingClassName = 'animate-pulse bg-gray-200 dark:gray-700',
  placeholderClassName = '',
  width,
  height,
  onLoad,
  onError,
  priority = false,
  objectFit = 'cover',
  loading = 'lazy',
  blurEffect = false,
  sizes,
  decoding = 'async',
}) => {

  // Track loading states with session-based cache check
  const [isLoaded, setIsLoaded] = useState(() => {
    // Check both memory cache and session storage for previously loaded images
    const memoryCache = imageLoadCache.get(src);
    const sessionCache = typeof window !== 'undefined' && window.sessionStorage
      ? window.sessionStorage.getItem(`img_loaded_${src}`) === 'true'
      : false;

    const cached = memoryCache || sessionCache;

    // If cached, also set memory cache for faster access
    if (cached && !memoryCache) {
      imageLoadCache.set(src, true);
    }

    return cached;
  });
const [shouldLoad, setShouldLoad] = useState(priority || isLoaded);
const [error, setError] = useState(false);
const imageRef = useRef<HTMLDivElement>(null);
const observerRef = useRef<IntersectionObserver | null>(null);



  // Fix URLs for both development and production environments
  const getFixedSrc = (originalSrc: string) => {
    // PRODUCTION FIX: Ensure product images use correct paths
    if (originalSrc.startsWith('/images/products/')) {
      return originalSrc;
    }
    
    // Product images should use a consistent path pattern that works in production
    if (originalSrc.includes('/images/products/')) {
      // Ensure it has a leading slash for absolute path from domain root
      return originalSrc.startsWith('/') ? originalSrc : `/${originalSrc}`;
    }
    
    // Default handling for other images
    return originalSrc.startsWith('/') ? originalSrc : `/${originalSrc}`;
  };
  
  const fixedSrc = getFixedSrc(src);

  // Handle IntersectionObserver setup for optimized lazy loading
  useEffect(() => {
    // If priority is true or image is already cached, load immediately
    if (priority || isLoaded) {
      setShouldLoad(true);
      return;
    }

    // Only create observer if it doesn't exist and image isn't already loading
    if (!observerRef.current && !shouldLoad) {
      observerRef.current = new IntersectionObserver((entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          // Start loading the image when it's near the viewport
          setShouldLoad(true);

          // Disconnect observer once loading is triggered
          if (observerRef.current) {
            observerRef.current.disconnect();
            observerRef.current = null;
          }
        }
      }, {
        // Increased root margin for earlier loading to reduce perceived lag
        rootMargin: '500px', // Increased from 300px for even earlier loading
        threshold: 0.01
      });

      // Start observing the image container
      if (imageRef.current) {
        observerRef.current.observe(imageRef.current);
      }
    }

    // Clean up observer on component unmount
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, [priority, shouldLoad, isLoaded, src]);

  // Handle successful image loading with enhanced caching
  const handleImageLoad = useCallback(() => {
    // Update component state
    setIsLoaded(true);

    // Store in memory cache for immediate access during filter changes
    imageLoadCache.set(src, true);

    // Store in session storage for persistence across page reloads
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        window.sessionStorage.setItem(`img_loaded_${src}`, 'true');
      } catch (e) {
        // Silent catch for storage errors
      }
    }

    // Call user provided onLoad handler if available
    if (onLoad) onLoad();
  }, [onLoad, src]);

  // Enhanced error handling with fallback attempts for product images
  const [currentSrc, setCurrentSrc] = useState(fixedSrc);
  const [fallbackAttempts, setFallbackAttempts] = useState(0);
  
  // Handle image loading error with progressive fallback strategy
  const handleImageError = useCallback((e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const imgElement = e.target as HTMLImageElement;
    const originalSrc = imgElement.src;
    
    if (import.meta.env.DEV) console.log(`Image failed to load: ${originalSrc}`);
    
    // Specific handling for product images
    if (originalSrc.includes('/images/products/') && fallbackAttempts < 2) {
      // Try different approaches based on the attempt count
      if (fallbackAttempts === 0) {
        // First fallback: Try with absolute URL for production
        const productId = originalSrc.split('/').pop()?.split('.')[0];
        if (productId) {
          const newSrc = `/images/products/${productId}.jpg`;
          if (import.meta.env.DEV) console.log(`Trying first fallback: ${newSrc}`);
          setCurrentSrc(newSrc);
          setFallbackAttempts(prev => prev + 1);
          return;
        }
      } else if (fallbackAttempts === 1) {
        // Second fallback: Use category-based fallback image
        const category = src.includes('aluminum') ? 'aluminum' : 
                         src.includes('steel') ? 'steel' :
                         src.includes('cast') ? 'castiron' : 
                         src.includes('poly') ? 'polyethylene' : 'generic';
                         
        const newSrc = `/images/products/Aluminum U-Profiles.jpg`; // Use existing product image as fallback
        if (import.meta.env.DEV) console.log(`Trying category fallback: ${newSrc}`);
        setCurrentSrc(newSrc);
        setFallbackAttempts(prev => prev + 1);
        return;
      }
    }
    
    // No more fallbacks or not a product image - show error
    setError(true);
    if (onError) onError(e);
  }, [onError, fallbackAttempts, src]);

  // Style classes with no transitions to eliminate flickering
  const imageClasses = cn(
    {
      "opacity-0": !isLoaded && !error,
      "opacity-100": isLoaded && !error,
    },
    className
  );

  // Placeholder styles - no transitions for instant switching
  const placeholderClasses = cn(
    "absolute inset-0 rounded-md overflow-hidden",
    {
      "opacity-100": !isLoaded && !error,
      "opacity-0 pointer-events-none": isLoaded || error,
    },
    loadingClassName,
    placeholderClassName
  );

  // Error state styling
  const errorClasses = cn(
    "absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md",
    {
      "opacity-0": !error,
      "opacity-100 transition-opacity duration-300": error,
    }
  );

  return (
    <div 
      className="relative overflow-hidden" 
      style={{ 
        width: width ? `${width}px` : '100%', 
        height: height ? `${height}px` : 'auto',
        minHeight: '80px' // Slightly increased minimum height for better placeholders
      }}
      ref={imageRef}
    >
      {/* Skeleton placeholder while loading */}
      <div className={placeholderClasses}>
        <Skeleton className="w-full h-full rounded-md" />
      </div>
      
      {/* Error state indicator */}
      <div className={errorClasses}>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {alt || 'Image failed to load'}
        </span>
      </div>
      
      {/* The actual image - only load when needed */}
      {shouldLoad && (
        <img
          src={currentSrc}
          alt={alt}
          className={imageClasses}
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{
            width: '100%',
            height: '100%',
            objectFit,
          }}
          loading={loading}
          width={width}
          height={height}
          decoding={decoding}
          sizes={sizes}
        />
      )}
    </div>
  );
};

export { LazyImage };