import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { ProductCard } from './product-card';
import { translateProductCategory } from '../../i18n';
import { useTranslation } from 'react-i18next';

// Animation variants for container
const containerVariants = {
  hidden: { opacity: 0.98 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.01,
      duration: 0.15,
      ease: "easeOut"
    }
  }
};

// Animation variants for items
const itemVariants = {
  hidden: { opacity: 0.95, y: 3 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { 
      duration: 0.15,
      ease: "easeOut"
    }
  }
};

interface VirtualProductGridProps {
  products: any[];
  isUrbanInfrastructure?: boolean;
}

export const VirtualProductGrid: React.FC<VirtualProductGridProps> = ({ 
  products,
  isUrbanInfrastructure = false
}) => {
  const { t } = useTranslation();
  const [visibleProducts, setVisibleProducts] = useState<any[]>([]);
  const [visibleCount, setVisibleCount] = useState(12); // Start with 12 products
  const [isLoading, setIsLoading] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  
  // Effect to initialize visibleProducts
  useEffect(() => {
    // Reset visible count when products change
    setVisibleCount(12);
    
    // Set initial visible products
    setVisibleProducts(products.slice(0, 12));
  }, [products]);
  
  // Set up IntersectionObserver for infinite scroll
  useEffect(() => {
    // If we're showing all products already, no need for observer
    if (visibleCount >= products.length) return;
    
    const observer = new IntersectionObserver((entries) => {
      // If intersection is visible and we have more products to show
      if (entries[0].isIntersecting && visibleCount < products.length) {
        loadMoreProducts();
      }
    }, { 
      rootMargin: '100px', // Load more before user reaches the button
      threshold: 0.1 
    });
    
    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }
    
    return () => observer.disconnect();
  }, [visibleCount, products.length]);
  
  // Function to load more products
  const loadMoreProducts = () => {
    if (isLoading || visibleCount >= products.length) return;
    
    setIsLoading(true);
    
    // Use setTimeout to simulate loading and improve perceived performance
    setTimeout(() => {
      // Add 12 more products (or whatever is left)
      const newCount = Math.min(visibleCount + 12, products.length);
      setVisibleCount(newCount);
      setVisibleProducts(products.slice(0, newCount));
      setIsLoading(false);
    }, 300);
  };
  
  // Handle manual click on load more button
  const handleLoadMoreClick = () => {
    loadMoreProducts();
  };
  
  return (
    <>
      <motion.div
        className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 md:gap-5"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        layoutId="products-grid"
      >
        {visibleProducts.map((product, index) => (
          <motion.div
            key={product.productId || `product-${index}`}
            variants={itemVariants}
            layoutId={`product-${product.slug || product.productId}`}
          >
            <ProductCard
              slug={product.productId}
              title={product.title}
              image={product.image}
              category={translateProductCategory(product.category)}
              description={product.description}
              link={product.link || `/products/${product.productId}`}
              features={product.features}
              applications={product.applications}
              specifications={product.specifications}
            />
          </motion.div>
        ))}
      </motion.div>
      
      {/* Only show load more UI if we have more products to display */}
      {isUrbanInfrastructure && visibleCount < products.length && (
        <div 
          ref={loadMoreRef}
          className="mt-8 text-center"
        >
          <button 
            onClick={handleLoadMoreClick}
            className="bg-gradient-to-r from-cyan-500 to-teal-500 text-white px-4 py-2 rounded-md hover:opacity-90 transition-opacity"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t("common.loading", "Loading...")}
              </span>
            ) : (
              t("products.loadMore", "Load More Products")
            )}
          </button>
          
          <p className="text-gray-500 mt-2 text-sm">
            {t("products.showing", "Showing {{visible}} of {{total}} products", { 
              visible: visibleCount,
              total: products.length
            })}
          </p>
        </div>
      )}
    </>
  );
};

export default VirtualProductGrid;