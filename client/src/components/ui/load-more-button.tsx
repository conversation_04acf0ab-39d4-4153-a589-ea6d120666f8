import React from 'react';
import { useTranslation } from 'react-i18next';

interface LoadMoreButtonProps {
  onClick: () => void;
  isLoading: boolean;
  currentCount: number;
  totalCount: number;
}

export function LoadMoreButton({ onClick, isLoading, currentCount, totalCount }: LoadMoreButtonProps) {
  const { t } = useTranslation();
  
  return (
    <div className="col-span-full flex flex-col items-center justify-center py-6 mt-4">
      <button 
        onClick={onClick}
        className="bg-gradient-to-r from-cyan-500 to-teal-500 text-white px-6 py-2.5 rounded-md hover:opacity-90 transition-opacity"
        disabled={isLoading || currentCount >= totalCount}
      >
        {isLoading ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {t("common.loading", "Loading...")}
          </span>
        ) : (
          t("products.loadMore", "Load More Products")
        )}
      </button>
      
      <p className="text-gray-500 mt-3 text-sm">
        {t("products.showing", "Showing {{visible}} of {{total}} products", { 
          visible: Math.min(currentCount, totalCount),
          total: totalCount
        })}
      </p>
    </div>
  );
}

export default LoadMoreButton;