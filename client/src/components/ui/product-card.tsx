import { cn } from "../../lib/utils";
import { ArrowRight } from "lucide-react";
import { Link, useLocation } from "wouter";
import { scrollToTop } from "../../utils/scroll-manager";
import { LazyImage } from "./lazy-image";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useEffect, memo } from "react";
import { sanitizeSpecifications } from "../../data/product-data";
import { translateProductCategory } from "../../i18n";

interface ProductCardProps {
  image: string;
  title: string;
  description: string;
  category: string;
  link: string;
  features?: string[];
  applications?: string[];
  specifications?: Record<string, string>;
  id?: number;
  slug: string;
  className?: string;
  onPreviewClick?: (product: {
    slug: string;
    image: string;
    title: string;
    description: string;
    category: string;
    features: string[];
    applications: string[];
    specifications: Record<string, string>;
    link: string;
  }) => void;
}

// Memoize the ProductCard component to prevent unnecessary re-renders during filtering
export const ProductCard = memo(function ProductCard({
  image,
  title,
  description,
  category,
  link,
  features,
  applications,
  specifications,
  slug,
  id,
  className,
  onPreviewClick
}: ProductCardProps) {
  const { t } = useTranslation();
  const [location] = useLocation();

  // Scroll to top whenever the location changes
  useEffect(() => {
    scrollToTop();
  }, [location]);

  // Create safe defaults for all props to prevent rendering failures
  const safeTitle = title || (slug ? slug.replace(/-/g, ' ').replace(/\b\w/g, c => c.toUpperCase()) : 'Product');
  const safeDescription = description || '';
  const safeLink = link || '#';
  const safeImage = image || '';
  const safeFeatures = Array.isArray(features) ? features : [];
  const safeApplications = Array.isArray(applications) ? applications : [];
  const safeSpecifications = specifications || {};

  // Use flexible translation keys for better language support
  const viewDetailsText = t("products.viewDetails") || t("viewDetails", "View Details");
  
  // Enhanced translation of product title and description
  const translatedTitle = slug ? t(`products.${slug}.title`, safeTitle) : safeTitle;
  const translatedDescription = slug ? t(`products.${slug}.description`, safeDescription) : safeDescription;
  
  // Handle quick preview action
  const handleQuickPreview = (e: React.MouseEvent) => {
    // Get the target element and check if it's specifically the "View Details" link
    const target = e.target as HTMLElement;
    const clickedOnViewDetails = target.closest('a[href*="/products/"]');

    // If clicked on the "View Details" link specifically, don't open the preview
    if (clickedOnViewDetails) {
      return;
    }

    // Otherwise trigger the preview callback if provided
    if (onPreviewClick && slug) {
      onPreviewClick({
        slug,
        image: safeImage,
        title: safeTitle,
        description: safeDescription,
        category: safeCategory,
        features: safeFeatures,
        applications: safeApplications,
        specifications: sanitizeSpecifications(safeSpecifications),
        link: safeLink
      });
    }
  };
  
  // Get translated category with fallback
  const safeCategory = category || 'other';
  const translatedCategory = translateProductCategory(safeCategory) || safeCategory;
  
  // Use motion.div for animation when language changes
  return (
    <>
      <motion.div
        className={cn(
          "group overflow-hidden h-full flex flex-col transition-all duration-300 active:scale-[0.99] hover-lift shadow-md xs:shadow-lg sm:shadow-xl md:hover:shadow-2xl border border-white/40 backdrop-blur-lg bg-white/20 rounded-xl xs:rounded-xl sm:rounded-2xl relative cursor-pointer",
          className
        )}
        initial={{ opacity: 0.8, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        layout={false}
        data-product-id={slug} // Add this attribute for scroll targeting
        onClick={handleQuickPreview} // Make entire card clickable for preview
      >
        {/* Product Image with Enhanced Overlay - Mobile touch optimized */}
        <div
          className="relative w-full aspect-[4/3] overflow-hidden cursor-pointer touch-target"
        >
          {/* Image wrapper with transforms to prevent interference with loading states */}
          <div className="absolute inset-0 transform group-hover:scale-105 group-active:scale-[1.02] transition-transform duration-300 ease-out">
            <LazyImage
              src={safeImage}
              alt={safeTitle}
              loading="lazy"
              width={400}
              height={300}
              className="absolute inset-0 w-full h-full object-cover"
              blurEffect
              decoding="async"
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
              onLoad={() => {
                // Mark this image as loaded in the cache for better performance
                if (typeof window !== 'undefined' && window.sessionStorage) {
                  try {
                    window.sessionStorage.setItem(`img_loaded_${id || safeTitle}`, 'true');
                  } catch (e) {
                    // Silently handle storage errors
                  }
                }
              }}
            />
          </div>

          {/* Enhanced duotone gradient overlay with mobile hover/touch states */}
          <div className="absolute inset-0 bg-gradient-to-t from-primary/60 xs:from-primary/50 sm:from-primary/40 via-primary-dark/30 to-transparent opacity-40 xs:opacity-30 sm:opacity-20 group-hover:opacity-80 transition-opacity duration-300 z-10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle,_rgba(0,0,0,0)_0%,_rgba(0,0,0,0.2)_100%)] opacity-20 xs:opacity-10 sm:opacity-5 group-hover:opacity-70 transition-opacity duration-300 z-10"></div>

          {/* Subtle overlay to indicate interactivity */}
          <div className="absolute inset-0 bg-primary/0 group-hover:bg-primary/5 transition-colors duration-300 z-10 pointer-events-none"></div>
          
          {/* Enhanced Category Badge with glassmorphism - mobile optimized */}
          <div className="absolute top-1.5 xs:top-2 sm:top-3 md:top-4 right-1.5 xs:right-2 sm:right-3 md:right-4 z-20">
            <div className="backdrop-blur-md bg-white/40 xs:bg-white/30 text-2xs xs:text-xs sm:text-sm font-medium xs:font-semibold text-primary px-1.5 xs:px-2 sm:px-2.5 md:px-3.5 py-0.5 xs:py-0.5 sm:py-1 md:py-1.5 rounded-full shadow-md xs:shadow-md sm:shadow-xl border border-white/60 group-hover:bg-accent/20 group-hover:text-white transition-all duration-300">
              {translatedCategory || safeCategory}
            </div>
          </div>
        </div>
        
        {/* Content - mobile optimized with better spacing */}
        <div className="p-2.5 xs:p-3 sm:p-4 md:p-5 lg:p-6 flex flex-col flex-grow bg-white/90 backdrop-blur-md relative shadow-inner border-t border-white/60">
          {/* Gradient Line */}
          <div className="absolute top-0 left-0 w-full h-0.5 xs:h-1 sm:h-1.5 md:h-2 gradient-primary opacity-80 group-hover:opacity-100 transition-all duration-300 shadow-sm animate-shimmer"></div>
          
          <h4 
            className="text-sm xs:text-base sm:text-lg md:text-xl font-inter font-semibold mb-1 xs:mb-1.5 sm:mb-2 md:mb-3 group-hover:text-primary transition-all duration-300 line-clamp-2 product-card-title"
            data-product-title={slug}
          >
            {translatedTitle}
          </h4>
          
          <p className="font-roboto text-neutral-dark/90 mb-1.5 xs:mb-2 sm:mb-3 md:mb-4 lg:mb-5 flex-grow line-clamp-2 xs:line-clamp-2 sm:line-clamp-2 md:line-clamp-3 text-2xs xs:text-2xs sm:text-xs md:text-sm product-card-description"
             data-product-description={slug}>
            {translatedDescription}
          </p>
          
          <div className="flex justify-end items-center mt-auto">
          <Link
              to={`/products/${slug}`}
              onClick={(e) => {
                // Prevent the card click from opening the quick preview
                e.stopPropagation();
                // Scroll to top for better UX; Link handles navigation
                if (typeof window !== "undefined") window.scrollTo(0, 0);
              }}
            >
              <div className="text-sm text-gray-600 hover:text-cyan-500 underline underline-offset-2 flex items-center gap-0.5 xs:gap-1 transition-colors duration-300 cursor-pointer py-1 xs:py-1.5 sm:py-2 touch-target z-10">
                <span>{viewDetailsText}</span>
                <ArrowRight className="h-3 xs:h-3.5 sm:h-4 w-3 xs:w-3.5 sm:w-4 group-hover:translate-x-1 transition-transform duration-300" />
              </div>
            </Link>
          </div>
        </div>
      </motion.div>
    </>
  );
});