import React from "react";
import { useTranslation } from "react-i18next";
import { Search, X, RefreshCw } from "lucide-react";

interface ProductFiltersSidebarProps {
  // Filter states
  activeCategory: string;
  activeApplication: string;
  searchQuery: string;
  
  // Callbacks for filter changes
  onCategoryChange: (category: string) => void;
  onApplicationChange: (application: string) => void;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClearSearch: () => void;
  onClearAllFilters?: () => void;
  
  // Available filter options
  productCategories: Array<{ id: string; label: string }>;
}

export const ProductFiltersSidebar: React.FC<ProductFiltersSidebarProps> = ({
  activeCategory,
  activeApplication,
  searchQuery,
  onCategoryChange,
  onApplicationChange,
  onSearchChange,
  onClearSearch,
  onClearAllFilters,
  productCategories: materialOptions
}) => {
  const { t } = useTranslation();
  
  // Available applications based on the selected material category
  const applicationOptions = [
    { id: '', label: t('products.applications.all', 'All Applications') },
    { id: 'urban-infrastructure', label: t('products.filters.urban', 'Urban Infrastructure') },
    { id: 'construction', label: t('products.applications.construction', 'Construction') },
    { id: 'drainage', label: t('products.applications.drainage', 'Drainage') },
    { id: 'water', label: t('products.applications.water', 'Water Management') },
    { id: 'fittings', label: t('products.filters.fittings', 'Fittings') }
  ];

  // Determine if any filters are active
  const hasActiveFilters = activeCategory !== 'all' || activeApplication !== '' || searchQuery !== '';
  
  return (
    <div className="sticky top-6 w-48">
      {/* Material Filters */}
      <div className="w-full mb-6">
        <div className="flex items-center gap-1.5 mb-3.5">
          <h3 className="text-base font-medium text-gray-700">{t('products.filters.byMaterial', 'Filter by Material')}</h3>
          {activeCategory !== 'all' && (
            <div 
              className="relative inline-flex items-center justify-center w-4 h-4 rounded-full bg-gray-200 text-gray-600 text-xs cursor-help"
              title={t('products.filters.removeTooltip', "Click 'X' above to remove this filter")}
            >
              ?
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          {materialOptions.map(material => {
            const isActive = activeCategory === material.id;
            const textClass = isActive
              ? "text-primary font-medium"
              : "text-gray-700 hover:text-gray-900";
            
            return (
              <button
                key={material.id}
                onClick={() => onCategoryChange(material.id)}
                className={`text-left text-sm transition-colors ${textClass} no-underline`}
                style={{textDecoration: 'none'}}
                title={isActive ? t('products.filters.removeTooltip', "Click 'X' above to remove this filter") : ''}
              >
                {material.label}
                {isActive && <span className="sr-only">{t('products.filters.activeLabel', '(active)')}</span>}
              </button>
            );
          })}
        </div>
      </div>
      
      {/* Application Filters - Always shown */}
      <div className="w-full mb-6">
        <div className="flex items-center gap-1.5 mb-3.5">
          <h3 className="text-base font-medium text-gray-700">{t('products.filters.byApplication', 'Filter by Application')}</h3>
          {activeApplication !== '' && (
            <div 
              className="relative inline-flex items-center justify-center w-4 h-4 rounded-full bg-gray-200 text-gray-600 text-xs cursor-help"
              title={t('products.filters.removeTooltip', "Click 'X' above to remove this filter")}
            >
              ?
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          {applicationOptions.map(app => {
            const isActive = app.id === activeApplication;
            const textClass = isActive
              ? "text-teal-600 font-medium"
              : "text-gray-700 hover:text-gray-900";
            
            return (
              <button
                key={app.id}
                onClick={() => onApplicationChange(app.id)}
                className={`text-left text-sm transition-colors ${textClass} no-underline`}
                style={{textDecoration: 'none'}}
                title={isActive ? t('products.filters.removeTooltip', "Click 'X' above to remove this filter") : ''}
              >
                {app.label}
                {isActive && <span className="sr-only">{t('products.filters.activeLabel', '(active)')}</span>}
              </button>
            );
          })}
        </div>
      </div>
      
      {/* Search Input */}
      <div className="w-full mb-6">
        <h3 className="text-base font-medium text-gray-700 mb-3.5">{t('products.filters.search', 'Search Products')}</h3>
        <div className="relative">
          <Search className="absolute left-1 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
          <input
            type="text"
            placeholder={t("common.search", "Search...")}
            value={searchQuery}
            onChange={onSearchChange}
            className="w-full text-sm pl-7 pr-7 py-1 border-b border-gray-200 outline-none focus:border-primary bg-transparent"
          />
          {searchQuery && (
            <button
              onClick={onClearSearch}
              className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700"
            >
              <X className="h-3.5 w-3.5" />
            </button>
          )}
        </div>
      </div>

      {/* Clear All Filters Button - only shown when filters are active */}
      {hasActiveFilters && onClearAllFilters && (
        <button 
          onClick={onClearAllFilters}
          className="flex items-center gap-1.5 text-xs text-gray-700 hover:text-primary transition-colors mt-1 mb-4"
        >
          <RefreshCw className="h-3 w-3" />
          {t('products.filters.clearAll', 'Clear All Filters')}
        </button>
      )}
    </div>
  );
};

// Mobile version with horizontal scrolling filters
export const MobileProductFilters: React.FC<ProductFiltersSidebarProps> = ({
  activeCategory,
  activeApplication,
  searchQuery,
  onCategoryChange,
  onApplicationChange,
  onSearchChange,
  onClearSearch,
  onClearAllFilters,
  productCategories: materialOptions
}) => {
  const { t } = useTranslation();
  
  // Available applications based on the selected material category
  const applicationOptions = [
    { id: '', label: t('products.applications.all', 'All Applications') },
    { id: 'urban-infrastructure', label: t('products.filters.urban', 'Urban Infrastructure') },
    { id: 'construction', label: t('products.applications.construction', 'Construction') },
    { id: 'drainage', label: t('products.applications.drainage', 'Drainage') },
    { id: 'water', label: t('products.applications.water', 'Water Management') },
    { id: 'fittings', label: t('products.filters.fittings', 'Fittings') }
  ];
  
  // Determine if any filters are active
  const hasActiveFilters = activeCategory !== 'all' || activeApplication !== '' || searchQuery !== '';
  
  return (
    <div className="flex flex-col w-full gap-3">
      {/* Material Filters - Horizontal scrolling */}
      <div className="w-full mb-1">
        <div className="flex items-center gap-1.5 mb-2">
          <h3 className="text-xs font-medium text-gray-700">{t('products.filters.byMaterial', 'Filter by Material')}</h3>
          {activeCategory !== 'all' && (
            <div 
              className="relative inline-flex items-center justify-center w-3.5 h-3.5 rounded-full bg-gray-200 text-gray-600 text-[10px] cursor-help"
              title={t('products.filters.removeTooltip', "Click 'X' above to remove this filter")}
            >
              ?
            </div>
          )}
        </div>
        <div className="flex flex-nowrap overflow-x-auto pb-1 gap-1.5 w-full scrollbar-hide">
          {materialOptions.map(material => {
            const isActive = activeCategory === material.id;
            const buttonClass = isActive
              ? "border-primary text-primary bg-primary/5 font-medium"
              : "border-gray-200 text-gray-700 hover:border-gray-300 hover:bg-gray-50";

            return (
              <button
                key={material.id}
                onClick={() => onCategoryChange(material.id)}
                className={`px-2.5 py-1.5 text-xs rounded-md whitespace-nowrap transition-colors border ${buttonClass}`}
                title={isActive ? t('products.filters.removeTooltip', "Click 'X' above to remove this filter") : ''}
              >
                {material.label}
                {isActive && <span className="sr-only">{t('products.filters.activeLabel', '(active)')}</span>}
              </button>
            );
          })}
        </div>
      </div>
      
      {/* Application Filters - Always shown */}
      <div className="w-full mb-1">
        <div className="flex items-center gap-1.5 mb-2">
          <h3 className="text-xs font-medium text-gray-700">{t('products.filters.byApplication', 'Filter by Application')}</h3>
          {activeApplication !== '' && (
            <div 
              className="relative inline-flex items-center justify-center w-3.5 h-3.5 rounded-full bg-gray-200 text-gray-600 text-[10px] cursor-help"
              title={t('products.filters.removeTooltip', "Click 'X' above to remove this filter")}
            >
              ?
            </div>
          )}
        </div>
        <div className="flex flex-nowrap overflow-x-auto pb-1 gap-1.5 w-full scrollbar-hide">
          {applicationOptions.map(app => {
            const isActive = app.id === activeApplication;
            const buttonClass = isActive
              ? "border-teal-500 text-teal-600 bg-teal-50 font-medium"
              : "border-gray-200 text-gray-700 hover:border-gray-300 hover:bg-gray-50";
            
            return (
              <button
                key={app.id}
                onClick={() => onApplicationChange(app.id)}
                className={`px-2.5 py-1.5 text-xs rounded-md whitespace-nowrap transition-colors border ${buttonClass}`}
                title={isActive ? t('products.filters.removeTooltip', "Click 'X' above to remove this filter") : ''}
              >
                {app.label}
                {isActive && <span className="sr-only">{t('products.filters.activeLabel', '(active)')}</span>}
              </button>
            );
          })}
        </div>
      </div>
      
      {/* Search Input */}
      <div className="relative w-full mb-1">
        <h3 className="text-xs font-medium text-gray-700 mb-2">{t('products.filters.search', 'Search Products')}</h3>
        <div className="relative">
          <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
          <input
            type="text"
            placeholder={t("common.search", "Search...")}
            value={searchQuery}
            onChange={onSearchChange}
            className="w-full text-xs border border-gray-200 pl-7 pr-7 py-1.5 rounded-md outline-none focus:ring-1 focus:ring-primary focus:border-primary"
          />
          {searchQuery && (
            <button
              onClick={onClearSearch}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700"
            >
              <X className="h-3.5 w-3.5" />
            </button>
          )}
        </div>
      </div>

      {/* Clear All Filters Button - only shown when filters are active */}
      {hasActiveFilters && onClearAllFilters && (
        <button 
          onClick={onClearAllFilters}
          className="flex items-center justify-center gap-1.5 text-xs text-gray-700 hover:text-primary transition-colors w-full py-1.5 mt-1"
        >
          <RefreshCw className="h-3 w-3" />
          {t('products.filters.clearAll', 'Clear All Filters')}
        </button>
      )}
    </div>
  );
};