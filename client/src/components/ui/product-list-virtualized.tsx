import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { ProductCard } from './product-card';
import { useMediaQuery } from '../../hooks';

// Memoized individual product wrapper to prevent unnecessary re-renders
const ProductWrapper = memo(({ product, style }: { product: any, style: React.CSSProperties }) => {
  return (
    <div style={style}>
      <ProductCard
        slug={product.productId}
        title={product.title}
        image={product.image}
        category={product.category}
        description={product.description}
        link={product.link}
        features={product.features}
        applications={product.applications}
        specifications={product.specifications}
      />
    </div>
  );
});

interface ProductListProps {
  products: any[];
  className?: string;
}

// Performance-optimized product list with virtualization
export function ProductListVirtualized({ products, className = '' }: ProductListProps) {
  // Track window size to determine columns
  const isExtraSmall = useMediaQuery('(max-width: 639px)');
  const isSmall = useMediaQuery('(min-width: 640px) and (max-width: 767px)');
  const isMedium = useMediaQuery('(min-width: 768px) and (max-width: 1023px)');
  const isLarge = useMediaQuery('(min-width: 1024px) and (max-width: 1279px)');
  const isExtraLarge = useMediaQuery('(min-width: 1280px)');
  
  // Calculate columns based on screen size
  const columns = useMemo(() => {
    if (isExtraSmall) return 1;
    if (isSmall) return 2;
    if (isMedium) return 2;
    if (isLarge) return 3;
    if (isExtraLarge) return 4;
    return 2; // Default fallback
  }, [isExtraSmall, isSmall, isMedium, isLarge, isExtraLarge]);
  
  // Constants for virtualization
  const itemHeight = 450; // Approximate height of product card in pixels
  const buffer = 5; // Number of items to render above and below visible area
  
  // Track window scroll position and size
  const [scrollPosition, setScrollPosition] = useState(0);
  const [windowHeight, setWindowHeight] = useState(0);
  
  // Memoized event handlers for better performance
  const handleResize = useCallback(() => {
    setWindowHeight(window.innerHeight);
  }, []);
  
  const handleScroll = useCallback(() => {
    // Using requestAnimationFrame to optimize scroll performance
    requestAnimationFrame(() => {
      setScrollPosition(window.scrollY);
    });
  }, []);
  
  useEffect(() => {
    // Set initial window height and scroll position
    setWindowHeight(window.innerHeight);
    setScrollPosition(window.scrollY);
    
    // Add event listeners with passive option for better performance
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleResize, handleScroll]);
  
  // Determine which items should be rendered based on scroll position
  const visibleRange = useMemo(() => {
    // Track render performance
    const startTime = performance.now();
    
    // To determine visible range, we need to know where the product grid is located
    // This is an approximation - in a real implementation we would use refs and getBoundingClientRect
    const gridTopPosition = 300; // Approximate top position of the grid
    
    // Calculate which rows are visible
    const startRow = Math.max(0, Math.floor((scrollPosition - gridTopPosition) / itemHeight) - buffer);
    const endRow = Math.ceil((scrollPosition - gridTopPosition + windowHeight) / itemHeight) + buffer;
    
    // Convert rows to indices based on number of columns
    const startIndex = startRow * columns;
    const endIndex = Math.min(products.length, endRow * columns);
    
    // Performance logging in development only
    if (process.env.NODE_ENV === 'development') {
      const endTime = performance.now();
      if (import.meta.env.DEV) console.log(`Calculated visible range in ${endTime - startTime}ms: ${startIndex}-${endIndex}`);
    }
    
    return { startIndex, endIndex };
  }, [scrollPosition, windowHeight, columns, products.length, buffer]);
  
  // Only render the visible items for performance
  const visibleProducts = useMemo(() => {
    return products.slice(visibleRange.startIndex, visibleRange.endIndex);
  }, [products, visibleRange]);
  
  // Calculate the total height of the container to maintain scroll position
  const totalHeight = Math.ceil(products.length / columns) * itemHeight;
  
  // Calculate positions for each visible product
  const getProductPosition = (index: number) => {
    const rowIndex = Math.floor((visibleRange.startIndex + index) / columns);
    const colIndex = (visibleRange.startIndex + index) % columns;
    
    return {
      top: rowIndex * itemHeight,
      left: `${(colIndex / columns) * 100}%`,
      width: `${100 / columns}%`
    };
  };
  
  // Memoize the product position calculations to reduce layout work
  const getProductStyles = useCallback((index: number) => {
    const position = getProductPosition(index);
    return {
      position: 'absolute' as 'absolute',
      top: position.top,
      left: position.left,
      width: position.width,
      padding: '0 0.75rem',
      boxSizing: 'border-box' as 'border-box',
    };
  }, [getProductPosition]);

  // Avoid re-creating the container style on each render
  const containerStyle = useMemo(() => ({
    position: 'relative' as 'relative',
    height: `${totalHeight}px`,
    willChange: 'contents', // Hint to browser for optimization
  }), [totalHeight]);

  return (
    <div className={className} style={containerStyle}>
      {visibleProducts.map((product, index) => (
        <ProductWrapper
          key={product.productId || `product-${visibleRange.startIndex + index}`}
          product={product}
          style={getProductStyles(index)}
        />
      ))}
    </div>
  );
}