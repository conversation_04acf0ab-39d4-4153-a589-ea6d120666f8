import { motion } from "framer-motion";

interface MotionSplitTitleProps {
  part1?: string;
  part2?: string;
  className?: string;
}

export function MotionSplitTitle({
  part1 = "",
  part2 = "",
  className = ""
}: MotionSplitTitleProps) {
  return (
    <motion.h2
      className={`text-3xl sm:text-4xl md:text-5xl font-bold text-center mb-6 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      <span className="text-gray-900">{part1} </span>
      <span className="text-[#2D7EB6]">{part2}</span>
    </motion.h2>
  );
}
