import { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "./button";
import { Check, ChevronDown, Globe } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./dropdown-menu";
import "flag-icons/css/flag-icons.min.css";

const languages = [
  { code: "en", name: "English", flag: "fi-gb" },
  { code: "et", name: "<PERSON><PERSON><PERSON>", flag: "fi-ee" },
  { code: "ru", name: "Русский", flag: "fi-ru" },
  { code: "lv", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", flag: "fi-lv" },
  { code: "lt", name: "Lietuvi<PERSON>", flag: "fi-lt" },
  { code: "pl", name: "<PERSON>ski", flag: "fi-pl" },
  { code: "zh-CN", name: "简体中文", flag: "fi-cn" }
];

export function LanguageSwitcher() {
  const { i18n, t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [isChanging, setIsChanging] = useState(false);

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const changeLanguage = useCallback(async (languageCode: string) => {
    // Prevent multiple simultaneous language changes
    if (isChanging || languageCode === i18n.language) {
      return;
    }

    try {
      setIsChanging(true);
      setOpen(false);

      // Simple, direct language change without complex optimizations
      await i18n.changeLanguage(languageCode);

      // Update document language attribute for accessibility
      document.documentElement.lang = languageCode;

      // Store language preference
      localStorage.setItem('i18nextLng', languageCode);

    } catch (error) {
      console.error('Error changing language:', error);
      // Fallback to English if there's an error
      if (languageCode !== 'en') {
        try {
          await i18n.changeLanguage('en');
          document.documentElement.lang = 'en';
        } catch (fallbackError) {
          console.error('Fallback language change failed:', fallbackError);
        }
      }
    } finally {
      setIsChanging(false);
    }
  }, [i18n, isChanging]);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-9 px-2 gap-1 text-neutral-600 hover:text-accent hover:bg-neutral-100"
          disabled={isChanging}
        >
          <span className={`${currentLanguage.flag}`} style={{ width: '20px', height: '15px' }}></span>
          <span className="sr-only">
            {currentLanguage.name}
          </span>
          <ChevronDown className="h-4 w-4 ml-1 text-neutral-500" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[150px] p-2">
        {languages.map(language => (
          <DropdownMenuItem
            key={language.code}
            className={`flex items-center gap-2 text-sm px-2.5 py-2 cursor-pointer ${
              language.code === i18n.language ? 'bg-neutral-100' : ''
            } ${isChanging ? 'opacity-50 pointer-events-none' : ''}`}
            onClick={() => changeLanguage(language.code)}
            disabled={isChanging}
          >
            <span className={`${language.flag}`} style={{ width: '20px', height: '15px' }}></span>
            <span className="flex-1">
              {language.code === 'en' && 'English'}
              {language.code === 'et' && 'Eesti'}
              {language.code === 'ru' && 'Русский'}
              {language.code === 'lv' && 'Latviešu'}
              {language.code === 'lt' && 'Lietuvių'}
              {language.code === 'pl' && 'Polski'}
              {language.code === 'zh-CN' && '简体中文'}
            </span>
            {language.code === i18n.language && (
              <Check className="h-4 w-4 text-accent" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}