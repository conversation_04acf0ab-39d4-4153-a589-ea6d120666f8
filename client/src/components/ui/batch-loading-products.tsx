import React, { useState, useEffect, useMemo, memo } from 'react';
import { motion } from 'framer-motion';
import { ProductCard } from './product-card';
import { translateProductCategory } from '../../i18n';
import { useTranslation } from 'react-i18next';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0.98 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.01,
      duration: 0.15,
      ease: "easeOut"
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0.95, y: 3 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { 
      duration: 0.15,
      ease: "easeOut"
    }
  }
};

interface BatchLoadingProductsProps {
  products: any[];
  className?: string;
}

// Memoized product card to prevent re-renders when parent changes
const MemoizedProductCard = memo(({ product }: { product: any }) => {
  return (
    <motion.div
      variants={itemVariants}
      layoutId={`product-${product.productId}`}
    >
      <ProductCard
        slug={product.productId}
        title={product.title}
        image={product.image}
        category={translateProductCategory(product.category)}
        description={product.description}
        link={product.link || `/products/${product.productId}`}
        features={product.features}
        applications={product.applications}
        specifications={product.specifications}
      />
    </motion.div>
  );
});

export function BatchLoadingProducts({ products, className = '' }: BatchLoadingProductsProps) {
  const { t } = useTranslation();
  const [visibleCount, setVisibleCount] = useState(8);
  const [isLoading, setIsLoading] = useState(false);
  
  // Reset visible count when products change but with requestAnimationFrame to avoid layout shifts
  useEffect(() => {
    if (products?.length) {
      // Use requestAnimationFrame to batch visual updates with browser paint cycle
      requestAnimationFrame(() => {
        setVisibleCount(8);
      });
    }
  }, [products]);
  
  // Handle load more button click with optimized loading
  const handleLoadMore = () => {
    if (isLoading) return;
    
    setIsLoading(true);
    
    // Calculate next batch immediately to avoid calculation during animation
    const nextBatch = Math.min(visibleCount + 8, products.length);
    
    // Use requestAnimationFrame for smoother transitions aligned with browser paint cycle
    requestAnimationFrame(() => {
      setTimeout(() => {
        setVisibleCount(nextBatch);
        setIsLoading(false);
      }, 300);
    });
  };
  
  // Get current visible products with memoization to avoid recalculation
  const visibleProducts = useMemo(() => {
    return products.slice(0, visibleCount);
  }, [products, visibleCount]);
  
  return (
    <div className="space-y-8">
      <motion.div
        className={`grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 md:gap-5 ${className}`}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        layout="position"
      >
        {visibleProducts.map((product, index) => (
          <MemoizedProductCard
            key={product.productId || `product-${index}`}
            product={product}
          />
        ))}
      </motion.div>
      
      {visibleCount < products.length && (
        <div className="flex flex-col items-center">
          <button
            onClick={handleLoadMore}
            disabled={isLoading}
            className="bg-gradient-to-r from-cyan-500 to-teal-500 text-white px-6 py-2.5 rounded-md hover:opacity-90 transition-opacity"
          >
            {isLoading ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t("common.loading", "Loading...")}
              </span>
            ) : (
              t("products.loadMore", "Load More Products")
            )}
          </button>
          
          <p className="text-gray-500 mt-3 text-sm">
            {t("products.showing", "Showing {{visible}} of {{total}} products", { 
              visible: visibleCount,
              total: products.length
            })}
          </p>
        </div>
      )}
    </div>
  );
}

export default BatchLoadingProducts;