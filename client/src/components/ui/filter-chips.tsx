import React from "react";
import { useTranslation } from "react-i18next";
import { X } from "lucide-react";

interface FilterChipsProps {
  // Active filters
  activeCategory: string;
  activeApplication: string;
  
  // Callbacks for removing filters
  onCategoryChange: (category: string) => void;
  onApplicationChange: (application: string) => void;
  onClearAllFilters?: () => void;
  
  // Filter options for label resolution
  materialOptions: Array<{ id: string; label: string }>;
  applicationOptions: Array<{ id: string; label: string }>;
}

const FilterChips: React.FC<FilterChipsProps> = ({
  activeCategory,
  activeApplication,
  onCategoryChange,
  onApplicationChange,
  onClearAllFilters,
  materialOptions,
  applicationOptions
}) => {
  const { t } = useTranslation();
  
  // Only show chips container if there are active filters
  if (activeCategory === 'all' && activeApplication === '') {
    return null;
  }
  
  // Find the label for the active category
  const activeCategoryLabel = materialOptions.find(opt => opt.id === activeCategory)?.label || '';
  
  // Find the label for the active application
  const activeApplicationLabel = applicationOptions.find(opt => opt.id === activeApplication)?.label || '';
  
  const hasMultipleFilters = activeCategory !== 'all' && activeApplication !== '';
  
  return (
    <div className="flex flex-wrap items-center gap-2 mb-4 p-2 bg-gray-50 rounded-md">
      <span className="text-sm text-gray-500 mr-1">{t('products.filters.activeFilters', 'Active Filters')}:</span>
      
      {/* Material filter chip */}
      {activeCategory !== 'all' && (
        <button 
          onClick={() => onCategoryChange('all')}
          className="inline-flex items-center gap-1 px-3 py-1 text-sm rounded-md bg-white border border-gray-200 hover:bg-gray-100 transition-colors"
          aria-label={t('products.filters.removeMaterial', 'Remove material filter')}
        >
          {activeCategoryLabel}
          <X className="h-3.5 w-3.5 text-gray-600" />
        </button>
      )}
      
      {/* Application filter chip */}
      {activeApplication !== '' && (
        <button 
          onClick={() => onApplicationChange('')}
          className="inline-flex items-center gap-1 px-3 py-1 text-sm rounded-md bg-white border border-gray-200 hover:bg-gray-100 transition-colors"
          aria-label={t('products.filters.removeApplication', 'Remove application filter')}
        >
          {activeApplicationLabel}
          <X className="h-3.5 w-3.5 text-gray-600" />
        </button>
      )}
      
      {/* Clear all filters link - only show when multiple filters are active */}
      {hasMultipleFilters && onClearAllFilters && (
        <button 
          onClick={onClearAllFilters}
          className="inline-flex items-center gap-1 px-3 py-1 text-sm text-primary hover:underline transition-colors ml-auto"
          aria-label={t('products.filters.clearAllFilters', 'Clear all filters')}
        >
          {t('products.filters.clearAll', 'Clear all')} <X className="h-3.5 w-3.5" />
        </button>
      )}
    </div>
  );
};

export default FilterChips;