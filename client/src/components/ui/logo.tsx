import { cn } from "../../lib/utils";

const logoImage = "/images/metanord-logo.png";

interface LogoProps {
  className?: string;
  variant?: "header" | "footer";
}

export const Logo = ({ className, variant = "header" }: LogoProps) => {
  // MOBILE LOGO SIZE: Increased by 20% for better mobile visibility and proportions
  const sizeClasses = variant === "footer"
    ? "w-full h-auto" // responsive size for footer - unchanged
    : "h-24 xs:h-26 sm:h-28 md:h-24 lg:h-28 w-auto"; // Mobile increased by 20%: 96px → 104px → 112px, desktop unchanged

  // For footer, we don't need any special treatment as it's displayed against white background
  // For header, we may want to adjust it for the dark background
  const visibilityClass = variant === "footer"
    ? "" // retain original colors exactly as they are
    : ""; // no adjustments for header either

  return (
    <img
      src={logoImage}
      alt="MetaNord Logo"
      className={cn(
        sizeClasses,
        visibilityClass,
        "object-contain",
        className
      )}
      aria-label="MetaNord Logo"
      data-logo="true"
    />
  );
};
