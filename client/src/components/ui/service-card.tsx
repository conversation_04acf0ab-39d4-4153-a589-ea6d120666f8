import { LucideIcon } from "lucide-react";
import { cn } from "../../lib/utils";
import { useTranslation } from "react-i18next";

interface ServiceCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  features: { text: string; icon: LucideIcon }[];
  className?: string;
  showButton?: boolean; // ← Добавлено
}

export function ServiceCard({
  icon: Icon,
  title,
  description,
  features,
  className,
  showButton = true // ← По умолчанию true
}: ServiceCardProps) {
  const { t } = useTranslation();

  return (
    <div
      className={cn(
        "group relative bg-white rounded-xl overflow-hidden transition-all duration-300 border border-gray-100 hover:shadow-xl hover:-translate-y-1",
        className
      )}
    >
      {/* Top gradient bar */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9]" />

      {/* Inner content */}
      <div className="relative p-8 z-10">
        {/* Icon with gradient background */}
        <div className="mb-6 flex justify-start">
          <div className="bg-gradient-to-br from-[#2D7EB6] to-[#40BFB9] w-14 h-14 rounded-lg flex items-center justify-center shadow-md group-hover:shadow-accent/40 transition-all">
            <Icon className="h-6 w-6 text-white group-hover:scale-110 transition-transform duration-300" />
          </div>
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold text-[#2D7EB6] mb-2">{title}</h3>
        <p className="text-sm text-gray-600 mb-4">{description}</p>

        {/* Features */}
        <ul className="space-y-3 mb-6">
          {features.map((f, i) => (
            <li key={i} className="flex items-start gap-3 text-sm text-gray-700">
              <f.icon className="w-4 h-4 text-[#2D7EB6] mt-0.5" />
              {f.text}
            </li>
          ))}
        </ul>

        {/* Conditional button */}
        {showButton && (
          <button className="inline-block px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-md shadow hover:shadow-lg transition-all">
            {t("common.learnMore", "Learn More")}
          </button>
        )}
      </div>
    </div>
  );
}
