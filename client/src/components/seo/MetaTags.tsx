import React from 'react';
import { Helmet } from 'react-helmet';
import { useLanguage } from '../../hooks/use-language';

interface MetaTagsProps {
  title: string;
  description: string;
  keywords?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'profile' | 'book' | 'product';
  canonical?: string;
  noIndex?: boolean;
  languageAlternates?: {
    hrefLang: string;
    href: string;
  }[];
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  ogUrl?: string;
}

/**
 * MetaTags component - Comprehensive SEO meta tags with full Open Graph and Twitter support
 */
export default function MetaTags({
  title,
  description,
  keywords,
  ogImage,
  ogType = 'website',
  canonical,
  noIndex = false,
  languageAlternates,
  publishedTime,
  modifiedTime,
  author,
  section,
  tags,
  ogUrl,
}: MetaTagsProps) {
  const { language } = useLanguage();
  const metaTitle = title.includes('MetaNord') ? title : `${title} | MetaNord OÜ`;

  // Generate current page URL if not provided
  const currentUrl = ogUrl || (typeof window !== 'undefined' ? window.location.href : 'https://metanord.eu');

  // Handle image fallback - only include og:image if we have a valid image
  const hasValidImage = ogImage && ogImage !== '/logo-share.png' && ogImage.trim() !== '';
  const finalImage = hasValidImage ? ogImage : undefined;

  // Ensure absolute URLs for images
  const absoluteImageUrl = finalImage && !finalImage.startsWith('http')
    ? `https://metanord.eu${finalImage}`
    : finalImage;

  return (
    <Helmet>
      {/* Primary Meta Tags */}
      <title>{metaTitle}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:title" content={metaTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:site_name" content="MetaNord" />
      <meta property="og:locale" content={language === 'en' ? 'en_US' : language} />
      {absoluteImageUrl && <meta property="og:image" content={absoluteImageUrl} />}
      {absoluteImageUrl && <meta property="og:image:alt" content={metaTitle} />}

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={metaTitle} />
      <meta name="twitter:description" content={description} />
      {absoluteImageUrl && <meta name="twitter:image" content={absoluteImageUrl} />}
      {absoluteImageUrl && <meta name="twitter:image:alt" content={metaTitle} />}

      {/* Product specific (if type is product) */}
      {ogType === 'product' && (
        <>
          <meta property="product:brand" content="MetaNord" />
          <meta property="product:availability" content="in stock" />
        </>
      )}

      {/* Article specific (if type is article) */}
      {ogType === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {ogType === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {ogType === 'article' && author && (
        <meta property="article:author" content={author} />
      )}
      {ogType === 'article' && section && (
        <meta property="article:section" content={section} />
      )}
      {ogType === 'article' && tags && tags.map((tag, index) => (
        <meta key={index} property="article:tag" content={tag} />
      ))}

      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}

      {/* Language alternates for internationalization */}
      {languageAlternates && languageAlternates.map((alt, index) => (
        <link
          key={index}
          rel="alternate"
          href={alt.href}
          hrefLang={alt.hrefLang}
        />
      ))}
    </Helmet>
  );
}