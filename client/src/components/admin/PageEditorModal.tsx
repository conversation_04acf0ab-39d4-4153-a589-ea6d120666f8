import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { Label } from "../ui/label";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON>eader, CardTitle } from "../ui/card";
import {
  Save,
  Loader2,
  Eye,
  Settings,
  FileText,
  ImageIcon,
  Globe,
  Smartphone,
  Tablet,
  Monitor,
  Layout,
  Type,
  Palette,
} from "lucide-react";
import { RichTextEditor } from "./RichTextEditor";
import { DragDropPageBuilder } from "./DragDropPageBuilder";

// Form validation schema
const pageFormSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Invalid slug format"),
  description: z.string().max(500, "Description too long").optional(),
  metaTitle: z.string().max(60, "Meta title too long").optional(),
  metaDescription: z.string().max(160, "Meta description too long").optional(),
  canonicalUrl: z.string().url("Invalid URL").optional().or(z.literal("")),
  ogImage: z.string().url("Invalid image URL").optional().or(z.literal("")),
  status: z.enum(['draft', 'published', 'archived']),
  language: z.string().min(2, "Language is required"),
  templateId: z.number().optional(),
});

type PageFormData = z.infer<typeof pageFormSchema>;

interface CustomPage {
  id: number;
  title: string;
  description: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
  language: string;
  status: 'draft' | 'published' | 'archived';
  content?: any;
  metaTitle?: string | null;
  metaDescription?: string | null;
  ogImage?: string | null;
  canonicalUrl?: string | null;
  publishedAt?: string | null;
  templateId?: number | null;
  author?: number | null;
}

interface PageTemplate {
  id: number;
  name: string;
  description: string;
  previewImage?: string;
  contentStructure: any;
  isActive: boolean;
}

interface PageEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: PageFormData) => void;
  isCreating: boolean;
  editingPage: CustomPage | null;
  pageTemplates: PageTemplate[];
  isLoading: boolean;
  form: any; // react-hook-form instance
}

const LANGUAGES = [
  { code: "en", name: "English" },
  { code: "et", name: "Estonian" },
  { code: "ru", name: "Russian" },
  { code: "lv", name: "Latvian" },
  { code: "lt", name: "Lithuanian" },
  { code: "pl", name: "Polish" },
];

export function PageEditorModal({
  isOpen,
  onClose,
  onSubmit,
  isCreating,
  editingPage,
  pageTemplates = [],
  isLoading,
  form,
}: PageEditorModalProps) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("content");
  const [previewDevice, setPreviewDevice] = useState<"desktop" | "tablet" | "mobile">("desktop");
  const [contentMode, setContentMode] = useState<"builder" | "editor">("builder");
  const [pageContent, setPageContent] = useState("");
  const [pageSections, setPageSections] = useState<any[]>([]);

  const handleSubmit = (data: PageFormData) => {
    onSubmit(data);
  };

  const handleTitleChange = (title: string) => {
    form.setValue("title", title);
    if (isCreating || !editingPage?.slug) {
      const slug = title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .replace(/^-|-$/g, "");
      form.setValue("slug", slug);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'archived':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {isCreating ? "Create New Page" : `Edit "${editingPage?.title}"`}
          </DialogTitle>
          <DialogDescription>
            {isCreating 
              ? "Create a new page with custom content and SEO settings."
              : "Edit page content, SEO settings, and configuration."
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="content" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Content
                </TabsTrigger>
                <TabsTrigger value="seo" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  SEO
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Settings
                </TabsTrigger>
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Preview
                </TabsTrigger>
              </TabsList>

              {/* Content Tab */}
              <TabsContent value="content" className="space-y-4">
                {/* Basic Page Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Page Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Page Title *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter page title"
                                {...field}
                                onChange={(e) => handleTitleChange(e.target.value)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="slug"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>URL Slug *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="page-url-slug"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              URL-friendly version of the title
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Brief description of the page"
                              rows={3}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Internal description for admin reference
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {isCreating && pageTemplates.length > 0 && (
                      <FormField
                        control={form.control}
                        name="templateId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Page Template</FormLabel>
                            <Select
                              onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                              value={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Choose a template (optional)" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="">No Template</SelectItem>
                                {pageTemplates.map((template) => (
                                  <SelectItem key={template.id} value={template.id.toString()}>
                                    {template.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Start with a pre-designed template
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </CardContent>
                </Card>

                {/* Content Editor */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Layout className="h-5 w-5" />
                        Page Content
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Button
                          type="button"
                          variant={contentMode === "builder" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setContentMode("builder")}
                        >
                          <Layout className="h-4 w-4 mr-2" />
                          Page Builder
                        </Button>
                        <Button
                          type="button"
                          variant={contentMode === "editor" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setContentMode("editor")}
                        >
                          <Type className="h-4 w-4 mr-2" />
                          Rich Editor
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {contentMode === "builder" ? (
                      <div className="border rounded-lg h-96">
                        <DragDropPageBuilder
                          initialSections={pageSections}
                          onSave={(sections) => setPageSections(sections)}
                          className="h-full"
                        />
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="page-content">Page Content</Label>
                          <RichTextEditor
                            value={pageContent}
                            onChange={setPageContent}
                            placeholder="Start creating your page content..."
                            minHeight="400px"
                          />
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* SEO Tab */}
              <TabsContent value="seo" className="space-y-4">
                <FormField
                  control={form.control}
                  name="metaTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meta Title</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="SEO title for search engines"
                          {...field}
                          maxLength={60}
                        />
                      </FormControl>
                      <FormDescription>
                        {field.value?.length || 0}/60 characters. Appears in search results.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="metaDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meta Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Brief description for search engines"
                          rows={3}
                          {...field}
                          maxLength={160}
                        />
                      </FormControl>
                      <FormDescription>
                        {field.value?.length || 0}/160 characters. Appears in search results.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="canonicalUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Canonical URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com/page"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Preferred URL for SEO
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ogImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Open Graph Image</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com/image.jpg"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Image for social media sharing
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                                Draft
                              </div>
                            </SelectItem>
                            <SelectItem value="published">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                Published
                              </div>
                            </SelectItem>
                            <SelectItem value="archived">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                                Archived
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="language"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Language</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {LANGUAGES.map((lang) => (
                              <SelectItem key={lang.code} value={lang.code}>
                                {lang.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {!isCreating && editingPage && (
                  <div className="space-y-2">
                    <Label>Page Information</Label>
                    <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                      <div>
                        <div className="text-sm font-medium">Created</div>
                        <div className="text-sm text-gray-600">
                          {new Date(editingPage.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium">Last Updated</div>
                        <div className="text-sm text-gray-600">
                          {new Date(editingPage.updatedAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium">Status</div>
                        <Badge className={getStatusColor(editingPage.status)}>
                          {editingPage.status}
                        </Badge>
                      </div>
                      <div>
                        <div className="text-sm font-medium">Page ID</div>
                        <div className="text-sm text-gray-600">#{editingPage.id}</div>
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>

              {/* Preview Tab */}
              <TabsContent value="preview" className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Page Preview</h3>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant={previewDevice === "desktop" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPreviewDevice("desktop")}
                    >
                      <Monitor className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant={previewDevice === "tablet" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPreviewDevice("tablet")}
                    >
                      <Tablet className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant={previewDevice === "mobile" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPreviewDevice("mobile")}
                    >
                      <Smartphone className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="border rounded-lg p-4 bg-gray-50 min-h-[300px]">
                  <div className="text-center text-gray-500 py-20">
                    <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Live preview will be available after page creation</p>
                    <p className="text-sm">Use the page builder to design your content</p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Save className="mr-2 h-4 w-4" />
                {isCreating ? "Create Page" : "Update Page"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
