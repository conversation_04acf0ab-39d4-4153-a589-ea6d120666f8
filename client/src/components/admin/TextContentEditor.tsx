import { useState } from "react";
import { useToast } from "../../hooks/use-toast";

// UI Components
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { 
  Edit,
  Globe,
  Loader2, 
  Plus,
  RefreshCw,
  Save,
  Type,
} from "lucide-react";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";

interface TextContent {
  id: string;
  key: string;
  value: string;
  language: string;
  section: string;
  description?: string;
  updatedAt: string;
}

// Mock text content data
const mockTextContent: TextContent[] = [
  {
    id: '1',
    key: 'hero.title',
    value: 'Premium Aluminum Solutions',
    language: 'en',
    section: 'homepage',
    description: 'Main hero title on homepage',
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    key: 'hero.subtitle',
    value: 'High-quality aluminum profiles for construction and industry',
    language: 'en',
    section: 'homepage',
    description: 'Hero subtitle text',
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    key: 'nav.products',
    value: 'Products',
    language: 'en',
    section: 'navigation',
    description: 'Products navigation link',
    updatedAt: new Date().toISOString()
  },
  {
    id: '4',
    key: 'nav.services',
    value: 'Services',
    language: 'en',
    section: 'navigation',
    description: 'Services navigation link',
    updatedAt: new Date().toISOString()
  },
  {
    id: '5',
    key: 'contact.title',
    value: 'Get in Touch',
    language: 'en',
    section: 'contact',
    description: 'Contact page title',
    updatedAt: new Date().toISOString()
  }
];

export function TextContentEditor() {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedContent, setSelectedContent] = useState<TextContent | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [selectedSection, setSelectedSection] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  
  const { toast } = useToast();

  // Filter content based on language and section
  const filteredContent = mockTextContent.filter(content => {
    const languageMatch = selectedLanguage === 'all' || content.language === selectedLanguage;
    const sectionMatch = selectedSection === 'all' || content.section === selectedSection;
    return languageMatch && sectionMatch;
  });

  // Get unique sections
  const sections = Array.from(new Set(mockTextContent.map(content => content.section)));

  const handleEditContent = (content: TextContent) => {
    setSelectedContent(content);
    setIsEditDialogOpen(true);
  };

  const handleSaveContent = async () => {
    if (!selectedContent) return;

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Content Updated",
        description: `Text content "${selectedContent.key}" has been updated successfully`,
      });
      setIsLoading(false);
      setIsEditDialogOpen(false);
      setSelectedContent(null);
    }, 1000);
  };

  const handleCreateContent = () => {
    const newContent: TextContent = {
      id: Date.now().toString(),
      key: '',
      value: '',
      language: selectedLanguage === 'all' ? 'en' : selectedLanguage,
      section: selectedSection === 'all' ? 'homepage' : selectedSection,
      description: '',
      updatedAt: new Date().toISOString()
    };
    setSelectedContent(newContent);
    setIsEditDialogOpen(true);
  };

  // Get language badge
  const getLanguageBadge = (language: string) => {
    const languageNames: Record<string, string> = {
      'en': 'English',
      'de': 'German',
      'fr': 'French',
      'es': 'Spanish'
    };
    
    return (
      <Badge variant="outline" className="text-blue-500 border-blue-500">
        {languageNames[language] || language.toUpperCase()}
      </Badge>
    );
  };

  // Get section badge
  const getSectionBadge = (section: string) => {
    return (
      <Badge variant="outline" className="text-green-500 border-green-500">
        {section.charAt(0).toUpperCase() + section.slice(1)}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <div>
            <CardTitle className="flex items-center">
              <Type className="h-5 w-5 mr-2 text-primary" />
              Text Content Editor
            </CardTitle>
            <CardDescription className="mt-1.5">
              Manage website text content and translations
            </CardDescription>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={handleCreateContent}>
              <Plus className="h-4 w-4 mr-2" />
              Add Content
            </Button>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filters */}
        <div className="flex gap-4 mb-6">
          <div className="flex items-center gap-2">
            <Label htmlFor="language">Language:</Label>
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-2">
            <Label htmlFor="section">Section:</Label>
            <Select value={selectedSection} onValueChange={setSelectedSection}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sections</SelectItem>
                {sections.map(section => (
                  <SelectItem key={section} value={section}>
                    {section.charAt(0).toUpperCase() + section.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Content Table */}
        {filteredContent.length > 0 ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Key</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Language</TableHead>
                  <TableHead>Section</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent.map((content) => (
                  <TableRow key={content.id}>
                    <TableCell className="font-mono text-sm">{content.key}</TableCell>
                    <TableCell className="max-w-xs truncate">{content.value}</TableCell>
                    <TableCell>{getLanguageBadge(content.language)}</TableCell>
                    <TableCell>{getSectionBadge(content.section)}</TableCell>
                    <TableCell className="text-muted-foreground text-sm">
                      {content.description || '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleEditContent(content)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="rounded-md border flex flex-col items-center justify-center py-12">
            <Type className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No content found</h3>
            <p className="text-muted-foreground mt-1 mb-4">
              No text content matches the current filters
            </p>
            <Button onClick={handleCreateContent}>
              <Plus className="h-4 w-4 mr-2" />
              Add Content
            </Button>
          </div>
        )}
      </CardContent>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {selectedContent?.id && selectedContent.id !== Date.now().toString() ? 'Edit' : 'Create'} Text Content
            </DialogTitle>
            <DialogDescription>
              {selectedContent?.id && selectedContent.id !== Date.now().toString() 
                ? 'Update the text content below' 
                : 'Create new text content for the website'
              }
            </DialogDescription>
          </DialogHeader>
          
          {selectedContent && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="key">Content Key *</Label>
                <Input
                  id="key"
                  value={selectedContent.key}
                  onChange={(e) => setSelectedContent({ ...selectedContent, key: e.target.value })}
                  placeholder="e.g., hero.title"
                  className="font-mono"
                />
              </div>
              
              <div>
                <Label htmlFor="value">Content Value *</Label>
                <Textarea
                  id="value"
                  value={selectedContent.value}
                  onChange={(e) => setSelectedContent({ ...selectedContent, value: e.target.value })}
                  placeholder="Enter the text content"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="language">Language</Label>
                  <Select 
                    value={selectedContent.language} 
                    onValueChange={(value) => setSelectedContent({ ...selectedContent, language: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="de">German</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="section">Section</Label>
                  <Select 
                    value={selectedContent.section} 
                    onValueChange={(value) => setSelectedContent({ ...selectedContent, section: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="homepage">Homepage</SelectItem>
                      <SelectItem value="navigation">Navigation</SelectItem>
                      <SelectItem value="contact">Contact</SelectItem>
                      <SelectItem value="products">Products</SelectItem>
                      <SelectItem value="services">Services</SelectItem>
                      <SelectItem value="footer">Footer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={selectedContent.description || ''}
                  onChange={(e) => setSelectedContent({ ...selectedContent, description: e.target.value })}
                  placeholder="Optional description for this content"
                />
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveContent} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Content
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
