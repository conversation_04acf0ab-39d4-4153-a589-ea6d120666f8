import React, { useState, useRef, useEffect } from "react";
import { Button } from "../ui/button";
import { Separator } from "../ui/separator";
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Link,
  Image,
  Code,
  Quote,
  Undo,
  Redo,
  Type,
  Palette,
  Eye,
  EyeOff,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  minHeight?: string;
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = "Start typing...",
  className = "",
  disabled = false,
  minHeight = "200px",
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [showImageDialog, setShowImageDialog] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [linkText, setLinkText] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [imageAlt, setImageAlt] = useState("");

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && !disabled) {
      editorRef.current.innerHTML = value;
    }
  }, [value, disabled]);

  // Handle content changes
  const handleInput = () => {
    if (editorRef.current && !disabled) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  };

  // Execute formatting commands
  const execCommand = (command: string, value?: string) => {
    if (disabled) return;
    
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleInput();
  };

  // Check if command is active
  const isCommandActive = (command: string): boolean => {
    return document.queryCommandState(command);
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          execCommand('bold');
          break;
        case 'i':
          e.preventDefault();
          execCommand('italic');
          break;
        case 'u':
          e.preventDefault();
          execCommand('underline');
          break;
        case 'z':
          e.preventDefault();
          if (e.shiftKey) {
            execCommand('redo');
          } else {
            execCommand('undo');
          }
          break;
      }
    }
  };

  // Insert link
  const insertLink = () => {
    if (linkUrl && linkText) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        
        const link = document.createElement('a');
        link.href = linkUrl;
        link.textContent = linkText;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        
        range.insertNode(link);
        range.setStartAfter(link);
        range.setEndAfter(link);
        selection.removeAllRanges();
        selection.addRange(range);
      }
      
      setShowLinkDialog(false);
      setLinkUrl("");
      setLinkText("");
      handleInput();
    }
  };

  // Insert image
  const insertImage = () => {
    if (imageUrl) {
      const img = `<img src="${imageUrl}" alt="${imageAlt}" style="max-width: 100%; height: auto;" />`;
      execCommand('insertHTML', img);
      
      setShowImageDialog(false);
      setImageUrl("");
      setImageAlt("");
    }
  };

  // Format block elements
  const formatBlock = (tag: string) => {
    execCommand('formatBlock', tag);
  };

  // Get current format
  const getCurrentFormat = (): string => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const container = range.commonAncestorContainer;
      const element = container.nodeType === Node.TEXT_NODE 
        ? container.parentElement 
        : container as Element;
      
      if (element) {
        const tagName = element.tagName?.toLowerCase();
        switch (tagName) {
          case 'h1': return 'Heading 1';
          case 'h2': return 'Heading 2';
          case 'h3': return 'Heading 3';
          case 'h4': return 'Heading 4';
          case 'h5': return 'Heading 5';
          case 'h6': return 'Heading 6';
          case 'p': return 'Paragraph';
          default: return 'Normal';
        }
      }
    }
    return 'Normal';
  };

  if (isPreviewMode) {
    return (
      <div className={`border rounded-lg ${className}`}>
        <div className="flex items-center justify-between p-2 border-b bg-gray-50">
          <span className="text-sm font-medium">Preview Mode</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPreviewMode(false)}
          >
            <Eye className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
        <div 
          className="p-4 prose max-w-none"
          style={{ minHeight }}
          dangerouslySetInnerHTML={{ __html: value }}
        />
      </div>
    );
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* Toolbar */}
      <div className="flex flex-wrap items-center gap-1 p-2 border-b bg-gray-50">
        {/* Undo/Redo */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => execCommand('undo')}
          disabled={disabled}
          title="Undo (Ctrl+Z)"
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => execCommand('redo')}
          disabled={disabled}
          title="Redo (Ctrl+Shift+Z)"
        >
          <Redo className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Format Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" disabled={disabled}>
              <Type className="h-4 w-4 mr-2" />
              {getCurrentFormat()}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => formatBlock('p')}>
              Normal
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock('h1')}>
              Heading 1
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock('h2')}>
              Heading 2
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock('h3')}>
              Heading 3
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock('h4')}>
              Heading 4
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock('h5')}>
              Heading 5
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock('h6')}>
              Heading 6
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Text Formatting */}
        <Button
          variant={isCommandActive('bold') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('bold')}
          disabled={disabled}
          title="Bold (Ctrl+B)"
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant={isCommandActive('italic') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('italic')}
          disabled={disabled}
          title="Italic (Ctrl+I)"
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant={isCommandActive('underline') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('underline')}
          disabled={disabled}
          title="Underline (Ctrl+U)"
        >
          <Underline className="h-4 w-4" />
        </Button>
        <Button
          variant={isCommandActive('strikeThrough') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('strikeThrough')}
          disabled={disabled}
          title="Strikethrough"
        >
          <Strikethrough className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Alignment */}
        <Button
          variant={isCommandActive('justifyLeft') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('justifyLeft')}
          disabled={disabled}
          title="Align Left"
        >
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button
          variant={isCommandActive('justifyCenter') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('justifyCenter')}
          disabled={disabled}
          title="Align Center"
        >
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button
          variant={isCommandActive('justifyRight') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('justifyRight')}
          disabled={disabled}
          title="Align Right"
        >
          <AlignRight className="h-4 w-4" />
        </Button>
        <Button
          variant={isCommandActive('justifyFull') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('justifyFull')}
          disabled={disabled}
          title="Justify"
        >
          <AlignJustify className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Lists */}
        <Button
          variant={isCommandActive('insertUnorderedList') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('insertUnorderedList')}
          disabled={disabled}
          title="Bullet List"
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          variant={isCommandActive('insertOrderedList') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => execCommand('insertOrderedList')}
          disabled={disabled}
          title="Numbered List"
        >
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Insert Elements */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowLinkDialog(true)}
          disabled={disabled}
          title="Insert Link"
        >
          <Link className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowImageDialog(true)}
          disabled={disabled}
          title="Insert Image"
        >
          <Image className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => execCommand('formatBlock', 'blockquote')}
          disabled={disabled}
          title="Quote"
        >
          <Quote className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => execCommand('formatBlock', 'pre')}
          disabled={disabled}
          title="Code Block"
        >
          <Code className="h-4 w-4" />
        </Button>

        <div className="ml-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPreviewMode(true)}
            disabled={disabled}
          >
            <EyeOff className="h-4 w-4 mr-2" />
            Preview
          </Button>
        </div>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        className={`p-4 outline-none prose max-w-none ${disabled ? 'opacity-70 cursor-not-allowed' : 'cursor-text'}`}
        style={{ minHeight }}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />

      {/* Link Dialog */}
      <Dialog open={showLinkDialog} onOpenChange={setShowLinkDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert Link</DialogTitle>
            <DialogDescription>
              Add a link to your content
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="link-text">Link Text</Label>
              <Input
                id="link-text"
                value={linkText}
                onChange={(e) => setLinkText(e.target.value)}
                placeholder="Enter link text"
              />
            </div>
            <div>
              <Label htmlFor="link-url">URL</Label>
              <Input
                id="link-url"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLinkDialog(false)}>
              Cancel
            </Button>
            <Button onClick={insertLink} disabled={!linkUrl || !linkText}>
              Insert Link
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Image Dialog */}
      <Dialog open={showImageDialog} onOpenChange={setShowImageDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert Image</DialogTitle>
            <DialogDescription>
              Add an image to your content
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="image-url">Image URL</Label>
              <Input
                id="image-url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <div>
              <Label htmlFor="image-alt">Alt Text</Label>
              <Input
                id="image-alt"
                value={imageAlt}
                onChange={(e) => setImageAlt(e.target.value)}
                placeholder="Describe the image"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowImageDialog(false)}>
              Cancel
            </Button>
            <Button onClick={insertImage} disabled={!imageUrl}>
              Insert Image
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
