import React, { useState, use<PERSON><PERSON>back } from "react";
import { DragDropContext, Droppable, Draggable, DropResult } from "react-beautiful-dnd";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Separator } from "../ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import {
  Plus,
  Trash2,
  Edit,
  Copy,
  Move,
  Type,
  Image,
  Layout,
  Columns,
  MousePointer as <PERSON>tonIcon,
  Video,
  Map,
  Minus as SeparatorIcon,
  Code,
  Quote,
  List,
  Grid3X3,
  Smartphone,
  Tablet,
  Monitor,
  Eye,
  Settings,
  Save,
  Undo,
  Redo,
} from "lucide-react";
import { RichTextEditor } from "./RichTextEditor";

// Component types for the page builder
interface PageComponent {
  id: string;
  type: string;
  content: any;
  styles: any;
  settings: any;
}

interface PageSection {
  id: string;
  title: string;
  components: PageComponent[];
  styles: any;
  settings: any;
}

interface DragDropPageBuilderProps {
  initialSections?: PageSection[];
  onSave?: (sections: PageSection[]) => void;
  onPreview?: () => void;
  className?: string;
}

// Component library for drag and drop
const COMPONENT_LIBRARY = [
  {
    category: "Content",
    components: [
      { id: "text", name: "Text Block", icon: Type, description: "Rich text content" },
      { id: "heading", name: "Heading", icon: Type, description: "Page headings" },
      { id: "paragraph", name: "Paragraph", icon: Type, description: "Text paragraph" },
      { id: "quote", name: "Quote", icon: Quote, description: "Blockquote" },
      { id: "list", name: "List", icon: List, description: "Bullet or numbered list" },
    ]
  },
  {
    category: "Media",
    components: [
      { id: "image", name: "Image", icon: Image, description: "Single image" },
      { id: "gallery", name: "Gallery", icon: Grid3X3, description: "Image gallery" },
      { id: "video", name: "Video", icon: Video, description: "Video embed" },
    ]
  },
  {
    category: "Layout",
    components: [
      { id: "section", name: "Section", icon: Layout, description: "Container section" },
      { id: "columns", name: "Columns", icon: Columns, description: "Multi-column layout" },
      { id: "separator", name: "Separator", icon: SeparatorIcon, description: "Visual divider" },
    ]
  },
  {
    category: "Interactive",
    components: [
      { id: "button", name: "Button", icon: ButtonIcon, description: "Call-to-action button" },
      { id: "form", name: "Form", icon: Edit, description: "Contact form" },
      { id: "map", name: "Map", icon: Map, description: "Google Maps embed" },
    ]
  },
  {
    category: "Advanced",
    components: [
      { id: "code", name: "Code Block", icon: Code, description: "Code snippet" },
      { id: "html", name: "Custom HTML", icon: Code, description: "Custom HTML/CSS" },
    ]
  },
];

export function DragDropPageBuilder({
  initialSections = [],
  onSave,
  onPreview,
  className = "",
}: DragDropPageBuilderProps) {
  const { t } = useTranslation();
  const [sections, setSections] = useState<PageSection[]>(initialSections);
  const [selectedComponent, setSelectedComponent] = useState<PageComponent | null>(null);
  const [selectedSection, setSelectedSection] = useState<PageSection | null>(null);
  const [viewportSize, setViewportSize] = useState<"desktop" | "tablet" | "mobile">("desktop");
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [showComponentDialog, setShowComponentDialog] = useState(false);
  const [draggedComponent, setDraggedComponent] = useState<any>(null);
  const [history, setHistory] = useState<PageSection[][]>([initialSections]);
  const [historyIndex, setHistoryIndex] = useState(0);

  // Add to history for undo/redo
  const addToHistory = useCallback((newSections: PageSection[]) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(JSON.parse(JSON.stringify(newSections)));
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex]);

  // Undo/Redo functionality
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setSections(JSON.parse(JSON.stringify(history[historyIndex - 1])));
    }
  };

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setSections(JSON.parse(JSON.stringify(history[historyIndex + 1])));
    }
  };

  // Handle drag end
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination, type } = result;

    if (type === "SECTION") {
      // Reorder sections
      const newSections = Array.from(sections);
      const [reorderedSection] = newSections.splice(source.index, 1);
      newSections.splice(destination.index, 0, reorderedSection);
      setSections(newSections);
      addToHistory(newSections);
    } else if (type === "COMPONENT") {
      // Handle component reordering within sections
      const sourceSectionIndex = sections.findIndex(s => s.id === source.droppableId);
      const destSectionIndex = sections.findIndex(s => s.id === destination.droppableId);
      
      if (sourceSectionIndex === -1 || destSectionIndex === -1) return;

      const newSections = [...sections];
      
      if (sourceSectionIndex === destSectionIndex) {
        // Same section reorder
        const components = Array.from(newSections[sourceSectionIndex].components);
        const [reorderedComponent] = components.splice(source.index, 1);
        components.splice(destination.index, 0, reorderedComponent);
        newSections[sourceSectionIndex].components = components;
      } else {
        // Move between sections
        const sourceComponents = Array.from(newSections[sourceSectionIndex].components);
        const destComponents = Array.from(newSections[destSectionIndex].components);
        const [movedComponent] = sourceComponents.splice(source.index, 1);
        destComponents.splice(destination.index, 0, movedComponent);
        
        newSections[sourceSectionIndex].components = sourceComponents;
        newSections[destSectionIndex].components = destComponents;
      }
      
      setSections(newSections);
      addToHistory(newSections);
    }
  };

  // Add new section
  const addSection = () => {
    const newSection: PageSection = {
      id: `section-${Date.now()}`,
      title: "New Section",
      components: [],
      styles: {
        backgroundColor: "#ffffff",
        padding: "40px 20px",
        margin: "0",
      },
      settings: {
        fullWidth: false,
        maxWidth: "1200px",
      },
    };
    
    const newSections = [...sections, newSection];
    setSections(newSections);
    addToHistory(newSections);
  };

  // Add component to section
  const addComponent = (sectionId: string, componentType: string) => {
    const newComponent: PageComponent = {
      id: `component-${Date.now()}`,
      type: componentType,
      content: getDefaultContent(componentType),
      styles: getDefaultStyles(componentType),
      settings: getDefaultSettings(componentType),
    };

    const newSections = sections.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          components: [...section.components, newComponent],
        };
      }
      return section;
    });

    setSections(newSections);
    addToHistory(newSections);
    setShowComponentDialog(false);
  };

  // Get default content for component type
  const getDefaultContent = (type: string) => {
    switch (type) {
      case "text":
        return { html: "<p>Enter your text here...</p>" };
      case "heading":
        return { text: "Your Heading", level: "h2" };
      case "paragraph":
        return { text: "Your paragraph text goes here..." };
      case "image":
        return { src: "", alt: "", caption: "" };
      case "button":
        return { text: "Click Me", url: "#", style: "primary" };
      case "video":
        return { url: "", title: "", autoplay: false };
      case "map":
        return { address: "", zoom: 14, height: "400px" };
      default:
        return {};
    }
  };

  // Get default styles for component type
  const getDefaultStyles = (type: string) => {
    switch (type) {
      case "heading":
        return { fontSize: "2rem", fontWeight: "bold", color: "#333", textAlign: "left" };
      case "paragraph":
        return { fontSize: "1rem", color: "#666", lineHeight: "1.6", textAlign: "left" };
      case "button":
        return { 
          backgroundColor: "#2D7EB6", 
          color: "#ffffff", 
          padding: "12px 24px", 
          borderRadius: "6px",
          border: "none",
          fontSize: "1rem",
          fontWeight: "500",
        };
      default:
        return {};
    }
  };

  // Get default settings for component type
  const getDefaultSettings = (type: string) => {
    switch (type) {
      case "image":
        return { responsive: true, lazy: true };
      case "video":
        return { responsive: true, controls: true };
      case "button":
        return { openInNewTab: false, trackClicks: true };
      default:
        return {};
    }
  };

  // Delete component
  const deleteComponent = (sectionId: string, componentId: string) => {
    const newSections = sections.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          components: section.components.filter(c => c.id !== componentId),
        };
      }
      return section;
    });

    setSections(newSections);
    addToHistory(newSections);
    setSelectedComponent(null);
  };

  // Delete section
  const deleteSection = (sectionId: string) => {
    const newSections = sections.filter(s => s.id !== sectionId);
    setSections(newSections);
    addToHistory(newSections);
    setSelectedSection(null);
  };

  // Update component
  const updateComponent = (sectionId: string, componentId: string, updates: Partial<PageComponent>) => {
    const newSections = sections.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          components: section.components.map(component => {
            if (component.id === componentId) {
              return { ...component, ...updates };
            }
            return component;
          }),
        };
      }
      return section;
    });

    setSections(newSections);
    addToHistory(newSections);
  };

  // Update section
  const updateSection = (sectionId: string, updates: Partial<PageSection>) => {
    const newSections = sections.map(section => {
      if (section.id === sectionId) {
        return { ...section, ...updates };
      }
      return section;
    });

    setSections(newSections);
    addToHistory(newSections);
  };

  // Render component in builder
  const renderComponent = (component: PageComponent, sectionId: string, index: number) => {
    const isSelected = selectedComponent?.id === component.id;
    
    return (
      <Draggable key={component.id} draggableId={component.id} index={index}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            className={`relative group border-2 border-dashed ${
              isSelected ? 'border-blue-500 bg-blue-50' : 'border-transparent hover:border-gray-300'
            } ${snapshot.isDragging ? 'opacity-50' : ''}`}
            onClick={() => setSelectedComponent(component)}
          >
            {/* Component content preview */}
            <div className="p-4">
              {renderComponentPreview(component)}
            </div>
            
            {/* Component controls */}
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedComponent(component);
                  }}
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Duplicate component logic
                  }}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteComponent(sectionId, component.id);
                  }}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </Draggable>
    );
  };

  // Render component preview
  const renderComponentPreview = (component: PageComponent) => {
    switch (component.type) {
      case "text":
        return (
          <div 
            dangerouslySetInnerHTML={{ __html: component.content.html || "<p>Text content</p>" }}
            className="prose max-w-none"
          />
        );
      case "heading":
        const HeadingTag = component.content.level || "h2";
        return React.createElement(
          HeadingTag,
          { style: component.styles },
          component.content.text || "Heading"
        );
      case "paragraph":
        return (
          <p style={component.styles}>
            {component.content.text || "Paragraph text"}
          </p>
        );
      case "image":
        return (
          <div className="text-center">
            {component.content.src ? (
              <img 
                src={component.content.src} 
                alt={component.content.alt}
                className="max-w-full h-auto"
              />
            ) : (
              <div className="bg-gray-200 h-32 flex items-center justify-center">
                <Image className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>
        );
      case "button":
        return (
          <button style={component.styles}>
            {component.content.text || "Button"}
          </button>
        );
      default:
        return (
          <div className="bg-gray-100 p-4 text-center text-gray-500">
            {component.type} component
          </div>
        );
    }
  };

  return (
    <div className={`flex h-full ${className}`}>
      {/* Component Library Sidebar */}
      <div className="w-80 border-r bg-gray-50 overflow-y-auto">
        <div className="p-4">
          <h3 className="font-semibold mb-4">Components</h3>
          
          {COMPONENT_LIBRARY.map((category) => (
            <div key={category.category} className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                {category.category}
              </h4>
              <div className="space-y-2">
                {category.components.map((component) => (
                  <div
                    key={component.id}
                    className="p-3 border rounded-lg cursor-pointer hover:bg-white hover:shadow-sm transition-all"
                    onClick={() => {
                      setDraggedComponent(component);
                      setShowComponentDialog(true);
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <component.icon className="h-5 w-5 text-gray-600" />
                      <div>
                        <div className="font-medium text-sm">{component.name}</div>
                        <div className="text-xs text-gray-500">{component.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Builder Area */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="border-b bg-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleUndo}
                disabled={historyIndex === 0}
              >
                <Undo className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRedo}
                disabled={historyIndex === history.length - 1}
              >
                <Redo className="h-4 w-4" />
              </Button>
              
              <Separator orientation="vertical" className="h-6 mx-2" />
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPreviewMode(!isPreviewMode)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {isPreviewMode ? "Edit" : "Preview"}
              </Button>
              
              {/* Viewport Size Controls */}
              <div className="flex border rounded-md">
                <Button
                  variant={viewportSize === "desktop" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewportSize("desktop")}
                  className="rounded-r-none"
                >
                  <Monitor className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewportSize === "tablet" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewportSize("tablet")}
                  className="rounded-none"
                >
                  <Tablet className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewportSize === "mobile" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewportSize("mobile")}
                  className="rounded-l-none"
                >
                  <Smartphone className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={onPreview}>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button onClick={() => onSave?.(sections)}>
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </div>

        {/* Canvas Area */}
        <div className="flex-1 overflow-auto bg-gray-100 p-4">
          <div 
            className={`mx-auto bg-white shadow-lg ${
              viewportSize === "mobile" ? "max-w-sm" :
              viewportSize === "tablet" ? "max-w-2xl" :
              "max-w-6xl"
            }`}
          >
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="sections" type="SECTION">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {sections.map((section, index) => (
                      <Draggable key={section.id} draggableId={section.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`relative group border-2 border-dashed ${
                              selectedSection?.id === section.id ? 'border-blue-500' : 'border-transparent hover:border-gray-300'
                            } ${snapshot.isDragging ? 'opacity-50' : ''}`}
                            onClick={() => setSelectedSection(section)}
                          >
                            {/* Section Header */}
                            <div 
                              {...provided.dragHandleProps}
                              className="flex items-center justify-between p-2 bg-gray-50 border-b opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <div className="flex items-center gap-2">
                                <Move className="h-4 w-4 text-gray-400" />
                                <span className="text-sm font-medium">{section.title}</span>
                              </div>
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedSection(section);
                                  }}
                                >
                                  <Settings className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    deleteSection(section.id);
                                  }}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>

                            {/* Section Content */}
                            <div style={section.styles}>
                              <Droppable droppableId={section.id} type="COMPONENT">
                                {(provided, snapshot) => (
                                  <div
                                    {...provided.droppableProps}
                                    ref={provided.innerRef}
                                    className={`min-h-[100px] ${
                                      snapshot.isDraggingOver ? 'bg-blue-50' : ''
                                    }`}
                                  >
                                    {section.components.map((component, index) =>
                                      renderComponent(component, section.id, index)
                                    )}
                                    {provided.placeholder}
                                    
                                    {/* Add Component Button */}
                                    {section.components.length === 0 && (
                                      <div className="flex items-center justify-center h-32 text-gray-400">
                                        <Button
                                          variant="outline"
                                          onClick={() => {
                                            setSelectedSection(section);
                                            setShowComponentDialog(true);
                                          }}
                                        >
                                          <Plus className="h-4 w-4 mr-2" />
                                          Add Component
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </Droppable>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                    
                    {/* Add Section Button */}
                    <div className="p-8 text-center">
                      <Button onClick={addSection} variant="outline">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Section
                      </Button>
                    </div>
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
        </div>
      </div>

      {/* Properties Panel */}
      {(selectedComponent || selectedSection) && (
        <div className="w-80 border-l bg-white overflow-y-auto">
          <div className="p-4">
            <h3 className="font-semibold mb-4">Properties</h3>
            
            {selectedComponent && (
              <ComponentPropertiesPanel
                component={selectedComponent}
                onUpdate={(updates) => {
                  const sectionId = sections.find(s => 
                    s.components.some(c => c.id === selectedComponent.id)
                  )?.id;
                  if (sectionId) {
                    updateComponent(sectionId, selectedComponent.id, updates);
                  }
                }}
              />
            )}
            
            {selectedSection && !selectedComponent && (
              <SectionPropertiesPanel
                section={selectedSection}
                onUpdate={(updates) => updateSection(selectedSection.id, updates)}
              />
            )}
          </div>
        </div>
      )}

      {/* Component Selection Dialog */}
      <Dialog open={showComponentDialog} onOpenChange={setShowComponentDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Component</DialogTitle>
            <DialogDescription>
              Choose a component to add to your section
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
            {COMPONENT_LIBRARY.flatMap(category => 
              category.components.map(component => (
                <Button
                  key={component.id}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => {
                    if (selectedSection) {
                      addComponent(selectedSection.id, component.id);
                    }
                  }}
                >
                  <component.icon className="h-8 w-8" />
                  <div className="text-center">
                    <div className="font-medium">{component.name}</div>
                    <div className="text-xs text-gray-500">{component.description}</div>
                  </div>
                </Button>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Component Properties Panel (placeholder - would need full implementation)
function ComponentPropertiesPanel({ component, onUpdate }: { 
  component: PageComponent; 
  onUpdate: (updates: Partial<PageComponent>) => void;
}) {
  return (
    <div className="space-y-4">
      <h4 className="font-medium">Component Settings</h4>
      <p className="text-sm text-gray-500">
        Properties panel for {component.type} component
      </p>
      {/* Add specific property controls based on component type */}
    </div>
  );
}

// Section Properties Panel (placeholder - would need full implementation)
function SectionPropertiesPanel({ section, onUpdate }: { 
  section: PageSection; 
  onUpdate: (updates: Partial<PageSection>) => void;
}) {
  return (
    <div className="space-y-4">
      <h4 className="font-medium">Section Settings</h4>
      <div>
        <Label htmlFor="section-title">Section Title</Label>
        <Input
          id="section-title"
          value={section.title}
          onChange={(e) => onUpdate({ title: e.target.value })}
        />
      </div>
      {/* Add more section property controls */}
    </div>
  );
}
