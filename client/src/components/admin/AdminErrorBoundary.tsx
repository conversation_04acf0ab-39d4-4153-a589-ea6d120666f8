import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { <PERSON>ertTriangle, RefreshCw, Bug, ExternalLink } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

class AdminErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return { 
      hasError: true, 
      error,
      errorId
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('AdminErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Log to admin debug if available
    if (import.meta.env.DEV && (window as any).adminDebug) {
      console.log('🚨 Admin Component Error:', {
        component: this.props.componentName,
        error: error.message,
        stack: error.stack,
        errorInfo
      });
    }
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private getErrorMessage = (): string => {
    const { error } = this.state;
    const { componentName } = this.props;
    
    if (!error) return 'An unknown error occurred';
    
    // Common error patterns and user-friendly messages
    if (error.message.includes('fetch')) {
      return 'Unable to connect to the server. Please check your internet connection.';
    }
    
    if (error.message.includes('401') || error.message.includes('unauthorized')) {
      return 'Your session has expired. Please refresh the page to log in again.';
    }
    
    if (error.message.includes('403') || error.message.includes('forbidden')) {
      return 'You do not have permission to access this feature.';
    }
    
    if (error.message.includes('404')) {
      return 'The requested resource could not be found.';
    }
    
    if (error.message.includes('500')) {
      return 'A server error occurred. Please try again later.';
    }
    
    if (componentName) {
      return `There was an error loading the ${componentName} component.`;
    }
    
    return 'Something went wrong while loading this section.';
  };

  private getErrorSuggestions = (): string[] => {
    const { error } = this.state;
    const suggestions: string[] = [];
    
    if (!error) return suggestions;
    
    if (error.message.includes('fetch') || error.message.includes('network')) {
      suggestions.push('Check your internet connection');
      suggestions.push('Verify that the backend server is running');
      suggestions.push('Try refreshing the page');
    } else if (error.message.includes('401') || error.message.includes('403')) {
      suggestions.push('Refresh the page to re-authenticate');
      suggestions.push('Contact your administrator if the problem persists');
    } else {
      suggestions.push('Try refreshing the page');
      suggestions.push('Clear your browser cache');
      suggestions.push('Contact support if the issue continues');
    }
    
    return suggestions;
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const errorMessage = this.getErrorMessage();
      const suggestions = this.getErrorSuggestions();
      const { componentName, showDetails = false } = this.props;
      const { error, errorInfo, errorId } = this.state;

      return (
        <Card className="border-red-200 bg-red-50/50">
          <CardHeader>
            <CardTitle className="flex items-center text-red-700">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {componentName ? `${componentName} Error` : 'Component Error'}
            </CardTitle>
            <CardDescription className="text-red-600">
              {errorMessage}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {suggestions.length > 0 && (
              <Alert>
                <Bug className="h-4 w-4" />
                <AlertTitle>Troubleshooting Steps</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1 mt-2">
                    {suggestions.map((suggestion, index) => (
                      <li key={index} className="text-sm">{suggestion}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
            
            <div className="flex flex-wrap gap-2">
              <Button onClick={this.handleRetry} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button onClick={this.handleReload} variant="outline" size="sm">
                <ExternalLink className="h-4 w-4 mr-2" />
                Reload Page
              </Button>
              {import.meta.env.DEV && (
                <Button 
                  onClick={() => console.log('Error Details:', { error, errorInfo })}
                  variant="outline" 
                  size="sm"
                >
                  <Bug className="h-4 w-4 mr-2" />
                  Log Details
                </Button>
              )}
            </div>

            {showDetails && import.meta.env.DEV && error && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                  Technical Details (Development)
                </summary>
                <div className="mt-2 p-3 bg-gray-100 rounded-md">
                  <div className="text-xs space-y-2">
                    <div>
                      <strong>Error ID:</strong> {errorId}
                    </div>
                    <div>
                      <strong>Component:</strong> {componentName || 'Unknown'}
                    </div>
                    <div>
                      <strong>Error Message:</strong> {error.message}
                    </div>
                    {error.stack && (
                      <div>
                        <strong>Stack Trace:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto whitespace-pre-wrap">
                          {error.stack}
                        </pre>
                      </div>
                    )}
                    {errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto whitespace-pre-wrap">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              </details>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

export default AdminErrorBoundary;
