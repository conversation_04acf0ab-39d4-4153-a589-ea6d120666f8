import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "../../lib/queryClient";
import { useToast } from "../../hooks/use-toast";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
// Removed problematic @shared import

// Enhanced interfaces for page management
interface CustomPage {
  id: number;
  title: string;
  description: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
  language: string;  // Single language per page instance
  status: 'draft' | 'published' | 'archived';
  content?: any;
  metaTitle?: string | null;
  metaDescription?: string | null;
  ogImage?: string | null;
  canonicalUrl?: string | null;
  publishedAt?: string | null;
  templateId?: number | null;
  author?: number | null;
}

interface PageTemplate {
  id: number;
  name: string;
  description: string;
  previewImage?: string;
  contentStructure: any;
  isActive: boolean;
}

// Form validation schemas
const pageFormSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Invalid slug format"),
  description: z.string().max(500, "Description too long").optional(),
  metaTitle: z.string().max(60, "Meta title too long").optional(),
  metaDescription: z.string().max(160, "Meta description too long").optional(),
  canonicalUrl: z.string().url("Invalid URL").optional().or(z.literal("")),
  ogImage: z.string().url("Invalid image URL").optional().or(z.literal("")),
  status: z.enum(['draft', 'published', 'archived']),
  language: z.string().min(2, "Language is required"),
  templateId: z.number().optional(),
});

type PageFormData = z.infer<typeof pageFormSchema>;

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Button } from "../ui/button";
import { Separator } from "../ui/separator";
import { Label } from "../ui/label";
import { Badge } from "../ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "../ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Loader2,
  Save,
  Eye,
  FileEdit,
  Pencil,
  Plus,
  Edit,
  Trash2,
  Globe,
  Calendar,
  FileText,
  Settings,
  ExternalLink,
  Copy,
  Archive,
  MoreHorizontal,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  RefreshCw,
  ImageIcon,
  Layout,
  Smartphone,
  Tablet,
  Monitor,
  CheckCircle,
  XCircle,
  Clock,
  Grid3X3,
  List,
  ChevronDown
} from "lucide-react";

// Import the new PageEditorModal
import { PageEditorModal } from "./PageEditorModal";

// Define website pages for navigation
const WEBSITE_PAGES = [
  { id: "home", name: "Home Page", path: "/" },
  { id: "about", name: "About Us", path: "/about" },
  { id: "products", name: "Products", path: "/products" },
  { id: "contact", name: "Contact Us", path: "/contact" },
  { id: "services", name: "Services", path: "/services" },
];

// Define available languages
const LANGUAGES = [
  { code: "en", name: "English" },
  { code: "et", name: "Estonian" },
  { code: "ru", name: "Russian" },
  { code: "lv", name: "Latvian" },
  { code: "lt", name: "Lithuanian" },
  { code: "pl", name: "Polish" },
];

interface PagesProps {
  onNavigate?: (tab: string, params?: Record<string, string>) => void;
}

export function Pages({ onNavigate }: PagesProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [activePage, setActivePage] = useState("home");
  const [activeLanguage, setActiveLanguage] = useState("en");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Enhanced page editor state
  const [isPageEditorOpen, setIsPageEditorOpen] = useState(false);
  const [editingPage, setEditingPage] = useState<CustomPage | null>(null);
  const [isCreatingPage, setIsCreatingPage] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("updatedAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [selectedPages, setSelectedPages] = useState<number[]>([]);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Page editor form
  const pageForm = useForm<PageFormData>({
    resolver: zodResolver(pageFormSchema),
    defaultValues: {
      title: "",
      slug: "",
      description: "",
      metaTitle: "",
      metaDescription: "",
      canonicalUrl: "",
      ogImage: "",
      status: "draft",
      language: "en",
    },
  });
  
  // SEO form state
  const [seoForm, setSeoForm] = useState({
    id: 0,
    pagePath: "/",
    title: "",
    metaDescription: "",
    ogTitle: "",
    ogDescription: "",
    ogImage: "",
    language: "en"
  });
  
  // Content form state
  const [contentForm, setContentForm] = useState({
    sections: [] as { key: string; value: string }[],
  });
  
  // Fetch custom pages with enhanced filtering
  const { data: customPages, isLoading: isCustomPagesLoading, refetch: refetchPages } = useQuery<CustomPage[]>({
    queryKey: ['/api/admin/pages', searchQuery, statusFilter, sortBy, sortOrder],
    queryFn: async () => {
      const params = new URLSearchParams({
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter !== "all" && { status: statusFilter }),
        sortBy,
        sortOrder,
      });

      const res = await apiRequest("GET", `/api/admin/pages?${params}`);
      if (!res.ok) {
        throw new Error("Failed to fetch custom pages");
      }
      return res.json();
    },
  });

  // Fetch page templates
  const { data: pageTemplates, isLoading: isTemplatesLoading } = useQuery<PageTemplate[]>({
    queryKey: ['/api/admin/page-templates'],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/page-templates");
      if (!res.ok) {
        throw new Error("Failed to fetch page templates");
      }
      return res.json();
    },
  });
  
  // Fetch SEO settings
  const { data: seoSettings, isLoading: isSeoLoading } = useQuery<SeoSetting>({
    queryKey: ['/api/admin/seo', activePage, activeLanguage],
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/seo?pagePath=${WEBSITE_PAGES.find(p => p.id === activePage)?.path}&language=${activeLanguage}`);
      if (!res.ok) {
        throw new Error("Failed to fetch SEO settings");
      }
      return res.json();
    },
    enabled: !!activePage && !!activeLanguage,
  });
  
  // Fetch page content
  const { data: pageContent, isLoading: isContentLoading } = useQuery({
    queryKey: ['/api/admin/content', activePage, activeLanguage],
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/content?section=${activePage}&language=${activeLanguage}`);
      if (!res.ok) {
        throw new Error("Failed to fetch page content");
      }
      return res.json();
    },
    enabled: !!activePage && !!activeLanguage,
  });
  
  // Update SEO settings
  const updateSeoMutation = useMutation({
    mutationFn: async (data: any) => {
      const res = await apiRequest("POST", "/api/admin/seo", data);
      if (!res.ok) {
        throw new Error("Failed to update SEO settings");
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/seo'] });
      toast({
        title: "SEO settings updated",
        description: "The SEO settings have been updated successfully.",
      });
      
      // Create a content version for history
      createVersionMutation.mutate({
        contentType: "seo",
        contentId: activePage,
        data: seoForm,
        version: 1, // Will be incremented server-side
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  // Update page content
  const updateContentMutation = useMutation({
    mutationFn: async (sections: any[]) => {
      const promises = sections.map(section => 
        apiRequest("POST", "/api/admin/content", section)
      );
      return Promise.all(promises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/content'] });
      toast({
        title: "Content updated",
        description: "The page content has been updated successfully.",
      });
      
      // Create a content version for history
      createVersionMutation.mutate({
        contentType: "page",
        contentId: activePage,
        data: contentForm.sections,
        version: 1, // Will be incremented server-side
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  // Create content version for history
  const createVersionMutation = useMutation({
    mutationFn: async (data: any) => {
      const res = await apiRequest("POST", "/api/admin/content/version", data);
      if (!res.ok) {
        throw new Error("Failed to create content version");
      }
      return res.json();
    },
    onError: (error) => {
      console.error("Failed to create content version:", error);
    }
  });

  // Enhanced page management mutations
  const createPageMutation = useMutation({
    mutationFn: async (data: PageFormData) => {
      const res = await apiRequest("POST", "/api/admin/pages", data);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || "Failed to create page");
      }
      return res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pages'] });
      toast({
        title: "Page created",
        description: "The page has been created successfully.",
      });
      setIsPageEditorOpen(false);
      setIsCreatingPage(false);
      pageForm.reset();

      // Navigate to page builder for the new page
      if (onNavigate && data.id) {
        onNavigate('page-builder', { id: data.id.toString() });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create page",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const updatePageMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: PageFormData }) => {
      const res = await apiRequest("PUT", `/api/admin/pages/${id}`, data);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || "Failed to update page");
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pages'] });
      toast({
        title: "Page updated",
        description: "The page has been updated successfully.",
      });
      setIsPageEditorOpen(false);
      setEditingPage(null);
      pageForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update page",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const deletePageMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/admin/pages/${id}`);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || "Failed to delete page");
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pages'] });
      toast({
        title: "Page deleted",
        description: "The page has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete page",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const duplicatePageMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("POST", `/api/admin/pages/${id}/duplicate`);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || "Failed to duplicate page");
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pages'] });
      toast({
        title: "Page duplicated",
        description: "The page has been duplicated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to duplicate page",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const bulkUpdatePagesMutation = useMutation({
    mutationFn: async ({ pageIds, action, data }: { pageIds: number[]; action: string; data?: any }) => {
      const res = await apiRequest("POST", "/api/admin/pages/bulk-action", {
        pageIds,
        action,
        data,
      });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || "Failed to perform bulk action");
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pages'] });
      setSelectedPages([]);
      toast({
        title: "Bulk action completed",
        description: "The bulk action has been completed successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Bulk action failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  // Update SEO form when data changes
  useEffect(() => {
    if (seoSettings) {
      setSeoForm({
        id: seoSettings.id || 0,
        pagePath: seoSettings.pagePath || WEBSITE_PAGES.find(p => p.id === activePage)?.path || "/",
        title: seoSettings.title || "",
        metaDescription: seoSettings.metaDescription || "",
        ogTitle: seoSettings.ogTitle || "",
        ogDescription: seoSettings.ogDescription || "",
        ogImage: seoSettings.ogImage || "",
        language: seoSettings.language || activeLanguage
      });
    } else if (activePage && activeLanguage) {
      setSeoForm({
        id: 0,
        pagePath: WEBSITE_PAGES.find(p => p.id === activePage)?.path || "/",
        title: "",
        metaDescription: "",
        ogTitle: "",
        ogDescription: "",
        ogImage: "",
        language: activeLanguage
      });
    }
  }, [seoSettings, activePage, activeLanguage]);
  
  // Update content form when data changes
  useEffect(() => {
    if (pageContent) {
      setContentForm({
        sections: Array.isArray(pageContent) ? pageContent.map(item => ({
          key: item.key,
          value: item.value
        })) : []
      });
    } else {
      setContentForm({ sections: [] });
    }
  }, [pageContent]);
  
  // Handle page change
  const handlePageChange = (pageId: string) => {
    setActivePage(pageId);
    setIsEditing(false);
  };
  
  // Handle language change
  const handleLanguageChange = (lang: string) => {
    setActiveLanguage(lang);
  };
  
  // Handle SEO form change
  const handleSeoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSeoForm(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle content form change
  const handleContentChange = (key: string, value: string) => {
    setContentForm(prev => ({
      sections: prev.sections.map(section => 
        section.key === key ? { ...section, value } : section
      )
    }));
  };
  
  // Save SEO settings
  const saveSeoSettings = () => {
    updateSeoMutation.mutate(seoForm);
  };
  
  // Save page content
  const savePageContent = () => {
    const sections = contentForm.sections.map(section => ({
      section: activePage,
      key: section.key,
      value: section.value,
      language: activeLanguage
    }));
    
    updateContentMutation.mutate(sections);
  };
  
  // Toggle preview mode
  const togglePreview = () => {
    setIsPreviewOpen(!isPreviewOpen);
    
    if (!isPreviewOpen) {
      // Opening preview
      const pagePath = WEBSITE_PAGES.find(p => p.id === activePage)?.path || '/';
      const url = `${window.location.origin}${pagePath}`;
      window.open(url, '_blank');
    }
  };
  
  // Toggle editing mode
  const toggleEditing = () => {
    setIsEditing(!isEditing);
  };
  
  // Enhanced page editor handlers
  const handleCreatePage = () => {
    setIsCreatingPage(true);
    setEditingPage(null);
    pageForm.reset({
      title: "",
      slug: "",
      description: "",
      metaTitle: "",
      metaDescription: "",
      canonicalUrl: "",
      ogImage: "",
      status: "draft",
      language: activeLanguage,
    });
    setIsPageEditorOpen(true);
  };

  const handleEditCustomPage = (page: CustomPage) => {
    setIsCreatingPage(false);
    setEditingPage(page);
    pageForm.reset({
      title: page.title,
      slug: page.slug,
      description: page.description || "",
      metaTitle: page.metaTitle || "",
      metaDescription: page.metaDescription || "",
      canonicalUrl: page.canonicalUrl || "",
      ogImage: page.ogImage || "",
      status: page.status,
      language: page.language,
      templateId: page.templateId || undefined,
    });
    setIsPageEditorOpen(true);
  };

  const handleDeletePage = (page: CustomPage) => {
    if (window.confirm(`Are you sure you want to delete "${page.title}"? This action cannot be undone.`)) {
      deletePageMutation.mutate(page.id);
    }
  };

  const handleDuplicatePage = (page: CustomPage) => {
    duplicatePageMutation.mutate(page.id);
  };

  const handleBulkAction = (action: string) => {
    if (selectedPages.length === 0) {
      toast({
        title: "No pages selected",
        description: "Please select pages to perform bulk actions.",
        variant: "destructive",
      });
      return;
    }

    let confirmMessage = "";
    switch (action) {
      case "publish":
        confirmMessage = `Publish ${selectedPages.length} selected pages?`;
        break;
      case "unpublish":
        confirmMessage = `Unpublish ${selectedPages.length} selected pages?`;
        break;
      case "archive":
        confirmMessage = `Archive ${selectedPages.length} selected pages?`;
        break;
      case "delete":
        confirmMessage = `Delete ${selectedPages.length} selected pages? This action cannot be undone.`;
        break;
      default:
        return;
    }

    if (window.confirm(confirmMessage)) {
      const data = action === "publish" ? { status: "published" } :
                   action === "unpublish" ? { status: "draft" } :
                   action === "archive" ? { status: "archived" } : undefined;

      bulkUpdatePagesMutation.mutate({ pageIds: selectedPages, action, data });
    }
  };

  const handlePageSelect = (pageId: number, selected: boolean) => {
    if (selected) {
      setSelectedPages(prev => [...prev, pageId]);
    } else {
      setSelectedPages(prev => prev.filter(id => id !== pageId));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected && customPages) {
      setSelectedPages(customPages.map(page => page.id));
    } else {
      setSelectedPages([]);
    }
  };

  const onPageFormSubmit = (data: PageFormData) => {
    if (isCreatingPage) {
      createPageMutation.mutate(data);
    } else if (editingPage) {
      updatePageMutation.mutate({ id: editingPage.id, data });
    }
  };

  // Auto-generate slug from title
  const handleTitleChange = (title: string) => {
    pageForm.setValue("title", title);
    if (isCreatingPage || !editingPage?.slug) {
      const slug = title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .replace(/^-|-$/g, "");
      pageForm.setValue("slug", slug);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Website Pages</h2>
          <p className="text-muted-foreground">
            Manage your website pages, content, and SEO settings
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Select value={activeLanguage} onValueChange={handleLanguageChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Language" />
            </SelectTrigger>
            <SelectContent>
              {LANGUAGES.map(lang => (
                <SelectItem key={lang.code} value={lang.code}>
                  {lang.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={togglePreview}
          >
            <Eye className="h-4 w-4 mr-2" />
            {isPreviewOpen ? "Close Preview" : "Preview"}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={toggleEditing}
          >
            <FileEdit className="h-4 w-4 mr-2" />
            {isEditing ? "View Mode" : "Edit Mode"}
          </Button>

          <Button
            onClick={handleCreatePage}
            className="bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] hover:from-[#2D7EB6]/90 hover:to-[#40BFB9]/90"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Page
          </Button>
        </div>
      </div>

      {/* Custom Pages Management Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Custom Pages
              </CardTitle>
              <CardDescription>
                Manage your custom pages with advanced content editing and SEO optimization
              </CardDescription>
            </div>

            <div className="flex items-center gap-2">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search pages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>

              {/* Status Filter */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>

              {/* Sort */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Sort
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setSortBy("title")}>
                    Title
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("updatedAt")}>
                    Last Updated
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("createdAt")}>
                    Created Date
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("status")}>
                    Status
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}>
                    {sortOrder === "asc" ? <SortDesc className="h-4 w-4 mr-2" /> : <SortAsc className="h-4 w-4 mr-2" />}
                    {sortOrder === "asc" ? "Descending" : "Ascending"}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* View Mode Toggle */}
              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedPages.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <span className="text-sm font-medium">
                {selectedPages.length} page{selectedPages.length > 1 ? 's' : ''} selected
              </span>
              <div className="flex gap-2 ml-auto">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("publish")}
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Publish
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("unpublish")}
                >
                  <Clock className="h-4 w-4 mr-1" />
                  Unpublish
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("archive")}
                >
                  <Archive className="h-4 w-4 mr-1" />
                  Archive
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleBulkAction("delete")}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          )}
        </CardHeader>

        <CardContent>
          {isCustomPagesLoading ? (
            <div className="flex items-center justify-center py-20">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : customPages && customPages.length > 0 ? (
            <>
              {/* Select All Checkbox */}
              <div className="flex items-center gap-2 mb-4 p-2 border-b">
                <input
                  type="checkbox"
                  checked={selectedPages.length === customPages.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm font-medium">
                  Select All ({customPages.length} pages)
                </span>
              </div>

              {/* Pages Grid/List */}
              {viewMode === "grid" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {customPages.map((page) => (
                    <div
                      key={page.id}
                      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <input
                          type="checkbox"
                          checked={selectedPages.includes(page.id)}
                          onChange={(e) => handlePageSelect(page.id, e.target.checked)}
                          className="rounded mt-1"
                        />
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditCustomPage(page)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onNavigate && onNavigate('page-builder', { id: page.id.toString() })}>
                              <Layout className="h-4 w-4 mr-2" />
                              Page Builder
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDuplicatePage(page)}>
                              <Copy className="h-4 w-4 mr-2" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => window.open(`/${page.slug}`, '_blank')}>
                              <ExternalLink className="h-4 w-4 mr-2" />
                              View Page
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeletePage(page)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold truncate">{page.title}</h3>
                          <Badge
                            className={
                              page.status === 'published' ? 'bg-green-100 text-green-800' :
                              page.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }
                          >
                            {page.status}
                          </Badge>
                        </div>

                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {page.description || "No description"}
                        </p>

                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>/{page.slug}</span>
                          <span>{page.language.toUpperCase()}</span>
                          <span>{new Date(page.updatedAt).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex gap-2 mt-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditCustomPage(page)}
                          className="flex-1"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => onNavigate && onNavigate('page-builder', { id: page.id.toString() })}
                          className="flex-1 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] hover:from-[#2D7EB6]/90 hover:to-[#40BFB9]/90"
                        >
                          <Layout className="h-4 w-4 mr-1" />
                          Builder
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {customPages.map((page) => (
                    <div
                      key={page.id}
                      className="flex items-center gap-4 p-3 border rounded-lg hover:bg-gray-50"
                    >
                      <input
                        type="checkbox"
                        checked={selectedPages.includes(page.id)}
                        onChange={(e) => handlePageSelect(page.id, e.target.checked)}
                        className="rounded"
                      />

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold truncate">{page.title}</h3>
                          <Badge
                            className={
                              page.status === 'published' ? 'bg-green-100 text-green-800' :
                              page.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }
                          >
                            {page.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>/{page.slug}</span>
                          <span>{page.language.toUpperCase()}</span>
                          <span>{new Date(page.updatedAt).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditCustomPage(page)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => onNavigate && onNavigate('page-builder', { id: page.id.toString() })}
                          className="bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] hover:from-[#2D7EB6]/90 hover:to-[#40BFB9]/90"
                        >
                          <Layout className="h-4 w-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleDuplicatePage(page)}>
                              <Copy className="h-4 w-4 mr-2" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => window.open(`/${page.slug}`, '_blank')}>
                              <ExternalLink className="h-4 w-4 mr-2" />
                              View Page
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeletePage(page)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-20">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No custom pages yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first custom page to get started
              </p>
              <Button onClick={handleCreatePage} className="bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] hover:from-[#2D7EB6]/90 hover:to-[#40BFB9]/90">
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Page
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* System Pages Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            System Pages
          </CardTitle>
          <CardDescription>
            Edit content and SEO settings for core website pages
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-6">
            {/* Sidebar navigation */}
            <div className="col-span-1">
              <div className="flex flex-col space-y-2">
                {WEBSITE_PAGES.map(page => (
                  <Button
                    key={page.id}
                    variant={activePage === page.id ? "default" : "ghost"}
                    className="justify-start"
                    onClick={() => handlePageChange(page.id)}
                  >
                    {page.name}
                  </Button>
                ))}
              </div>
            </div>
        
        {/* Main content */}
        <div className="col-span-3">
          <Tabs defaultValue="content">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="content">Page Content</TabsTrigger>
              <TabsTrigger value="seo">SEO Settings</TabsTrigger>
            </TabsList>
            
            {/* Content Tab */}
            <TabsContent value="content" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>
                    {`${WEBSITE_PAGES.find(p => p.id === activePage)?.name} Content`}
                  </CardTitle>
                  <CardDescription>
                    Edit the content for this page in {LANGUAGES.find(l => l.code === activeLanguage)?.name}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isContentLoading ? (
                    <div className="flex items-center justify-center py-10">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : contentForm.sections.length > 0 ? (
                    <>
                      {contentForm.sections.map((section, index) => (
                        <div key={index} className="space-y-2">
                          <div className="font-medium capitalize">
                            {section.key.replace(/([A-Z])/g, ' $1').trim()}
                          </div>
                          
                          {section.value.length > 100 ? (
                            <Textarea
                              rows={6}
                              name={section.key}
                              value={section.value}
                              onChange={(e) => handleContentChange(section.key, e.target.value)}
                              disabled={!isEditing}
                              className={!isEditing ? "opacity-70" : ""}
                            />
                          ) : (
                            <Input
                              name={section.key}
                              value={section.value}
                              onChange={(e) => handleContentChange(section.key, e.target.value)}
                              disabled={!isEditing}
                              className={!isEditing ? "opacity-70" : ""}
                            />
                          )}
                          
                          <Separator className="my-4" />
                        </div>
                      ))}
                      
                      {isEditing && (
                        <Button 
                          onClick={savePageContent}
                          className="mt-4"
                          disabled={updateContentMutation.isPending}
                        >
                          {updateContentMutation.isPending && (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          )}
                          <Save className="mr-2 h-4 w-4" />
                          Save Changes
                        </Button>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-10 text-muted-foreground">
                      <p>No content sections found for this page in {LANGUAGES.find(l => l.code === activeLanguage)?.name}.</p>
                      {isEditing && (
                        <Button 
                          variant="outline"
                          className="mt-4"
                        >
                          Initialize Content
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* SEO Tab */}
            <TabsContent value="seo" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>SEO Settings</CardTitle>
                  <CardDescription>
                    Optimize search engine visibility for {WEBSITE_PAGES.find(p => p.id === activePage)?.name}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isSeoLoading ? (
                    <div className="flex items-center justify-center py-10">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <>
                      <div className="space-y-2">
                        <div className="font-medium">Page Title</div>
                        <Input
                          name="title"
                          value={seoForm.title}
                          onChange={handleSeoChange}
                          placeholder="Page title (60-70 characters recommended)"
                          disabled={!isEditing}
                          className={!isEditing ? "opacity-70" : ""}
                          maxLength={70}
                        />
                        {seoForm.title && (
                          <div className="text-xs text-muted-foreground">
                            {seoForm.title.length}/70 characters
                          </div>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <div className="font-medium">Meta Description</div>
                        <Textarea
                          name="metaDescription"
                          value={seoForm.metaDescription}
                          onChange={handleSeoChange}
                          placeholder="Meta description (150-160 characters recommended)"
                          rows={3}
                          disabled={!isEditing}
                          className={!isEditing ? "opacity-70" : ""}
                          maxLength={160}
                        />
                        {seoForm.metaDescription && (
                          <div className="text-xs text-muted-foreground">
                            {seoForm.metaDescription.length}/160 characters
                          </div>
                        )}
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <div className="font-medium">Open Graph Title</div>
                        <Input
                          name="ogTitle"
                          value={seoForm.ogTitle}
                          onChange={handleSeoChange}
                          placeholder="Title for social media sharing"
                          disabled={!isEditing}
                          className={!isEditing ? "opacity-70" : ""}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <div className="font-medium">Open Graph Description</div>
                        <Textarea
                          name="ogDescription"
                          value={seoForm.ogDescription}
                          onChange={handleSeoChange}
                          placeholder="Description for social media sharing"
                          rows={3}
                          disabled={!isEditing}
                          className={!isEditing ? "opacity-70" : ""}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <div className="font-medium">Open Graph Image URL</div>
                        <Input
                          name="ogImage"
                          value={seoForm.ogImage}
                          onChange={handleSeoChange}
                          placeholder="URL to image for social media sharing"
                          disabled={!isEditing}
                          className={!isEditing ? "opacity-70" : ""}
                        />
                      </div>
                      
                      {isEditing && (
                        <Button 
                          onClick={saveSeoSettings}
                          className="mt-4"
                          disabled={updateSeoMutation.isPending}
                        >
                          {updateSeoMutation.isPending && (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          )}
                          <Save className="mr-2 h-4 w-4" />
                          Save SEO Settings
                        </Button>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
          </div>
        </CardContent>
      </Card>

      {/* Page Editor Modal */}
      <PageEditorModal
        isOpen={isPageEditorOpen}
        onClose={() => {
          setIsPageEditorOpen(false);
          setEditingPage(null);
          setIsCreatingPage(false);
          pageForm.reset();
        }}
        onSubmit={onPageFormSubmit}
        isCreating={isCreatingPage}
        editingPage={editingPage}
        pageTemplates={pageTemplates || []}
        isLoading={createPageMutation.isPending || updatePageMutation.isPending}
        form={pageForm}
      />
    </div>
  );
}