import { useAuth } from "../../hooks/use-auth";
import { useEffect } from "react";
import { Route, Redirect } from "wouter";
import { Loader2 } from "lucide-react";

interface ProtectedRouteProps {
  path: string;
  component: React.ComponentType;
  adminOnly?: boolean;
}

export function ProtectedRoute({
  path,
  component: Component,
  adminOnly = true
}: ProtectedRouteProps) {
  const { user, loading } = useAuth();

  if (import.meta.env.DEV) {
    console.log('🔒 ProtectedRoute check:', { user, loading, adminOnly, path });
  }

  // Force a refresh of admin data when user is authenticated
  // Always call hooks unconditionally at the top level
  useEffect(() => {
    if (user && user.isAdmin) {
      // Refresh admin data in the background
      const API_BASE_URL = import.meta.env.VITE_API_URL || '';
      fetch(`${API_BASE_URL}/api/admin/me`, { credentials: "include" })
        .catch(err => console.error("Error refreshing auth:", err));
    }
  }, [user]);

  if (loading) {
    if (import.meta.env.DEV) {
      console.log('⏳ ProtectedRoute: Loading...');
    }
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-border" />
        </div>
      </Route>
    );
  }

  if (!user || (adminOnly && !user.isAdmin)) {
    if (import.meta.env.DEV) {
      console.log('🚫 ProtectedRoute: Access denied, redirecting to login');
    }
    return (
      <Route path={path}>
        <Redirect to="/admin/login" />
      </Route>
    );
  }

  if (import.meta.env.DEV) {
    console.log('✅ ProtectedRoute: Access granted, rendering component');
  }
  
  // Use Route with children pattern instead of component prop to avoid TypeScript errors
  return (
    <Route path={path}>
      <Component />
    </Route>
  );
}