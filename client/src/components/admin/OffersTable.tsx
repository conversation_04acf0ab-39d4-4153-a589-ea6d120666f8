import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAdminOffers, AdminOffer } from "../../hooks/use-admin-api";
import { useToast } from "../../hooks/use-toast";
import { apiRequest } from "../../lib/queryClient";
import { format } from "date-fns";

// UI Components
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Badge } from "../ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { 
  DollarSign,
  FileText,
  Loader2, 
  Plus,
  RefreshCw,
} from "lucide-react";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

export function OffersTable() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newOffer, setNewOffer] = useState({
    clientName: "",
    clientEmail: "",
    amount: "",
    description: "",
    validUntil: "",
    status: "draft" as const,
  });

  const { data: offers, isLoading, refetch } = useAdminOffers();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Create offer mutation
  const createOfferMutation = useMutation({
    mutationFn: async (offerData: Partial<AdminOffer>) => {
      const response = await apiRequest('POST', '/api/admin/offers', offerData);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'offers'] });
      toast({
        title: "Success",
        description: "Offer created successfully",
      });
      setIsCreateDialogOpen(false);
      setNewOffer({
        clientName: "",
        clientEmail: "",
        amount: "",
        description: "",
        validUntil: "",
        status: "draft",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to create offer: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const handleCreateOffer = async () => {
    if (!newOffer.clientName || !newOffer.clientEmail || !newOffer.amount || !newOffer.description) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    await createOfferMutation.mutateAsync({
      ...newOffer,
      amount: parseFloat(newOffer.amount),
    });
  };

  // Get status badge styling
  const getStatusBadge = (status: AdminOffer['status']) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline" className="text-gray-500 border-gray-500">Draft</Badge>;
      case "sent":
        return <Badge variant="outline" className="text-blue-500 border-blue-500">Sent</Badge>;
      case "accepted":
        return <Badge variant="outline" className="text-green-500 border-green-500">Accepted</Badge>;
      case "declined":
        return <Badge variant="outline" className="text-red-500 border-red-500">Declined</Badge>;
    }
  };

  // Format date
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return format(date, "MMM d, yyyy");
    } catch (error) {
      return dateString;
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <div>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2 text-primary" />
              Offers Management
            </CardTitle>
            <CardDescription className="mt-1.5">
              Create and manage client offers
            </CardDescription>
          </div>
          
          <div className="flex gap-2">
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Offer
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Create New Offer</DialogTitle>
                  <DialogDescription>
                    Create a new offer for a client
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="clientName">Client Name *</Label>
                    <Input
                      id="clientName"
                      value={newOffer.clientName}
                      onChange={(e) => setNewOffer({ ...newOffer, clientName: e.target.value })}
                      placeholder="Enter client name"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="clientEmail">Client Email *</Label>
                    <Input
                      id="clientEmail"
                      type="email"
                      value={newOffer.clientEmail}
                      onChange={(e) => setNewOffer({ ...newOffer, clientEmail: e.target.value })}
                      placeholder="Enter client email"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="amount">Amount (€) *</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={newOffer.amount}
                      onChange={(e) => setNewOffer({ ...newOffer, amount: e.target.value })}
                      placeholder="Enter amount"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      value={newOffer.description}
                      onChange={(e) => setNewOffer({ ...newOffer, description: e.target.value })}
                      placeholder="Enter offer description"
                      rows={3}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="validUntil">Valid Until</Label>
                    <Input
                      id="validUntil"
                      type="date"
                      value={newOffer.validUntil}
                      onChange={(e) => setNewOffer({ ...newOffer, validUntil: e.target.value })}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={newOffer.status} onValueChange={(value: any) => setNewOffer({ ...newOffer, status: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="sent">Sent</SelectItem>
                        <SelectItem value="accepted">Accepted</SelectItem>
                        <SelectItem value="declined">Declined</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateOffer} disabled={createOfferMutation.isPending}>
                    {createOfferMutation.isPending ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Create Offer
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {offers && offers.length > 0 ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Valid Until</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {offers.map((offer: AdminOffer) => (
                  <TableRow key={offer.id}>
                    <TableCell className="font-medium">#{offer.id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{offer.clientName}</div>
                        <div className="text-sm text-muted-foreground">{offer.clientEmail}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                        €{offer.amount.toLocaleString()}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(offer.status)}</TableCell>
                    <TableCell className="text-muted-foreground">
                      {offer.validUntil ? formatDate(offer.validUntil) : 'No expiry'}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {formatDate(offer.createdAt)}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="rounded-md border flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No offers yet</h3>
            <p className="text-muted-foreground mt-1 mb-4">
              Create your first offer to get started
            </p>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Offer
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
