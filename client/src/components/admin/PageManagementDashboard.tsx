import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "../../lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { Badge } from "../ui/badge";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts";
import {
  FileText,
  Eye,
  Edit,
  TrendingUp,
  Users,
  Globe,
  Calendar,
  Activity,
  BarChart3,
  <PERSON><PERSON>hart as PieChartIcon,
  LineChart as LineChartIcon,
} from "lucide-react";

interface PageStats {
  totalPages: number;
  publishedPages: number;
  draftPages: number;
  archivedPages: number;
  totalViews: number;
  uniqueVisitors: number;
  avgTimeOnPage: number;
  bounceRate: number;
}

interface PageAnalytics {
  pageId: number;
  title: string;
  slug: string;
  views: number;
  uniqueVisitors: number;
  avgTimeOnPage: number;
  bounceRate: number;
  lastUpdated: string;
}

interface LanguageStats {
  language: string;
  pageCount: number;
  views: number;
}

interface StatusDistribution {
  status: string;
  count: number;
  color: string;
}

export function PageManagementDashboard() {
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d">("30d");

  // Fetch page statistics
  const { data: pageStats, isLoading: isStatsLoading } = useQuery<PageStats>({
    queryKey: ['/api/admin/pages/stats', timeRange],
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/pages/stats?range=${timeRange}`);
      if (!res.ok) {
        throw new Error("Failed to fetch page statistics");
      }
      return res.json();
    },
  });

  // Fetch page analytics
  const { data: pageAnalytics, isLoading: isAnalyticsLoading } = useQuery<PageAnalytics[]>({
    queryKey: ['/api/admin/pages/analytics', timeRange],
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/pages/analytics?range=${timeRange}`);
      if (!res.ok) {
        throw new Error("Failed to fetch page analytics");
      }
      return res.json();
    },
  });

  // Fetch language statistics
  const { data: languageStats, isLoading: isLanguageLoading } = useQuery<LanguageStats[]>({
    queryKey: ['/api/admin/pages/language-stats'],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/pages/language-stats");
      if (!res.ok) {
        throw new Error("Failed to fetch language statistics");
      }
      return res.json();
    },
  });

  // Mock data for demonstration (replace with real API data)
  const mockPageStats: PageStats = {
    totalPages: 24,
    publishedPages: 18,
    draftPages: 4,
    archivedPages: 2,
    totalViews: 15420,
    uniqueVisitors: 8930,
    avgTimeOnPage: 145,
    bounceRate: 32.5,
  };

  const mockPageAnalytics: PageAnalytics[] = [
    { pageId: 1, title: "Home Page", slug: "/", views: 5420, uniqueVisitors: 3210, avgTimeOnPage: 180, bounceRate: 25.3, lastUpdated: "2024-01-15" },
    { pageId: 2, title: "About Us", slug: "/about", views: 2340, uniqueVisitors: 1890, avgTimeOnPage: 165, bounceRate: 28.7, lastUpdated: "2024-01-14" },
    { pageId: 3, title: "Products", slug: "/products", views: 3890, uniqueVisitors: 2450, avgTimeOnPage: 220, bounceRate: 18.9, lastUpdated: "2024-01-13" },
    { pageId: 4, title: "Contact", slug: "/contact", views: 1560, uniqueVisitors: 1120, avgTimeOnPage: 95, bounceRate: 45.2, lastUpdated: "2024-01-12" },
    { pageId: 5, title: "Services", slug: "/services", views: 2210, uniqueVisitors: 1260, avgTimeOnPage: 190, bounceRate: 22.1, lastUpdated: "2024-01-11" },
  ];

  const mockLanguageStats: LanguageStats[] = [
    { language: "English", pageCount: 12, views: 8420 },
    { language: "Estonian", pageCount: 8, views: 3210 },
    { language: "Russian", pageCount: 6, views: 2890 },
    { language: "Latvian", pageCount: 4, views: 1560 },
    { language: "Lithuanian", pageCount: 3, views: 1240 },
    { language: "Polish", pageCount: 2, views: 890 },
  ];

  const statusDistribution: StatusDistribution[] = [
    { status: "Published", count: mockPageStats.publishedPages, color: "#10B981" },
    { status: "Draft", count: mockPageStats.draftPages, color: "#F59E0B" },
    { status: "Archived", count: mockPageStats.archivedPages, color: "#6B7280" },
  ];

  const stats = pageStats || mockPageStats;
  const analytics = pageAnalytics || mockPageAnalytics;
  const langStats = languageStats || mockLanguageStats;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Page Management Dashboard</h2>
          <p className="text-muted-foreground">
            Overview of your website pages performance and analytics
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={timeRange === "7d" ? "default" : "outline"}
            size="sm"
            onClick={() => setTimeRange("7d")}
          >
            7 Days
          </Button>
          <Button
            variant={timeRange === "30d" ? "default" : "outline"}
            size="sm"
            onClick={() => setTimeRange("30d")}
          >
            30 Days
          </Button>
          <Button
            variant={timeRange === "90d" ? "default" : "outline"}
            size="sm"
            onClick={() => setTimeRange("90d")}
          >
            90 Days
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pages</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPages}</div>
            <p className="text-xs text-muted-foreground">
              {stats.publishedPages} published, {stats.draftPages} drafts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalViews.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12.5% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unique Visitors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.uniqueVisitors.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +8.2% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Time on Page</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.floor(stats.avgTimeOnPage / 60)}m {stats.avgTimeOnPage % 60}s</div>
            <p className="text-xs text-muted-foreground">
              Bounce rate: {stats.bounceRate}%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Page Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              Page Status Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {statusDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Language Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Pages by Language
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={langStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="language" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="pageCount" fill="#2D7EB6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Pages */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Top Performing Pages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.slice(0, 5).map((page, index) => (
              <div key={page.pageId} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 font-semibold">
                    {index + 1}
                  </div>
                  <div>
                    <h3 className="font-semibold">{page.title}</h3>
                    <p className="text-sm text-muted-foreground">{page.slug}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-6 text-sm">
                  <div className="text-center">
                    <div className="font-semibold">{page.views.toLocaleString()}</div>
                    <div className="text-muted-foreground">Views</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{page.uniqueVisitors.toLocaleString()}</div>
                    <div className="text-muted-foreground">Visitors</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{Math.floor(page.avgTimeOnPage / 60)}m {page.avgTimeOnPage % 60}s</div>
                    <div className="text-muted-foreground">Avg. Time</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{page.bounceRate}%</div>
                    <div className="text-muted-foreground">Bounce Rate</div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Page Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <div className="flex-1">
                <p className="font-medium">Home Page updated</p>
                <p className="text-sm text-muted-foreground">Updated hero section content</p>
              </div>
              <Badge variant="outline">2 hours ago</Badge>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 rounded-full bg-blue-500"></div>
              <div className="flex-1">
                <p className="font-medium">New page created</p>
                <p className="text-sm text-muted-foreground">Product Catalog page added</p>
              </div>
              <Badge variant="outline">1 day ago</Badge>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
              <div className="flex-1">
                <p className="font-medium">About Us page published</p>
                <p className="text-sm text-muted-foreground">Moved from draft to published</p>
              </div>
              <Badge variant="outline">2 days ago</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
