import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  Users, 
  MessageSquare, 
  FileText, 
  Package, 
  TrendingUp,
  Bell,
  FolderKanban,
  Tag,
  Building,
  CheckCircle,
  Clock,
  AlertCircle,
  ChevronRight
} from 'lucide-react';
import { apiRequest } from '../../lib/queryClient';

interface DashboardStats {
  stats: {
    users: {
      total: number;
      admins: number;
      editors: number;
      viewers: number;
    };
    inquiries: {
      total: number;
      new: number;
      inProgress: number;
      resolved: number;
      archived: number;
    };
    quoteRequests: {
      total: number;
      new: number;
      reviewing: number;
      quoted: number;
      accepted: number;
      rejected: number;
    };
    crm: {
      totalClients: number;
      newLeads: number;
      qualified: number;
      proposal: number;
      closedWon: number;
    };
    offers: {
      total: number;
      draft: number;
      sent: number;
      accepted: number;
      rejected: number;
      expired: number;
    };
    content: {
      documents: number;
      projects: number;
      products: number;
      publishedProjects: number;
    };
    notifications: {
      unread: number;
    };
  };
  recentActivity: {
    inquiries: Array<{
      id: number;
      name: string;
      email: string;
      message: string;
      status: string;
      createdAt: string;
    }>;
    quoteRequests: Array<{
      id: number;
      productName: string;
      clientName: string;
      status: string;
      createdAt: string;
    }>;
  };
}

interface DashboardStatsProps {
  onNavigate: (tab: string) => void;
}

export function DashboardStats({ onNavigate }: DashboardStatsProps) {
  const API_BASE_URL = import.meta.env.VITE_API_URL || (
    import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
  );

  if (import.meta.env.DEV) {
    console.log('🔧 DashboardStats component mounted');
    console.log('🌐 API_BASE_URL:', API_BASE_URL);
  }

  const { data: dashboardData, isLoading, error } = useQuery({
    queryKey: ['/api/admin/dashboard'],
    queryFn: async () => {
      try {
        if (import.meta.env.DEV) {
          console.log('📊 Fetching dashboard data from:', `${API_BASE_URL}/api/admin/dashboard`);
        }

        const response = await fetch(`${API_BASE_URL}/api/admin/dashboard`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (import.meta.env.DEV) {
          console.log('📊 Dashboard API response status:', response.status);
        }

        if (response.status === 401) {
          console.warn('🔒 Dashboard API returned 401, redirecting to login');
          window.location.href = '/admin/login';
          return;
        }

        const data = await response.json();
        if (import.meta.env.DEV) {
          console.log('📊 Received dashboard data:', data);
        }

        if (data.success) {
          return data as DashboardStats;
        } else {
          throw new Error(data.message || 'Failed to fetch dashboard data');
        }
      } catch (error) {
        console.error('❌ Dashboard error:', error);
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true,
    retry: 2,
  });



  if (isLoading) {
    return (
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(8)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-muted rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-muted rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-muted-foreground">Failed to load dashboard data</p>
          {error && (
            <p className="text-sm text-red-500 mt-2">{error.message}</p>
          )}
          <Button variant="outline" className="mt-2" onClick={() => window.location.reload()}>
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const { stats, recentActivity } = dashboardData;

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {/* Users Stats */}
        <Card className="admin-card-metanord cursor-pointer hover:shadow-md transition-all duration-300" onClick={() => onNavigate('users')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Users</CardTitle>
            <Users className="h-4 w-4" style={{ color: '#2D7EB6' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: '#2D7EB6' }}>{stats.users.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.users.admins} admins, {stats.users.editors} editors
            </p>
          </CardContent>
        </Card>

        {/* Inquiries Stats */}
        <Card className="admin-card-metanord cursor-pointer hover:shadow-md transition-all duration-300" onClick={() => onNavigate('inquiries')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inquiries</CardTitle>
            <MessageSquare className="h-4 w-4" style={{ color: '#40BFB9' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: '#40BFB9' }}>{stats.inquiries.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.inquiries.new} new, {stats.inquiries.inProgress} in progress
            </p>
          </CardContent>
        </Card>

        {/* Quote Requests Stats */}
        <Card className="admin-card-metanord cursor-pointer hover:shadow-md transition-all duration-300" onClick={() => onNavigate('quotes')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quote Requests</CardTitle>
            <Tag className="h-4 w-4" style={{ color: '#2D7EB6' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: '#2D7EB6' }}>{stats.quoteRequests.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.quoteRequests.new} new, {stats.quoteRequests.reviewing} reviewing
            </p>
          </CardContent>
        </Card>

        {/* CRM Stats */}
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onNavigate('crm')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CRM Clients</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.crm.totalClients}</div>
            <p className="text-xs text-muted-foreground">
              {stats.crm.newLeads} new leads, {stats.crm.qualified} qualified
            </p>
          </CardContent>
        </Card>

        {/* Offers Stats */}
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onNavigate('offers')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Offers</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.offers.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.offers.sent} sent, {stats.offers.accepted} accepted
            </p>
          </CardContent>
        </Card>

        {/* Content Stats */}
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onNavigate('projects')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Projects</CardTitle>
            <FolderKanban className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.content.projects}</div>
            <p className="text-xs text-muted-foreground">
              {stats.content.publishedProjects} published
            </p>
          </CardContent>
        </Card>

        {/* Products Stats */}
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onNavigate('products')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.content.products}</div>
            <p className="text-xs text-muted-foreground">
              {stats.content.documents} documents
            </p>
          </CardContent>
        </Card>

        {/* Notifications Stats */}
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onNavigate('notifications')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Notifications</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.notifications.unread}</div>
            <p className="text-xs text-muted-foreground">
              Unread notifications
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {/* Recent Inquiries */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Recent Inquiries</CardTitle>
            <Button variant="ghost" size="sm" onClick={() => onNavigate('inquiries')}>
              View All <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardHeader>
          <CardContent>
            {recentActivity.inquiries.length > 0 ? (
              <div className="space-y-3">
                {recentActivity.inquiries.slice(0, 3).map((inquiry) => (
                  <div key={inquiry.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                    <MessageSquare className="h-4 w-4 mt-1 text-muted-foreground" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{inquiry.name}</p>
                      <p className="text-xs text-muted-foreground truncate">{inquiry.email}</p>
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2">{inquiry.message}</p>
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant={inquiry.status === 'new' ? 'default' : 'secondary'} className="text-xs">
                          {inquiry.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(inquiry.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">No recent inquiries</p>
            )}
          </CardContent>
        </Card>

        {/* Recent Quote Requests */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Recent Quote Requests</CardTitle>
            <Button variant="ghost" size="sm" onClick={() => onNavigate('quotes')}>
              View All <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardHeader>
          <CardContent>
            {recentActivity.quoteRequests.length > 0 ? (
              <div className="space-y-3">
                {recentActivity.quoteRequests.slice(0, 3).map((quote) => (
                  <div key={quote.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                    <Tag className="h-4 w-4 mt-1 text-muted-foreground" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{quote.productName}</p>
                      <p className="text-xs text-muted-foreground truncate">{quote.clientName}</p>
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant={quote.status === 'new' ? 'default' : 'secondary'} className="text-xs">
                          {quote.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(quote.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">No recent quote requests</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
