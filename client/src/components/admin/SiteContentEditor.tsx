import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useToast } from "../../hooks/use-toast";
import { apiRequest, queryClient } from "../../lib/queryClient";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

// UI Components
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "../ui/form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { ScrollArea } from "../ui/scroll-area";
import { Switch } from "../ui/switch";
import { 
  Check, 
  Eye, 
  Globe,
  Info, 
  Loader2, 
  MailIcon,
  MapPin,
  PhoneCall, 
  RefreshCw, 
  Save, 
  X,
  FileText,
  Edit
} from "lucide-react";
import { MobileFormLayout } from "../admin/MobileFormLayout";
import { useMediaQuery } from "../../hooks/use-media-query";

// Languages supported by the site
const LANGUAGES = [
  { code: "en", name: "English" },
  { code: "et", name: "Estonian" },
  { code: "ru", name: "Russian" },
  { code: "lv", name: "Latvian" },
  { code: "lt", name: "Lithuanian" },
  { code: "pl", name: "Polish" },
];

// Different content sections
const CONTENT_SECTIONS = [
  { id: "about", name: "About Us" },
  { id: "footer", name: "Footer" },
  { id: "contact", name: "Contact" },
  { id: "certificates", name: "Certificates" },
];

// Schema for site content form
const siteContentSchema = z.object({
  section: z.string().min(1, "Section is required"),
  key: z.string().min(1, "Key is required"),
  value: z.string().min(1, "Content value is required"),
  language: z.string().min(2, "Language is required"),
});

// Schema for multilingual site content
const multilingualContentSchema = z.object({
  section: z.string().min(1, "Section is required"),
  key: z.string().min(1, "Key is required"),
  values: z.record(z.string().min(1, "Content is required")),
});

type SiteContentForm = z.infer<typeof siteContentSchema>;
type MultilingualContentForm = z.infer<typeof multilingualContentSchema>;

// Interface for site content data
interface SiteContent {
  id: number;
  section: string;
  key: string;
  value: string;
  language: string;
}

// Main Site Content Editor Component
export function SiteContentEditor() {
  const [activeSection, setActiveSection] = useState("about");
  const [activeLanguage, setActiveLanguage] = useState("en");
  const [showPreview, setShowPreview] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedContent, setSelectedContent] = useState<SiteContent | null>(null);
  const { toast } = useToast();
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Fetch site content
  const { data: content = [], isLoading, refetch } = useQuery({
    queryKey: ['/api/admin/content'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/content');
      if (!res.ok) throw new Error('Failed to fetch content');
      return await res.json();
    },
  });

  // Save content mutation
  const saveContentMutation = useMutation({
    mutationFn: async (contentData: Omit<SiteContent, 'id'>) => {
      const res = await apiRequest('POST', '/api/admin/content', contentData);
      if (!res.ok) throw new Error('Failed to save content');
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/content'] });
      toast({
        title: "Success",
        description: "Content saved successfully",
      });
      setIsEditDialogOpen(false);
      setSelectedContent(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update content mutation
  const updateContentMutation = useMutation({
    mutationFn: async (contentData: SiteContent) => {
      const res = await apiRequest('PUT', `/api/admin/content/${contentData.id}`, contentData);
      if (!res.ok) throw new Error('Failed to update content');
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/content'] });
      toast({
        title: "Success",
        description: "Content updated successfully",
      });
      setIsEditDialogOpen(false);
      setSelectedContent(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete content mutation
  const deleteContentMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest('DELETE', `/api/admin/content/${id}`);
      if (!res.ok) throw new Error('Failed to delete content');
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/content'] });
      toast({
        title: "Success",
        description: "Content deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Filter content by section and language
  const filteredContent = content.filter(item =>
    item.section === activeSection && item.language === activeLanguage
  );

  // Get content for preview (all languages for current section)
  const previewContent = content.filter(item => item.section === activeSection);

  // Handle edit content
  const handleEditContent = (contentItem: SiteContent) => {
    setSelectedContent(contentItem);
    setIsEditDialogOpen(true);
  };

  // Handle create new content
  const handleCreateContent = () => {
    setSelectedContent({
      id: 0,
      section: activeSection,
      key: '',
      value: '',
      language: activeLanguage,
    });
    setIsEditDialogOpen(true);
  };

  // Handle save content
  const handleSaveContent = () => {
    if (!selectedContent) return;

    if (selectedContent.id === 0) {
      // Create new content
      const { id, ...contentData } = selectedContent;
      saveContentMutation.mutate(contentData);
    } else {
      // Update existing content
      updateContentMutation.mutate(selectedContent);
    }
  };

  // Handle delete content
  const handleDeleteContent = (id: number) => {
    if (window.confirm('Are you sure you want to delete this content?')) {
      deleteContentMutation.mutate(id);
    }
  };

  // Content preview component
  const ContentPreview = ({ content, section }: { content: SiteContent[]; section: string }) => {
    const groupedContent: Record<string, Record<string, string>> = {};

    // Group content by key and language
    content.forEach(item => {
      if (item.section === section) {
        if (!groupedContent[item.key]) {
          groupedContent[item.key] = {};
        }
        groupedContent[item.key][item.language] = item.value;
      }
    });

    const renderAboutSection = () => (
      <div className="space-y-4 p-4 border rounded-md bg-white">
        <h2 className="text-xl font-bold text-slate-800">About Us</h2>
        <div className="prose prose-sm max-w-none">
          {groupedContent['company_description'] && (
            <p>{groupedContent['company_description']['en'] || ''}</p>
          )}

          {groupedContent['mission'] && (
            <>
              <h3 className="text-md font-semibold">Our Mission</h3>
              <p>{groupedContent['mission']['en'] || ''}</p>
            </>
          )}

          {groupedContent['values'] && (
            <>
              <h3 className="text-md font-semibold">Our Values</h3>
              <p>{groupedContent['values']['en'] || ''}</p>
            </>
          )}
        </div>
      </div>
    );

    const renderFooterSection = () => (
      <div className="space-y-4 p-4 border rounded-md bg-slate-900 text-white">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="text-sm font-semibold mb-2 uppercase">MetaNord OÜ</h3>
            <p className="text-xs text-slate-300">
              {groupedContent['company_tagline'] && groupedContent['company_tagline']['en']}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-semibold mb-2 uppercase">Contact</h3>
            <div className="space-y-2 text-xs text-slate-300">
              <p>
                {groupedContent['address'] && groupedContent['address']['en']}
              </p>
              <p>
                {groupedContent['email'] && groupedContent['email']['en']}
              </p>
              <p>
                {groupedContent['phone'] && groupedContent['phone']['en']}
              </p>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-semibold mb-2 uppercase">Legal</h3>
            <div className="space-y-1 text-xs text-slate-300">
              {groupedContent['legal_text'] && (
                <p>{groupedContent['legal_text']['en']}</p>
              )}
            </div>
          </div>
        </div>

        <div className="pt-4 border-t border-slate-700 text-xs text-center text-slate-400">
          {groupedContent['copyright'] && groupedContent['copyright']['en']}
        </div>
      </div>
    );

    const renderContactSection = () => (
      <div className="space-y-4 p-4 border rounded-md bg-white">
        <h2 className="text-xl font-bold text-slate-800">Contact Us</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <MapPin className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h3 className="text-sm font-semibold">Address</h3>
                <p className="text-sm text-slate-600">
                  {groupedContent['address'] && groupedContent['address']['en']}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <MailIcon className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h3 className="text-sm font-semibold">Email</h3>
                <p className="text-sm text-slate-600">
                  {groupedContent['email'] && groupedContent['email']['en']}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <PhoneCall className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h3 className="text-sm font-semibold">Phone</h3>
                <p className="text-sm text-slate-600">
                  {groupedContent['phone'] && groupedContent['phone']['en']}
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="h-40 border rounded bg-slate-100 flex items-center justify-center text-slate-400">
              Map Placeholder
            </div>

            <p className="text-sm text-slate-600">
              {groupedContent['contact_text'] && groupedContent['contact_text']['en']}
            </p>
          </div>
        </div>
      </div>
    );

    const renderCertificatesSection = () => (
      <div className="space-y-4 p-4 border rounded-md bg-white">
        <h2 className="text-xl font-bold text-slate-800">Our Certificates</h2>

        <div className="text-sm text-slate-600 mb-4">
          {groupedContent['certificates_intro'] && groupedContent['certificates_intro']['en']}
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {['certificate1', 'certificate2', 'certificate3', 'certificate4'].map((key, index) => (
            groupedContent[key] && (
              <div key={index} className="border rounded-md p-3 flex flex-col items-center">
                <div className="h-16 w-16 bg-slate-100 rounded-md mb-2 flex items-center justify-center">
                  <Info className="h-8 w-8 text-slate-400" />
                </div>
                <span className="text-xs font-medium text-center">{groupedContent[key]['en']}</span>
              </div>
            )
          ))}
        </div>
      </div>
    );

    switch (section) {
      case 'about':
        return renderAboutSection();
      case 'footer':
        return renderFooterSection();
      case 'contact':
        return renderContactSection();
      case 'certificates':
        return renderCertificatesSection();
      default:
        return (
          <div className="p-4 border rounded-md bg-slate-50 text-center text-slate-400">
            No preview available for this section
          </div>
        );
    }
  };

  if (isMobile) {
    return (
      <MobileFormLayout title="Site Content Editor">
        <div className="space-y-4">
          {/* Section and Language Selectors */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="section">Section</Label>
              <Select value={activeSection} onValueChange={setActiveSection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  {CONTENT_SECTIONS.map((section) => (
                    <SelectItem key={section.id} value={section.id}>
                      {section.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="language">Language</Label>
              <Select value={activeLanguage} onValueChange={setActiveLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {LANGUAGES.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      <Globe className="h-4 w-4 mr-2" />
                      {lang.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button onClick={handleCreateContent} className="flex-1">
              <Edit className="h-4 w-4 mr-2" />
              Add Content
            </Button>
            <Button variant="outline" onClick={() => setShowPreview(!showPreview)}>
              <Eye className="h-4 w-4 mr-2" />
              {showPreview ? 'Hide' : 'Show'} Preview
            </Button>
          </div>

          {/* Preview */}
          {showPreview && (
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <ContentPreview content={previewContent} section={activeSection} />
              </CardContent>
            </Card>
          )}

          {/* Content List */}
          <Card>
            <CardHeader>
              <CardTitle>Content Items</CardTitle>
              <CardDescription>
                {filteredContent.length} items in {CONTENT_SECTIONS.find(s => s.id === activeSection)?.name} ({LANGUAGES.find(l => l.code === activeLanguage)?.name})
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : filteredContent.length > 0 ? (
                <div className="space-y-3">
                  {filteredContent.map((item) => (
                    <div key={item.id} className="border rounded-lg p-3">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium">{item.key}</h4>
                          <p className="text-sm text-muted-foreground truncate">
                            {item.value}
                          </p>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditContent(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteContent(item.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No content items found</p>
                  <Button onClick={handleCreateContent} className="mt-4">
                    Create First Item
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </MobileFormLayout>
    );
  }

  // Desktop Layout
  return (
    <div className="admin-content-container">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Site Content Editor</h1>
        <p className="admin-page-description">
          Manage website content across different sections and languages
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Controls */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Content Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="section">Section</Label>
                <Select value={activeSection} onValueChange={setActiveSection}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select section" />
                  </SelectTrigger>
                  <SelectContent>
                    {CONTENT_SECTIONS.map((section) => (
                      <SelectItem key={section.id} value={section.id}>
                        {section.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="language">Language</Label>
                <Select value={activeLanguage} onValueChange={setActiveLanguage}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    {LANGUAGES.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <Globe className="h-4 w-4 mr-2" />
                        {lang.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleCreateContent} className="flex-1">
                  <Edit className="h-4 w-4 mr-2" />
                  Add Content
                </Button>
                <Button variant="outline" onClick={() => refetch()}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="show-preview"
                  checked={showPreview}
                  onCheckedChange={setShowPreview}
                />
                <Label htmlFor="show-preview">Show Preview</Label>
              </div>
            </CardContent>
          </Card>

          {/* Content List */}
          <Card>
            <CardHeader>
              <CardTitle>Content Items</CardTitle>
              <CardDescription>
                {filteredContent.length} items in {CONTENT_SECTIONS.find(s => s.id === activeSection)?.name} ({LANGUAGES.find(l => l.code === activeLanguage)?.name})
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : filteredContent.length > 0 ? (
                <div className="space-y-3">
                  {filteredContent.map((item) => (
                    <div key={item.id} className="border rounded-lg p-3">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{item.key}</h4>
                          <p className="text-xs text-muted-foreground truncate">
                            {item.value}
                          </p>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditContent(item)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteContent(item.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-sm">No content items found</p>
                  <Button onClick={handleCreateContent} className="mt-4" size="sm">
                    Create First Item
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Preview */}
        <div className="lg:col-span-2">
          {showPreview && (
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>
                  Live preview of {CONTENT_SECTIONS.find(s => s.id === activeSection)?.name} section
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ContentPreview content={previewContent} section={activeSection} />
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedContent?.id === 0 ? 'Create' : 'Edit'} Content
            </DialogTitle>
            <DialogDescription>
              {selectedContent?.id === 0 ? 'Add new content item' : 'Edit existing content item'}
            </DialogDescription>
          </DialogHeader>

          {selectedContent && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="section">Section</Label>
                  <Select
                    value={selectedContent.section}
                    onValueChange={(value) =>
                      setSelectedContent({ ...selectedContent, section: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CONTENT_SECTIONS.map((section) => (
                        <SelectItem key={section.id} value={section.id}>
                          {section.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="language">Language</Label>
                  <Select
                    value={selectedContent.language}
                    onValueChange={(value) =>
                      setSelectedContent({ ...selectedContent, language: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {LANGUAGES.map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          <Globe className="h-4 w-4 mr-2" />
                          {lang.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="key">Content Key</Label>
                <Input
                  id="key"
                  value={selectedContent.key}
                  onChange={(e) =>
                    setSelectedContent({ ...selectedContent, key: e.target.value })
                  }
                  placeholder="e.g., company_description, mission, values"
                />
              </div>

              <div>
                <Label htmlFor="value">Content Value</Label>
                <Textarea
                  id="value"
                  value={selectedContent.value}
                  onChange={(e) =>
                    setSelectedContent({ ...selectedContent, value: e.target.value })
                  }
                  placeholder="Enter the content text..."
                  rows={6}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSaveContent}
              disabled={saveContentMutation.isPending || updateContentMutation.isPending}
            >
              {(saveContentMutation.isPending || updateContentMutation.isPending) ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {selectedContent?.id === 0 ? 'Create' : 'Update'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default SiteContentEditor;
