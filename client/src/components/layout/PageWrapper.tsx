import { ReactNode, useEffect } from 'react';
import { useLocation } from 'wouter';
import { CtaSection } from '../sections/CtaSection';

interface PageWrapperProps {
  children: ReactNode;
}

/**
 * PageWrapper component that conditionally renders the CTA section
 * based on the current page path to avoid duplicate "Request Quote" buttons
 */
export const PageWrapper = ({ children }: PageWrapperProps) => {
  const [location] = useLocation();

  // Determine which pages should NOT have automatic CTA
  // Enhanced detection for product detail pages
  const isProductDetailPage = (
    location.startsWith('/products/') &&
    location !== '/products' &&
    location !== '/products/' &&
    location.length > '/products/'.length
  ) || location === '/product';

  const isAdminRoute = location.startsWith('/admin');
  const isContactPage = location === '/contact';
  const isCareersPage = location === '/careers';

  // Pages that already have their own CTA sections or shouldn't have CTAs
  // ProductDetail pages have their own QuoteRequestModal functionality
  // Services page shows CTA section via PageWrapper
  // Careers page has its own custom CTA section
  const shouldSkipCTA = isProductDetailPage || isAdminRoute || isContactPage || isCareersPage;

  // Scroll to top on page change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  return (
    <>
      {children}

      {/* Only show the CTA section on pages that don't have their own CTA */}
      {!shouldSkipCTA && <CtaSection />}
    </>
  );
};