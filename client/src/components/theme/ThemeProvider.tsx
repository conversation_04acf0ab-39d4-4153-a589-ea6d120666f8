import React, { createContext, useContext, useEffect, useState } from "react";
import { applyAdminTheme, loadAdminCSS, unloadAdminCSS, resetAdminTheme } from "../../lib/admin-theme";

type Theme = "light" | "dark";

interface ThemeContextType {
  theme: Theme;
  adminTheme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  toggleAdminTheme: () => void;
  setAdminTheme: (theme: Theme) => void;
  isAdmin: boolean;
  setIsAdmin: (isAdmin: boolean) => void;
  // Alias for setIsAdmin for better readability in specific contexts
  setIsAdminMode: (isAdmin: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Helper to get initial theme - ALWAYS returns light theme now
const getInitialTheme = (): Theme => {
  // Always return light theme
  return "light";
};

// Helper to get initial admin theme
const getInitialAdminTheme = (): Theme => {
  // Check if we're in the browser
  if (typeof window === "undefined") return "dark";

  // Check localStorage first
  const savedTheme = localStorage.getItem("metanord-admin-theme") as Theme | null;
  if (savedTheme && (savedTheme === "light" || savedTheme === "dark")) {
    return savedTheme;
  }
  
  // Default admin theme is dark
  return "dark";
};

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<Theme>(getInitialTheme);
  const [adminTheme, setAdminThemeState] = useState<Theme>(getInitialAdminTheme);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [mounted, setMounted] = useState(false);

  // Set the theme on the document element
  const applyTheme = (newTheme: Theme) => {
    if (typeof window === "undefined") return;
    
    // Only apply theme if we're not in admin mode
    if (!isAdmin) {
      const root = window.document.documentElement;
      
      // Remove both classes first
      root.classList.remove("light", "dark");
      
      // Add the current theme class
      root.classList.add(newTheme);
      
      // Save to localStorage
      localStorage.setItem("metanord-theme", newTheme);
    }
  };

  // Set theme with validation
  const setTheme = (newTheme: Theme) => {
    if (newTheme !== "light" && newTheme !== "dark") return;
    setThemeState(newTheme);
    
    // Only apply the general theme if we're not in admin mode
    if (!isAdmin) {
      applyTheme(newTheme);
    }
  };

  // Set the admin theme
  const applyAdminThemeInternal = (newTheme: Theme) => {
    if (typeof window === "undefined") return;
    
    // Only apply if we're in admin mode
    if (isAdmin) {
      // Load admin styles if needed
      loadAdminCSS();
      
      // Apply the admin-specific theme using the utility
      applyAdminTheme(newTheme === 'dark');
      
      // Save to localStorage
      localStorage.setItem("metanord-admin-theme", newTheme);
    }
  };

  // Set admin theme with validation
  const setAdminTheme = (newTheme: Theme) => {
    if (newTheme !== "light" && newTheme !== "dark") return;
    setAdminThemeState(newTheme);
    
    // Only apply if we're in admin mode
    if (isAdmin) {
      applyAdminThemeInternal(newTheme);
    }
  };

  // Toggle between light and dark for admin
  const toggleAdminTheme = () => {
    const newTheme = adminTheme === "light" ? "dark" : "light";
    setAdminTheme(newTheme);
  };

  // Toggle between light and dark for general site
  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme);
  };

  // Force light mode theme on component mount and when dependencies change
  useEffect(() => {
    // Always force light theme regardless of user preferences or system settings
    const root = window.document.documentElement;
    
    // Remove dark class and ensure light class is applied
    root.classList.remove("dark");
    root.classList.add("light");
    
    // Set light mode in localStorage for persistence
    localStorage.setItem("metanord-theme", "light");
    
    // Force data-theme attribute to be light
    root.setAttribute('data-theme', 'light');
    
    // Set mounted state
    if (!mounted) {
      setMounted(true);
    }
    
    // Handle admin-specific theming if needed
    if (isAdmin) {
      applyAdminThemeInternal(adminTheme);
    } else {
      unloadAdminCSS();
      resetAdminTheme();
    }
  }, [adminTheme, isAdmin, mounted]);

  // Make sure we're providing the initialized theme value and functions
  const contextValue: ThemeContextType = {
    theme,
    adminTheme,
    toggleTheme,
    setTheme,
    toggleAdminTheme,
    setAdminTheme,
    isAdmin,
    setIsAdmin,
    // Alias for setIsAdmin with the same implementation
    setIsAdminMode: setIsAdmin
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}