import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from './ui/button';
import { <PERSON><PERSON><PERSON>riangle, Refresh<PERSON><PERSON>, Bug, ExternalLink } from 'lucide-react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
}

interface ErrorDetails {
  message: string;
  stack?: string;
  componentStack?: string;
  environment: {
    isDev: boolean;
    apiUrl: string;
    hostname: string;
    userAgent: string;
  };
  timestamp: string;
}

export class CareersErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      showDetails: false
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Log error details for debugging
    const errorDetails: ErrorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      environment: {
        isDev: import.meta.env.DEV,
        apiUrl: import.meta.env.VITE_API_URL || (
          import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
        ),
        hostname: window.location.hostname,
        userAgent: navigator.userAgent
      },
      timestamp: new Date().toISOString()
    };

    console.error('🚨 Careers Page Error:', errorDetails);

    // In production, you might want to send this to an error reporting service
    if (!import.meta.env.DEV) {
      this.reportErrorToService(errorDetails);
    }
  }

  private reportErrorToService(errorDetails: ErrorDetails) {
    // Example: Send to error reporting service
    // fetch('/api/errors', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorDetails)
    // }).catch(console.error);
    
    console.log('Error reported:', errorDetails);
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  private copyErrorDetails = () => {
    const { error, errorInfo } = this.state;
    const errorText = `
MetaNord Careers Page Error Report
================================
Time: ${new Date().toISOString()}
Environment: ${import.meta.env.DEV ? 'Development' : 'Production'}
API URL: ${import.meta.env.VITE_API_URL || 'default'}
Hostname: ${window.location.hostname}

Error: ${error?.message}
Stack: ${error?.stack}
Component Stack: ${errorInfo?.componentStack}
`;
    
    navigator.clipboard.writeText(errorText).then(() => {
      alert('Error details copied to clipboard');
    });
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, showDetails } = this.state;
      const isDev = import.meta.env.DEV;

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg p-8">
            {/* Error Icon */}
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
            </div>

            {/* Error Title */}
            <h1 className="text-2xl font-bold text-gray-900 text-center mb-4">
              Something went wrong
            </h1>

            {/* Error Description */}
            <p className="text-gray-600 text-center mb-8">
              We're sorry, but there was an error loading the careers page. 
              This might be a temporary issue with our servers or your connection.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
              <Button 
                onClick={this.handleRetry}
                className="bg-[#2D7EB6] hover:bg-[#2D7EB6]/90 text-white"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
              
              <Button 
                onClick={this.handleReload}
                variant="outline"
                className="border-[#2D7EB6] text-[#2D7EB6] hover:bg-[#2D7EB6] hover:text-white"
              >
                Reload Page
              </Button>

              <Button 
                onClick={() => window.location.href = '/contact'}
                variant="outline"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Contact Support
              </Button>
            </div>

            {/* Error Details Toggle */}
            {(isDev || error) && (
              <div className="border-t pt-6">
                <Button
                  onClick={this.toggleDetails}
                  variant="ghost"
                  className="w-full text-sm text-gray-500 hover:text-gray-700"
                >
                  <Bug className="w-4 h-4 mr-2" />
                  {showDetails ? 'Hide' : 'Show'} Technical Details
                </Button>

                {showDetails && (
                  <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-semibold text-sm text-gray-700">Error Details</h3>
                      <Button
                        onClick={this.copyErrorDetails}
                        size="sm"
                        variant="outline"
                        className="text-xs"
                      >
                        Copy Details
                      </Button>
                    </div>
                    
                    <div className="text-xs text-gray-600 space-y-2">
                      <div>
                        <strong>Error:</strong> {error?.message}
                      </div>
                      
                      <div>
                        <strong>Environment:</strong> {isDev ? 'Development' : 'Production'}
                      </div>
                      
                      <div>
                        <strong>API URL:</strong> {import.meta.env.VITE_API_URL || 'default (https://api.metanord.eu)'}
                      </div>
                      
                      <div>
                        <strong>Hostname:</strong> {window.location.hostname}
                      </div>

                      {isDev && error?.stack && (
                        <details className="mt-2">
                          <summary className="cursor-pointer font-semibold">Stack Trace</summary>
                          <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto">
                            {error.stack}
                          </pre>
                        </details>
                      )}

                      {isDev && errorInfo?.componentStack && (
                        <details className="mt-2">
                          <summary className="cursor-pointer font-semibold">Component Stack</summary>
                          <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto">
                            {errorInfo.componentStack}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Helpful Links */}
            <div className="mt-8 text-center text-sm text-gray-500">
              <p>
                If this problem persists, please{' '}
                <a 
                  href="/contact" 
                  className="text-[#2D7EB6] hover:underline"
                >
                  contact our support team
                </a>
                {' '}or try visiting our{' '}
                <a 
                  href="/" 
                  className="text-[#2D7EB6] hover:underline"
                >
                  homepage
                </a>
                .
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
