import React from 'react';

interface AluminumProfilesIconProps {
  className?: string;
  size?: number;
  [key: string]: any; // Allow other props to be passed through
}

const AluminumProfilesIcon: React.FC<AluminumProfilesIconProps> = ({ 
  className = "", 
  size = 24,
  ...props 
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 256 256"
    fill="currentColor"
    className={className}
    {...props}
  >
    <path d="M208,96a8,8,0,0,1-8,8H136V216a8,8,0,0,1-16,0V104H56a8,8,0,0,1,0-16H200A8,8,0,0,1,208,96ZM56,64H200a8,8,0,0,0,0-16H56a8,8,0,0,0,0,16Z" />
  </svg>
);

export default AluminumProfilesIcon;
