import React from 'react';

interface CustomProductIconProps {
  className?: string;
  size?: number;
}

const CustomProductIcon: React.FC<CustomProductIconProps> = ({ 
  className = "", 
  size = 24,
  ...props 
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    {/* Example: Industrial pipe/tube icon */}
    <rect x="2" y="6" width="20" height="12" rx="2" />
    <circle cx="7" cy="12" r="2" />
    <circle cx="17" cy="12" r="2" />
    <path d="M7 10V8a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2" />
    <path d="M7 14v2a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-2" />
  </svg>
);

export default CustomProductIcon;
