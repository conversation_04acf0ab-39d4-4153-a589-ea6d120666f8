import { useTranslation } from "react-i18next";
import { ServiceCard } from "../ui/service-card";
import { MotionSplitTitle } from "../ui/MotionSplitTitle";
import {
  TruckIcon,
  ClipboardCheck,
  FileText,
  ShieldCheck,
  Sliders,
  Bolt,
  Zap,
  Layers3
} from "lucide-react";

export function ServicesSection() {
  const { t } = useTranslation();

  const services = [
    {
      icon: TruckIcon,
      title: t("services.logistics.title"),
      description: t("services.logistics.description"),
      features: [
        { text: t("services.logistics.features.0"), icon: TruckIcon },
        { text: t("services.logistics.features.1"), icon: ClipboardCheck },
        { text: t("services.logistics.features.2"), icon: Sliders },
      ],
    },
    {
      icon: ClipboardCheck,
      title: t("services.consulting.title"),
      description: t("services.consulting.description"),
      features: [
        { text: t("services.consulting.features.0"), icon: FileText },
        { text: t("services.consulting.features.1"), icon: Shield<PERSON>heck },
        { text: t("services.consulting.features.2"), icon: Sliders },
      ],
    },
    {
      icon: Bolt,
      title: t("services.custom.title"),
      description: t("services.custom.description"),
      features: [
        { text: t("services.custom.features.0"), icon: Sliders },
        { text: t("services.custom.features.1"), icon: Zap },
        { text: t("services.custom.features.2"), icon: Layers3 },
      ],
    },
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <MotionSplitTitle
          part1={t("home.services.title.part1", "Our")}
          part2={t("home.services.title.part2", "Services")}
        />
        <p className="text-center text-gray-500 text-sm max-w-2xl mx-auto mb-10">
          {t("services.subtitle")}
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <ServiceCard key={index} {...service} showButton={false} />
          ))}
        </div>
      </div>
    </section>
  );
}
