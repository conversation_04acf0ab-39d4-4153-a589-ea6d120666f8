import { ProductCard } from "../ui/product-card";
import { ProductPreviewModal } from "../ui/product-preview-modal";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useAllProducts, useProductCategories } from "../../hooks/use-product-api";
import { useMemo, memo, useState } from "react";
import { Link } from "wouter";
import { ArrowRight } from "lucide-react";
import { MotionSplitTitle } from "../ui/MotionSplitTitle";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext
} from "../ui/carousel";
import { useLanguage } from "../../hooks/use-language";
import Autoplay from "embla-carousel-autoplay";

// Memoized ProductCard wrapper to prevent unnecessary re-renders
const MemoizedProductCard = memo(function MemoizedProductCard(props: any) {
  return <ProductCard {...props} />;
});

export function ProductsSection() {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const { data: products = [], isLoading, error } = useAllProducts();
  const categories = useProductCategories();

  // Modal state
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);

  // Handle product preview
  const handleProductPreview = (product: any) => {
    setSelectedProduct(product);
    setIsPreviewOpen(true);
  };

  // Group products by category and select representative product for each
  const categoryData = useMemo(() => {
    if (!products.length || !categories.length) return [];

    return categories.map(category => {
      // Find products for this category
      const categoryProducts = products.filter(product => {
        if (!product?.category) return false;
        const normalized = (s: string) => s.replace(/[-_ ]/g, "").toLowerCase();
        return normalized(product.category) === normalized(category.id);
      });

      // Select the first product as representative (you could implement more sophisticated logic here)
      const representativeProduct = categoryProducts[0];

      return {
        category,
        representativeProduct,
        productCount: categoryProducts.length,
        categorySlug: category.id
      };
    }).filter(item => item.representativeProduct); // Only include categories that have products
  }, [products, categories, language]);

  // Loading state
  if (isLoading) {
    return (
      <section id="products" className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center">
            <MotionSplitTitle
              part1={t("products.our", "Our")}
              part2={t("products.products", "Products")}
            />
            <div className="flex items-center justify-center h-64">
              <span className="text-gray-600">{t("common.loading", "Loading...")}</span>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Error state
  if (error) {
    return (
      <section id="products" className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center">
            <MotionSplitTitle
              part1={t("products.our", "Our")}
              part2={t("products.products", "Products")}
            />
            <div className="flex items-center justify-center h-64 text-red-500">
              {t("products.error_loading", "Error loading products")}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="products" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <MotionSplitTitle
            part1={t("products.our", "Our")}
            part2={t("products.products", "Products")}
          />
          <motion.p
            className="text-sm text-gray-500 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {t(
              "products.description",
              "Explore our diverse range of high-quality infrastructure products designed for construction and industrial applications."
            )}
          </motion.p>
        </div>

        {/* Products Carousel */}
        {categoryData.length > 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Carousel
              opts={{
                align: "start",
                loop: true,
                skipSnaps: false,
                dragFree: true,
              }}
              plugins={[
                Autoplay({
                  delay: 4000,
                  stopOnInteraction: true,
                  stopOnMouseEnter: true,
                  stopOnFocusIn: true,
                })
              ]}
              className="w-full"
              aria-label={t("products.carousel.label", "Product categories carousel")}
            >
              <CarouselContent className="-ml-2 md:-ml-4">
                {categoryData.map((item, index) => (
                  <CarouselItem
                    key={item.category.id}
                    className="pl-2 md:pl-4 basis-full xs:basis-1/1 sm:basis-1/2 lg:basis-1/3 xl:basis-1/4"
                  >
                    <motion.div
                      className="h-full"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      {/* Category Card Container */}
                      <div className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-6 h-full flex flex-col">
                        {/* Category Name */}
                        <div className="text-center mb-4">
                          <h3 className="text-xl font-bold text-[#2D7EB6] mb-2">
                            {item.category.label}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {t("products.productsCount", "{{count}} products", { count: item.productCount })}
                          </p>
                        </div>

                        {/* Representative Product Card */}
                        <div className="flex-grow mb-4">
                          <MemoizedProductCard
                            {...item.representativeProduct}
                            className="h-full border-0 shadow-none bg-gray-50/50 hover:bg-gray-100/50 transition-colors duration-200"
                            onPreviewClick={handleProductPreview}
                          />
                        </div>

                        {/* View All Link */}
                        <div className="text-center">
                          <Link to={`/products?material=${item.categorySlug}`}>
                            <button className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] text-white font-medium rounded-full hover:shadow-lg transition-all duration-300 hover:scale-105">
                              {t("products.viewAll", "View All")}
                              <ArrowRight className="h-4 w-4" />
                            </button>
                          </Link>
                        </div>
                      </div>
                    </motion.div>
                  </CarouselItem>
                ))}
              </CarouselContent>

              {/* Navigation Arrows */}
              <CarouselPrevious className="hidden md:flex -left-12 bg-white border-2 border-[#2D7EB6]/20 text-[#2D7EB6] hover:bg-[#2D7EB6] hover:text-white" />
              <CarouselNext className="hidden md:flex -right-12 bg-white border-2 border-[#2D7EB6]/20 text-[#2D7EB6] hover:bg-[#2D7EB6] hover:text-white" />
            </Carousel>
          </motion.div>
        ) : (
          <div className="text-center py-16">
            <p className="text-gray-500">
              {t("products.noCategories", "No product categories available")}
            </p>
          </div>
        )}

      </div>

      {/* Product Preview Modal */}
      {selectedProduct && (
        <ProductPreviewModal
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          product={selectedProduct}
        />
      )}
    </section>
  );
}
