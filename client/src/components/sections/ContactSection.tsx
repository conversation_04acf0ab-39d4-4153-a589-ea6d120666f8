import { ContactForm } from "../forms/ContactForm";
import { Briefcase, Clipboard, MapPin, Phone, Mail, Globe } from "lucide-react";
import { useTranslation } from "react-i18next";

export function ContactSection() {
  const { t } = useTranslation();

  const contactInfo = [
    {
      icon: <Briefcase className="w-5 h-5 text-white" />,
      label: t("contact.headers.company", "Company"),
      value: t("contact.info.company", "MetaNord OÜ"),
    },
    {
      icon: <Clipboard className="w-5 h-5 text-white" />,
      label: t("contact.headers.registry", "Registry Code"),
      value: t("contact.info.registry", "17235227"),
    },
    {
      icon: <MapPin className="w-5 h-5 text-white" />,
      label: t("contact.headers.address", "Address"),
      value: t("contact.info.address", "Tornimäe 5, Tallinn, 10145, Estonia"),
      link: "https://goo.gl/maps/YJ4SytAoGHbQXauy8"
    },
    {
      icon: <Phone className="w-5 h-5 text-white" />,
      label: t("contact.headers.phone", "Phone"),
      value: t("contact.info.phone", "+372 55589800"),
      link: "tel:+37255589800"
    },
    {
      icon: <Mail className="w-5 h-5 text-white" />,
      label: t("contact.headers.email", "Email"),
      value: t("contact.info.email", "<EMAIL>"),
      link: "mailto:<EMAIL>"
    },
    {
      icon: <Globe className="w-5 h-5 text-white" />,
      label: t("contact.headers.website", "Website"),
      value: t("contact.info.website", "www.metanord.eu"),
      link: "https://www.metanord.eu"
    },
  ];

  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        {/* SECTION HEADER */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-black mb-2">
            {t("contact.sectionTitle_part1", "Contact")}{" "}
            <span className="text-[#2D7EB6]">{t("contact.sectionTitle_part2", "Us")}</span>
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-start">
          {/* LEFT: Write Us + Form */}
          <div>
            <h3 className="text-2xl font-bold mb-1 text-black">{t("contact.writeUs", "Write Us")}</h3>
            <p className="text-gray-500 mb-6">{t("contact.form.subtitle", "Fill out the form below and our team will get back to you as soon as possible.")}</p>
            <ContactForm labelColor="#40BFB9" />
          </div>

          {/* RIGHT: Contact Info + Map */}
          <div>
            <h3 className="text-2xl font-bold mb-4 text-black">{t("contact.info.title", "Contact Information")}</h3>
            <ul className="space-y-4 mb-6">
              {contactInfo.map((item, i) => (
                <li key={i} className="flex items-start gap-3">
                  <div className="bg-[#2D7EB6] p-2 rounded-full">{item.icon}</div>
                  <div>
                    <div className="font-semibold text-base text-[#40BFB9] mb-0.5">{item.label}</div>
                    {item.link ? (
                      <a
                        href={item.link}
                        className="text-sm font-medium text-black underline hover:text-[#2D7EB6] transition"
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ wordBreak: 'break-all' }}
                      >
                        {item.value}
                      </a>
                    ) : (
                      <div className="text-sm font-medium text-black">{item.value}</div>
                    )}
                  </div>
                </li>
              ))}
            </ul>

            <div className="relative rounded-xl overflow-hidden h-56 w-full mt-4 shadow border border-gray-200">
              <iframe
                src="https://maps.google.com/maps?q=Tornimäe%205,%20Tallinn,%2010145,%20Estonia&t=&z=16&ie=UTF8&iwloc=&output=embed"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="MetaNord Office Location - Tornimäe 5, Tallinn, 10145, Estonia"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
