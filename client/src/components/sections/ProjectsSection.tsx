import { ProjectCard } from "../ui/project-card";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { MotionSplitTitle } from "../ui/MotionSplitTitle";

const officeImage = "/images/office-building.jpg";
const waterSupplyNetwork = "/images/water-supply-network.jpg";

export function ProjectsSection() {
  const { t } = useTranslation();

  const projects = [
    {
      image: officeImage,
      title: t("projects.office"),
      description: t("projects.officeDesc"),
      category: t("projects.commercial"),
      categoryClass: "bg-accent"
    },
    {
      image: waterSupplyNetwork,
      title: t("projects.pipeline"),
      description: t("projects.pipelineDesc"),
      category: t("projects.infrastructure"),
      categoryClass: "bg-primary"
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-b from-white via-gray-50 to-white relative overflow-hidden">
      <div className="absolute top-0 left-0 w-full h-24 bg-gradient-to-b from-white to-transparent opacity-80 z-10"></div>
      <div className="absolute w-96 h-96 bg-primary/5 rounded-full blur-3xl -top-48 -right-48"></div>
      <div className="absolute w-96 h-96 bg-accent/5 rounded-full blur-3xl -bottom-48 -left-48"></div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <MotionSplitTitle
            part1={t("projects.sectionTitle_part1", "Our")}
            part2={t("projects.sectionTitle_part2", "Projects")}
          />
          <motion.p
            className="text-sm text-gray-500 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {t("projects.subtitle")}
          </motion.p>
        </div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
        >
          {projects.map((project, index) => (
            <ProjectCard
              key={index}
              image={project.image}
              title={project.title}
              description={project.description}
              category={project.category}
              categoryClass={project.categoryClass}
            />
          ))}
        </motion.div>
      </div>
    </section>
  );
}
