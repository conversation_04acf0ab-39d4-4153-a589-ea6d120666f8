import { FeatureCard } from "../ui/feature-card";
import { Award, EuroIcon, Truck, Handshake } from "lucide-react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { MotionSplitTitle } from "../ui/MotionSplitTitle"; // Импорт MotionSplitTitle

export function Features() {
  const { t } = useTranslation();
  
  const features = [
    {
      icon: Award,
      title: t("features.quality.title"),
      description: t("features.quality.description"),
      colorClass: "border-primary" as const,
      iconColorClass: "text-primary" as const
    },
    {
      icon: EuroIcon,
      title: t("features.competitive.title"),
      description: t("features.competitive.description"),
      colorClass: "border-accent" as const,
      iconColorClass: "text-accent" as const
    },
    {
      icon: Truck,
      title: t("features.reliability.title"),
      description: t("features.reliability.description"),
      colorClass: "border-primary" as const,
      iconColorClass: "text-primary" as const
    },
    {
      icon: Handshake,
      title: t("features.expertise.title"),
      description: t("features.expertise.description"),
      colorClass: "border-accent" as const,
      iconColorClass: "text-accent" as const
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <MotionSplitTitle
            part1={t("features.whyChooseUs_part1", "Why Choose")}
            part2={t("features.whyChooseUs_part2", "Us")}
          />
          <motion.p 
            className="text-sm text-gray-500 max-w-720 leading-relaxed"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {t("features.subtitle")}
          </motion.p>
        </div>
        
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 items-stretch"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => (
            <motion.div key={index} variants={itemVariants} className="h-full flex">
              <FeatureCard
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                colorClass={feature.colorClass}
                iconColorClass={feature.iconColorClass}
              />
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
