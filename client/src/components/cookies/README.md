# MetaNord Cookie Consent System

A comprehensive GDPR-compliant cookie consent system for the MetaNord frontend application.

## Overview

This implementation provides a complete cookie consent management system that integrates seamlessly with the existing MetaNord design system and multi-language architecture.

## Features

### ✅ GDPR Compliance
- Non-dismissible banner until user makes a choice
- Granular consent options (Essential, Analytics, Marketing)
- Easy consent withdrawal mechanism
- 12-month consent expiration with re-prompting
- Comprehensive cookie policy page

### ✅ User Experience
- **Mobile**: Fixed bottom banner with slide-up animation
- **Desktop**: Centered modal overlay with backdrop blur
- **Design**: Consistent with MetaNord brand colors (#2D7EB6, #40BFB9)
- **Accessibility**: Keyboard navigation and screen reader support

### ✅ Multi-Language Support
- Translated into all MetaNord supported languages:
  - English (EN)
  - Russian (RU) 
  - Estonian (ET)
  - Latvian (LV)
  - Lithuanian (LT)
  - Polish (PL)
  - Chinese Simplified (ZH-CN)

### ✅ Technical Integration
- React Context for state management
- localStorage for consent persistence
- Conditional analytics loading based on consent
- Integration with existing React Query setup
- TypeScript support throughout

## Components

### Core Components
- `CookieConsentBanner.tsx` - Main consent banner
- `CookieConsentModal.tsx` - Detailed settings modal
- `CookieConsentContext.tsx` - React Context provider
- `CookiePolicy.tsx` - Dedicated policy page

### Utilities
- `cookie-storage.ts` - localStorage management
- `analytics-manager.ts` - Conditional analytics loading
- `use-cookie-consent.ts` - React hook for easy access

## Cookie Categories

### Essential Cookies (Always Enabled)
- Language preferences
- Session data
- Consent status
- Authentication tokens

### Analytics Cookies (User Choice)
- Google Analytics/GTM
- Performance tracking
- Usage statistics

### Marketing Cookies (User Choice)
- Advertising trackers
- Social media pixels
- Retargeting data

## Usage

### Basic Usage
```tsx
import { useCookieConsent } from '../../hooks/use-cookie-consent';

function MyComponent() {
  const { consent, openSettings } = useCookieConsent();
  
  return (
    <div>
      <button onClick={openSettings}>
        Manage Cookie Preferences
      </button>
      {consent.analytics && <AnalyticsComponent />}
    </div>
  );
}
```

### Checking Consent Status
```tsx
import { canUseAnalytics, canUseMarketing } from '../../utils/cookie-storage';

// Check if analytics is allowed
if (canUseAnalytics()) {
  // Initialize analytics
}

// Check if marketing is allowed
if (canUseMarketing()) {
  // Initialize marketing scripts
}
```

## Integration Points

### Footer Integration
- "Cookie Settings" link added to footer legal section
- Opens settings modal when clicked
- Available in all supported languages

### Analytics Integration
- Google Tag Manager conditional loading
- Google Analytics conditional loading
- Event tracking respects consent status
- Automatic script removal when consent withdrawn

### Route Integration
- `/cookie-policy` route added to main router
- SEO optimized with MetaTags
- Consistent page layout with hero section

## Storage Structure

```typescript
// localStorage key: 'metanord-cookie-consent'
{
  essential: true,        // Always true
  analytics: boolean,     // User choice
  marketing: boolean,     // User choice
  timestamp: number,      // Consent timestamp
  version: string         // Policy version
}
```

## Compliance Features

### Data Protection
- No personal data stored in cookies without consent
- Clear data retention policies
- Easy data deletion mechanism

### User Rights
- Right to withdraw consent
- Right to granular control
- Right to information (policy page)
- Right to be forgotten (clear consent)

### Legal Requirements
- Clear consent language
- Specific purpose descriptions
- Contact information provided
- Regular consent renewal (12 months)

## Browser Support

- Modern browsers with localStorage support
- Graceful degradation for older browsers
- Mobile-responsive design
- Touch-friendly interactions

## Development

### Testing Consent Flow
1. Clear localStorage: `localStorage.removeItem('metanord-cookie-consent')`
2. Refresh page to see banner
3. Test different consent combinations
4. Verify analytics loading behavior

### Adding New Cookie Categories
1. Update `CookieConsent` interface in `cookie-storage.ts`
2. Add translations to all language files
3. Update UI components with new category
4. Implement conditional loading logic

## Maintenance

### Updating Translations
- Add new keys to all language files in `client/src/locales/`
- Follow existing naming convention: `cookies.category.key`
- Test in all supported languages

### Policy Updates
- Increment version in `cookie-storage.ts`
- Update policy content in translation files
- Users will be re-prompted on next visit

This implementation ensures full GDPR compliance while maintaining the high-quality user experience expected from the MetaNord platform.
