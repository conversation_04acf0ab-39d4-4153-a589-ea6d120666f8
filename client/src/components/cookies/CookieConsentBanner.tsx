/**
 * <PERSON><PERSON>sent Banner Component
 * GDPR-compliant cookie consent banner with MetaNord design system
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'wouter';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, Set<PERSON><PERSON> } from 'lucide-react';
import { useCookieConsent } from '../../hooks/use-cookie-consent';

export function CookieConsentBanner() {
  const { t } = useTranslation();
  const {
    showBanner,
    acceptAll,
    rejectNonEssential,
    openSettings,
    isLoading,
  } = useCookieConsent();

  // Don't render anything while loading or if banner shouldn't be shown
  if (isLoading || !showBanner) {
    return null;
  }

  return (
    <AnimatePresence>
      {showBanner && (
        <motion.div
          initial={{ y: '100%', opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: '100%', opacity: 0 }}
          transition={{ 
            type: 'spring', 
            stiffness: 300, 
            damping: 30,
            duration: 0.4 
          }}
          className="fixed bottom-0 left-0 right-0 z-[9999] bg-white shadow-2xl border-t border-gray-200"
        >
          {/* Desktop Layout */}
          <div className="hidden md:block">
            <div className="container mx-auto px-6 py-6">
              <div className="flex items-center justify-between gap-6">
                {/* Content */}
                <div className="flex items-start gap-4 flex-1">
                  <div className="flex-shrink-0 mt-1">
                    <Cookie className="w-6 h-6 text-[#2D7EB6]" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {t('cookies.banner.title', 'Cookie Preferences')}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                      {t('cookies.banner.description', 'We use cookies to enhance your browsing experience, provide personalized content, and analyze our traffic. You can choose which categories of cookies to accept.')}
                    </p>
                    <Link 
                      to="/cookie-policy"
                      className="text-sm text-[#2D7EB6] hover:text-[#40BFB9] transition-colors duration-200 underline"
                    >
                      {t('cookies.banner.policyLink', 'Learn more in our Cookie Policy')}
                    </Link>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-3 flex-shrink-0">
                  <button
                    onClick={openSettings}
                    className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                  >
                    <Settings className="w-4 h-4" />
                    {t('cookies.banner.customizeSettings', 'Customize Settings')}
                  </button>
                  
                  <button
                    onClick={rejectNonEssential}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                  >
                    {t('cookies.banner.rejectNonEssential', 'Reject Non-Essential')}
                  </button>
                  
                  <button
                    onClick={acceptAll}
                    className="px-6 py-2 text-sm font-semibold text-white rounded-lg transition-all duration-300 hover:shadow-lg hover:scale-105"
                    style={{
                      background: 'linear-gradient(90deg, #2D7EB6 0%, #40BFB9 100%)',
                    }}
                  >
                    {t('cookies.banner.acceptAll', 'Accept All')}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="block md:hidden">
            <div className="px-4 py-5">
              <div className="flex items-start gap-3 mb-4">
                <Cookie className="w-5 h-5 text-[#2D7EB6] flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-base font-semibold text-gray-900 mb-2">
                    {t('cookies.banner.title', 'Cookie Preferences')}
                  </h3>
                  <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                    {t('cookies.banner.description', 'We use cookies to enhance your browsing experience, provide personalized content, and analyze our traffic.')}
                  </p>
                  <Link 
                    to="/cookie-policy"
                    className="text-sm text-[#2D7EB6] hover:text-[#40BFB9] transition-colors duration-200 underline"
                  >
                    {t('cookies.banner.policyLink', 'Learn more in our Cookie Policy')}
                  </Link>
                </div>
              </div>

              {/* Mobile Actions */}
              <div className="space-y-3">
                <button
                  onClick={acceptAll}
                  className="w-full px-4 py-3 text-sm font-semibold text-white rounded-lg transition-all duration-300"
                  style={{
                    background: 'linear-gradient(90deg, #2D7EB6 0%, #40BFB9 100%)',
                  }}
                >
                  {t('cookies.banner.acceptAll', 'Accept All')}
                </button>
                
                <div className="flex gap-2">
                  <button
                    onClick={rejectNonEssential}
                    className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                  >
                    {t('cookies.banner.rejectNonEssential', 'Reject Non-Essential')}
                  </button>
                  
                  <button
                    onClick={openSettings}
                    className="flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                  >
                    <Settings className="w-4 h-4" />
                    {t('cookies.banner.customizeSettings', 'Settings')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
