/**
 * <PERSON><PERSON>sent Modal Component
 * Detailed cookie settings modal with granular controls
 */

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Check, Shield, BarChart3, Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useCookieConsent } from '../../hooks/use-cookie-consent';
import { CookieConsent } from '../../utils/cookie-storage';

export function CookieConsentModal() {
  const { t } = useTranslation();
  const {
    showModal,
    consent,
    closeSettings,
    updateConsent,
    acceptAll,
    rejectNonEssential,
  } = useCookieConsent();

  // Local state for form inputs
  const [localConsent, setLocalConsent] = useState<Partial<CookieConsent>>({
    essential: true,
    analytics: consent.analytics,
    marketing: consent.marketing,
  });

  // Update local state when consent changes
  useEffect(() => {
    setLocalConsent({
      essential: true,
      analytics: consent.analytics,
      marketing: consent.marketing,
    });
  }, [consent]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showModal) {
        closeSettings();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [showModal, closeSettings]);

  // Handle save preferences
  const handleSavePreferences = () => {
    updateConsent(localConsent);
  };

  // Handle toggle
  const handleToggle = (category: keyof CookieConsent) => {
    if (category === 'essential') return; // Cannot toggle essential cookies
    
    setLocalConsent(prev => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  // Handle accept all
  const handleAcceptAll = () => {
    setLocalConsent({
      essential: true,
      analytics: true,
      marketing: true,
    });
    acceptAll();
  };

  // Handle reject all non-essential
  const handleRejectAll = () => {
    setLocalConsent({
      essential: true,
      analytics: false,
      marketing: false,
    });
    rejectNonEssential();
  };

  if (!showModal) {
    return null;
  }

  // Animation variants
  const backdrop = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  };

  const modal = {
    hidden: { opacity: 0, scale: 0.95, y: 20 },
    visible: { opacity: 1, scale: 1, y: 0 },
    exit: { opacity: 0, scale: 0.95, y: 20 },
  };

  // Render modal using React Portal
  const modalContent = (
    <AnimatePresence>
      {showModal && (
        <motion.div
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm p-4"
          variants={backdrop}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={closeSettings}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            variants={modal}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {t('cookies.modal.title', 'Cookie Settings')}
              </h2>
              <button
                onClick={closeSettings}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              <p className="text-gray-600 mb-6">
                {t('cookies.modal.description', 'Manage your cookie preferences. You can enable or disable different types of cookies below.')}
              </p>

              {/* Cookie Categories */}
              <div className="space-y-6">
                {/* Essential Cookies */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Shield className="w-5 h-5 text-green-600" />
                      <h3 className="font-semibold text-gray-900">
                        {t('cookies.categories.essential.title', 'Essential Cookies')}
                      </h3>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-green-600 font-medium">
                      <Check className="w-4 h-4" />
                      {t('cookies.categories.essential.always', 'Always Active')}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    {t('cookies.categories.essential.description', 'These cookies are necessary for the website to function and cannot be switched off. They are usually only set in response to actions made by you which amount to a request for services.')}
                  </p>
                </div>

                {/* Analytics Cookies */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <BarChart3 className="w-5 h-5 text-[#2D7EB6]" />
                      <h3 className="font-semibold text-gray-900">
                        {t('cookies.categories.analytics.title', 'Analytics Cookies')}
                      </h3>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={localConsent.analytics || false}
                        onChange={() => handleToggle('analytics')}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D7EB6]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-[#2D7EB6] peer-checked:to-[#40BFB9]"></div>
                    </label>
                  </div>
                  <p className="text-sm text-gray-600">
                    {t('cookies.categories.analytics.description', 'These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us know which pages are most popular.')}
                  </p>
                </div>

                {/* Marketing Cookies */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Target className="w-5 h-5 text-[#40BFB9]" />
                      <h3 className="font-semibold text-gray-900">
                        {t('cookies.categories.marketing.title', 'Marketing Cookies')}
                      </h3>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={localConsent.marketing || false}
                        onChange={() => handleToggle('marketing')}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D7EB6]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-[#2D7EB6] peer-checked:to-[#40BFB9]"></div>
                    </label>
                  </div>
                  <p className="text-sm text-gray-600">
                    {t('cookies.categories.marketing.description', 'These cookies may be set through our site by our advertising partners. They may be used to build a profile of your interests and show you relevant ads.')}
                  </p>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex flex-col sm:flex-row gap-3 p-6 border-t border-gray-200">
              <button
                onClick={handleRejectAll}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors duration-200"
              >
                {t('cookies.modal.rejectAll', 'Reject Non-Essential')}
              </button>
              
              <button
                onClick={handleSavePreferences}
                className="px-6 py-2 text-sm font-semibold text-white rounded-lg transition-all duration-300 hover:shadow-lg"
                style={{
                  background: 'linear-gradient(90deg, #2D7EB6 0%, #40BFB9 100%)',
                }}
              >
                {t('cookies.modal.savePreferences', 'Save Preferences')}
              </button>
              
              <button
                onClick={handleAcceptAll}
                className="px-6 py-2 text-sm font-semibold text-white rounded-lg transition-all duration-300 hover:shadow-lg"
                style={{
                  background: 'linear-gradient(90deg, #2D7EB6 0%, #40BFB9 100%)',
                }}
              >
                {t('cookies.modal.acceptAll', 'Accept All')}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  // Use React Portal to render modal at document root level
  if (typeof document === 'undefined') {
    return null;
  }

  return createPortal(modalContent, document.body);
}
