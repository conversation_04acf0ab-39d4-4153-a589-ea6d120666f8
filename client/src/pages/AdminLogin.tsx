import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON> } from "react-helmet";
import { useLocation } from "wouter";
import { useAuth } from "../hooks/use-auth";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

// UI Components
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../components/ui/form";
import { Alert, AlertDescription } from "../components/ui/alert";
import { Loader2, Shield } from "lucide-react";

const loginSchema = z.object({
  username: z.string().min(3, {
    message: "Username must be at least 3 characters",
  }),
  password: z.string().min(6, {
    message: "Password must be at least 6 characters",
  }),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function AdminLogin() {
  const [error, setError] = useState<string | null>(null);
  const { loginAdmin, user } = useAuth();
  const [, navigate] = useLocation();
  
  // Initialize form first, before any conditional returns
  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });
  
  const isSubmitting = form.formState.isSubmitting;
  
  // Use useEffect for checking authentication and navigation
  // This avoids using hooks conditionally, which violates React's rules of hooks
  useEffect(() => {
    if (user) {
      navigate("/admin");
    }
  }, [user, navigate]);

  const onSubmit = async (data: LoginFormData) => {
    setError(null);
    try {
      const result = await loginAdmin(data.username, data.password);
      if (result.success) {
        navigate("/admin");
      } else {
        setError(result.message || "Invalid username or password");
      }
    } catch (err) {
      setError("An error occurred. Please try again.");
    }
  };

  return (
    <>
      <Helmet>
        <title>Admin Login - MetaNord</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="flex min-h-screen items-center justify-center bg-slate-100 py-12">
        <div className="w-full max-w-md px-4">
          <Card className="shadow-xl">
            <CardHeader className="space-y-1">
              <div className="flex justify-center mb-4">
                <div className="bg-primary text-white p-3 rounded-full">
                  <Shield className="h-6 w-6" />
                </div>
              </div>
              <CardTitle className="text-2xl text-center">Admin Login</CardTitle>
              <CardDescription className="text-center">
                Enter your credentials to access the admin panel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  {error && (
                    <Alert variant="destructive" className="mb-4">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}
                  
                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your username" 
                            {...field} 
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input 
                            type="password" 
                            placeholder="Enter your password" 
                            {...field} 
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Logging in...
                      </>
                    ) : (
                      "Login to Admin Panel"
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <p className="text-xs text-center w-full text-muted-foreground">
                MetaNord OÜ Admin Panel • Protected Area
              </p>
            </CardFooter>
          </Card>
        </div>
      </div>
    </>
  );
}