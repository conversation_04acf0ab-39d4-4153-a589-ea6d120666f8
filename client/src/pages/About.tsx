import React from "react";
import { motion } from "framer-motion";
import {
  CheckCircleIcon,
  LightbulbIcon,
  GlobeIcon,
  UsersIcon,
  BarChart3Icon,
  ArrowRightIcon,
  LeafIcon,
  BriefcaseIcon,
  BuildingIcon,
  AwardIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link } from "wouter";
import { Button } from "../components/ui/button";
import MetaTags from "../components/seo/MetaTags";
import SchemaOrg from "../components/seo/SchemaOrg";
import CountUp from "react-countup";
import { useLanguage } from "../hooks/use-language";

export default function About() {
  const { t } = useTranslation();
  const { language } = useLanguage();

  // Generate language alternates for about page
  const baseUrl = 'https://metanord.eu';
  const languageAlternates = ['en', 'et', 'ru', 'lv', 'lt', 'pl'].map(lang => ({
    hrefLang: lang,
    href: `${baseUrl}${lang === 'en' ? '' : `/${lang}`}/about`
  }));

  return (
    <>
      <MetaTags
        title={t(
          "about.metaTitle",
          "About Us - MetaNord | Premium Industrial Solutions for European & International Markets"
        )}
        description={t(
          "about.metaDescription",
          "Learn about MetaNord OÜ, an Estonia-based global trading and distribution company specializing in aluminum profiles and infrastructure products for European and international markets."
        )}
        keywords="MetaNord company, aluminum profiles supplier, infrastructure products Estonia, European markets trading, industrial solutions, premium aluminum supplier"
        ogType="website"
        canonical={`${baseUrl}${language === 'en' ? '' : `/${language}`}/about`}
        ogUrl={`${baseUrl}${language === 'en' ? '' : `/${language}`}/about`}
        languageAlternates={languageAlternates}
      />

      <SchemaOrg
        type="website"
        title={t(
          "about.metaTitle",
          "About Us - MetaNord | Premium Industrial Solutions for European & International Markets"
        )}
        description={t(
          "about.metaDescription",
          "Learn about MetaNord OÜ, an Estonia-based global trading and distribution company specializing in aluminum profiles and infrastructure products for European and international markets."
        )}
        url={`${baseUrl}${language === 'en' ? '' : `/${language}`}/about`}
        breadcrumbs={[
          { name: t("home", "Home"), url: "/" },
          { name: t("about.title", "About Us"), url: "/about" },
        ]}
      />

      {/* --- Hero Section --- */}
      <section className="relative pb-20 pt-24 overflow-hidden bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9]">
        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row lg:items-center gap-12">
            {/* Left Side */}
            <motion.div
              className="lg:w-1/2 text-white"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
            >
              <div className="mb-6">
                <h1 className="text-4xl md:text-5xl font-inter font-semibold leading-tight">
                  <span className="text-white">About </span>
                  <span className="text-[#1a365d] font-bold">MetaNord</span>
                </h1>
              </div>
              <h2 className="text-2xl md:text-3xl mb-6 font-medium text-white">
                {t("about.subtitle", "Your Trusted Partner for Quality Infrastructure")}
              </h2>
              <p className="text-lg text-white mb-8 font-roboto max-w-xl leading-relaxed">
                {t(
                  "about.description",
                  "MetaNord OÜ is an Estonia-based trading and distribution company specializing in aluminum profiles and infrastructure products for European and international markets."
                )}
              </p>
            </motion.div>
            {/* Right Side — Global Reach with non-duplicated metrics */}
            <motion.div
              className="lg:w-1/2 flex justify-center"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              <div className="w-full max-w-md bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-lg flex flex-col items-center">
                <div className="mb-3 flex flex-col items-center">
                  <div className="w-16 h-16 rounded-full flex items-center justify-center mb-3 bg-gradient-to-tr from-[#2D7EB6] to-[#40BFB9] shadow-md">
                    <GlobeIcon className="h-10 w-10 text-white" />
                  </div>
                  <span className="text-white font-semibold text-lg tracking-wide mb-1">
                    {t("about.global_reach", "Global Reach")}
                  </span>
                </div>
                <div className="w-full flex flex-wrap gap-4 justify-center mt-2 mb-3">
                  <div className="flex flex-col items-center bg-white/20 rounded-lg px-4 py-3 min-w-[110px]">
                    <AwardIcon className="h-6 w-6 mb-1 text-[#40BFB9]" />
                    <div className="text-white font-bold text-lg">ISO / EN</div>
                    <div className="text-white/80 text-xs text-center">
                      Certified Quality
                    </div>
                  </div>
                  <div className="flex flex-col items-center bg-white/20 rounded-lg px-4 py-3 min-w-[110px]">
                    <CheckCircleIcon className="h-6 w-6 mb-1 text-[#2D7EB6]" />
                    <div className="text-white font-bold text-xl">
                      <CountUp end={99.7} decimals={1} duration={2} />%
                    </div>
                    <div className="text-white/80 text-xs text-center">
                      On-Time Delivery
                    </div>
                  </div>
                  <div className="flex flex-col items-center bg-white/20 rounded-lg px-4 py-3 min-w-[110px]">
                    <BarChart3Icon className="h-6 w-6 mb-1 text-[#2D7EB6]" />
                    <div className="text-white font-bold text-xl">
                      <CountUp end={25} duration={2} />+
                    </div>
                    <div className="text-white/80 text-xs text-center">
                      Active B2B Clients
                    </div>
                  </div>
                  <div className="flex flex-col items-center bg-white/20 rounded-lg px-4 py-3 min-w-[110px]">
                    <UsersIcon className="h-6 w-6 mb-1 text-[#40BFB9]" />
                    <div className="text-white font-bold text-xl">
                      <CountUp end={12} duration={2} />+
                    </div>
                    <div className="text-white/80 text-xs text-center">
                      Strategic Partnerships
                    </div>
                  </div>
                </div>
                <div className="text-white/90 text-sm text-center mt-2 font-medium">
                  {t(
                    "about.distribution",
                    "Premium infrastructure solutions distributed throughout Europe and beyond"
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
        {/* Simplified Wave SVG */}
        <div className="absolute bottom-0 left-0 w-full">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1440 100"
            className="w-full h-auto fill-white"
          >
            <path d="M0,80 Q360,40 720,60 T1440,70 L1440,100 L0,100 Z" />
          </svg>
        </div>
      </section>

      {/* --- Vision & Strategy Section --- */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-center">
            <motion.div
              className="lg:col-span-5"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <div className="glass-card backdrop-blur-md bg-white border border-gray-100 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:translate-y-[-5px]">
                <div
                  className="rounded-full w-12 h-12 flex items-center justify-center mb-6"
                  style={{
                    background: "#2D7EB6",
                  }}
                >
                  <LightbulbIcon className="h-6 w-6 text-white" />
                </div>
                <h2 className="text-2xl font-inter font-semibold mb-6 text-[#1a1a1a]">
                  {t("about.vision.title")}
                </h2>
                <p className="text-lg text-[#0e0e0e] mb-6 font-roboto leading-relaxed font-medium">
                  {t("about.vision.text")}
                </p>
                <div className="border-t border-gray-100 pt-6 mt-6">
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <p className="text-gray-600 text-sm font-medium mb-2">
                        {t("about.experience.title")}
                      </p>
                      <p className="text-lg font-medium text-[#0e0e0e]">
                        {t("about.experience.text")}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600 text-sm font-medium mb-2">
                        {t("about.sustainability.title")}
                      </p>
                      <p className="text-lg font-medium text-[#0e0e0e]">
                        {t("about.sustainability.text")}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            <motion.div
              className="lg:col-span-7"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="glass-card backdrop-blur-md bg-white border border-gray-100 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:translate-y-[-5px]">
                  <div
                    className="rounded-full w-12 h-12 flex items-center justify-center mb-5"
                    style={{
                      background: "#2D7EB6",
                    }}
                  >
                    <GlobeIcon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-inter font-semibold mb-3 text-[#0e0e0e]">
                    {t("about.markets.title")}
                  </h3>
                  <p className="font-roboto text-[#0e0e0e] leading-relaxed font-medium">
                    {t("about.markets.text")}
                  </p>
                </div>
                <div className="glass-card backdrop-blur-md bg-white border border-gray-100 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:translate-y-[-5px]">
                  <div
                    className="rounded-full w-12 h-12 flex items-center justify-center mb-5"
                    style={{
                      background: "#2D7EB6",
                    }}
                  >
                    <BarChart3Icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-inter font-semibold mb-3 text-[#0e0e0e]">
                    {t("about.quality.title")}
                  </h3>
                  <p className="font-roboto text-[#0e0e0e] leading-relaxed font-medium">
                    {t("about.quality.text")}
                  </p>
                </div>
                <div className="glass-card backdrop-blur-md bg-white border border-gray-100 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:translate-y-[-5px]">
                  <div
                    className="rounded-full w-12 h-12 flex items-center justify-center mb-5"
                    style={{
                      background: "#2D7EB6",
                    }}
                  >
                    <LeafIcon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-inter font-semibold mb-3 text-[#0e0e0e]">
                    {t("about.values.sustainability.title")}
                  </h3>
                  <p className="font-roboto text-[#0e0e0e] leading-relaxed font-medium">
                    {t("about.values.sustainability.description")}
                  </p>
                </div>
                <div className="glass-card backdrop-blur-md bg-white border border-gray-100 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:translate-y-[-5px]">
                  <div
                    className="rounded-full w-12 h-12 flex items-center justify-center mb-5"
                    style={{
                      background: "#2D7EB6",
                    }}
                  >
                    <UsersIcon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-inter font-semibold mb-3 text-[#0e0e0e]">
                    {t("about.values.customer.title")}
                  </h3>
                  <p className="font-roboto text-[#0e0e0e] leading-relaxed font-medium">
                    {t("about.values.customer.description")}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* --- Company Story Section --- */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              className="text-center mb-16"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-3xl font-inter font-semibold mb-6">
                <span className="text-[#1a1a1a]">{t("about.our", "Our")}</span>{" "}
                <span className="text-[#2D7EB6]">
                  {t("about.story_word", "Story")}
                </span>
              </h2>
            </motion.div>
            <motion.div
              className="glass-card backdrop-blur-md bg-white border border-gray-100 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:translate-y-[-5px]"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <p className="text-lg text-[#0e0e0e] mb-6 font-roboto leading-relaxed font-medium">
                {t("about.story.paragraph1")}
              </p>
              <p className="text-lg text-[#0e0e0e] mb-6 font-roboto leading-relaxed font-medium">
                {t("about.story.paragraph2")}
              </p>
              <p className="text-lg text-[#0e0e0e] font-roboto leading-relaxed font-medium">
                {t("about.story.paragraph3")}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-10 pt-10 border-t border-gray-100">
                <div
                  className="text-center p-6 rounded-lg flex flex-col items-center justify-center"
                  style={{
                    background: "linear-gradient(to right, #2D7EB6, #40BFB9)",
                  }}
                >
                  <div className="text-4xl font-bold mb-2 relative text-white text-center">
                    <CountUp enableScrollSpy scrollSpyDelay={200} end={15} suffix="+" />
                  </div>
                  <p className="text-white font-medium text-center">
                    {t("about.countries")}
                  </p>
                </div>
                <div
                  className="text-center p-6 rounded-lg flex flex-col items-center justify-center"
                  style={{
                    background: "linear-gradient(to right, #2D7EB6, #40BFB9)",
                  }}
                >
                  <div className="text-4xl font-bold mb-2 relative text-white text-center">
                    <CountUp enableScrollSpy scrollSpyDelay={300} end={200} suffix="+" />
                  </div>
                  <p className="text-white font-medium text-center">
                    {t("about.products")}
                  </p>
                </div>
                <div
                  className="text-center p-6 rounded-lg flex flex-col items-center justify-center"
                  style={{
                    background: "linear-gradient(to right, #2D7EB6, #40BFB9)",
                  }}
                >
                  <div className="text-4xl font-bold mb-2 relative text-white text-center">
                    <CountUp enableScrollSpy scrollSpyDelay={400} end={10} suffix="+" />
                  </div>
                  <p className="text-white font-medium text-center">
                    {t("about.experience.text")}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* --- Mission & Values Section --- */}
      <section className="py-24 bg-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-[0.03]">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(#2D7EB6_1px,transparent_1px)] bg-[size:20px_20px]"></div>
        </div>
        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-16">
            <motion.h2
              className="text-3xl font-inter font-semibold mb-6 text-[#1a1a1a]"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <span className="text-[#1a1a1a]">{t("about.our", "Our")}</span>{" "}
              <span className="text-[#2D7EB6]">
                {t("about.mission_values", "Mission & Values")}
              </span>
            </motion.h2>
            <motion.p
              className="text-lg md:text-xl text-[#0e0e0e] max-w-3xl mx-auto font-roboto font-medium"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {t(
                "about.mission.subtitle",
                "Delivering excellence in industrial solutions with a commitment to quality, reliability, and customer satisfaction."
              )}
            </motion.p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            {/* Quality Value */}
            <motion.div
              className="group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="bg-white border border-gray-200 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 h-full transform hover:scale-[1.01] hover:translate-y-[-5px] overflow-hidden relative">
                <div
                  className="relative mb-6 inline-flex items-center justify-center w-12 h-12 rounded-full p-3"
                  style={{
                    backgroundColor: "#2D7EB6",
                  }}
                >
                  <CheckCircleIcon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-inter font-semibold mb-4 relative z-10 text-[#0e0e0e]">
                  {t("about.values.quality.title", "Product Quality")}
                </h3>
                <p className="font-roboto text-[#0e0e0e] relative z-10 font-medium leading-relaxed">
                  {t(
                    "about.values.quality.description",
                    "We ensure all products meet the highest European quality standards, with rigorous testing and quality control at every stage."
                  )}
                </p>
                <div className="absolute top-0 right-0 w-16 h-16 border-t-2 border-r-2 border-[#2D7EB6]/20 rounded-bl-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </motion.div>
            {/* Partnership Value */}
            <motion.div
              className="group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="bg-white border border-gray-200 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 h-full transform hover:scale-[1.01] hover:translate-y-[-5px] overflow-hidden relative">
                <div
                  className="relative mb-6 inline-flex items-center justify-center w-12 h-12 rounded-full p-3"
                  style={{
                    backgroundColor: "#2D7EB6",
                  }}
                >
                  <BuildingIcon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-inter font-semibold mb-4 relative z-10 text-[#0e0e0e]">
                  {t("about.values.customer.title", "Customer Focus")}
                </h3>
                <p className="font-roboto text-[#0e0e0e] relative z-10 font-medium leading-relaxed">
                  {t(
                    "about.values.customer.description",
                    "We put our customers first, building relationships based on understanding their needs and providing personalized solutions."
                  )}
                </p>
                <div className="absolute top-0 right-0 w-16 h-16 border-t-2 border-r-2 border-[#2D7EB6]/20 rounded-bl-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </motion.div>
            {/* Global Value */}
            <motion.div
              className="group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="bg-white border border-gray-200 rounded-[10px] p-8 shadow-sm hover:shadow-lg transition-all duration-300 h-full transform hover:scale-[1.01] hover:translate-y-[-5px] overflow-hidden relative">
                <div
                  className="relative mb-6 inline-flex items-center justify-center w-12 h-12 rounded-full p-3"
                  style={{
                    backgroundColor: "#2D7EB6",
                  }}
                >
                  <LeafIcon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-inter font-semibold mb-4 relative z-10 text-[#0e0e0e]">
                  {t("about.values.sustainability.title", "Sustainability")}
                </h3>
                <p className="font-roboto text-[#0e0e0e] relative z-10 font-medium leading-relaxed">
                  {t(
                    "about.values.sustainability.description",
                    "We are committed to sustainable business practices and environmentally friendly operations throughout our supply chain."
                  )}
                </p>
                <div className="absolute top-0 right-0 w-16 h-16 border-t-2 border-r-2 border-[#2D7EB6]/20 rounded-bl-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </motion.div>
          </div>
          <motion.div
            className="mt-16 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="inline-block">
              <Link to="/contact">
                <Button
                  className="text-white font-medium px-6 py-3 rounded-[10px] shadow-md flex items-center justify-center gap-2 group relative overflow-hidden transform hover:translate-y-[-2px] transition-all duration-300"
                  style={{
                    background: "linear-gradient(90deg, #2D7EB6, #40BFB9)",
                  }}
                >
                  <span>{t("contact.title", "Contact Us")}</span>
                  <ArrowRightIcon className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}
