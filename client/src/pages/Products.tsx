import React, { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { Search, X } from "lucide-react";
import { ProductCard } from "../components/ui/product-card";
import { ProductPreviewModal } from "../components/ui/product-preview-modal";
import { useAllProducts, useProductCategories, ProductData } from "../hooks/use-product-api"; // ← ВАЖНО!
import { translateProductCategory } from "../i18n";
import MetaTags from "../components/seo/MetaTags";
import SchemaOrg from "../components/seo/SchemaOrg";
import { useLocation } from "wouter";
import BatchLoadingProducts from "../components/ui/batch-loading-products";
import { ProductFiltersSidebar, MobileProductFilters } from "../components/ui/product-filters-sidebar";
import { preloadProductImages } from "../utils/image-preloader";
import { useLanguage } from "../hooks/use-language";


// Filter chips component to display active filters as tags with remove buttons
const FilterChips: React.FC<{
  activeCategory: string;
  activeApplication: string;
  onCategoryChange: (category: string) => void;
  onApplicationChange: (application: string) => void;
  onClearAllFilters?: () => void;
  materialOptions: Array<{ id: string; label: string }>;
  applicationOptions: Array<{ id: string; label: string }>;
}> = ({
  activeCategory,
  activeApplication,
  onCategoryChange,
  onApplicationChange,
  onClearAllFilters,
  materialOptions,
  applicationOptions
}) => {
  const { t } = useTranslation();
  
  // Only show chips container if there are active filters
  if (activeCategory === 'all' && activeApplication === '') {
    return null;
  }
  
  // Find the label for the active category
  const activeCategoryLabel = materialOptions.find(opt => opt.id === activeCategory)?.label || '';
  
  // Find the label for the active application
  const activeApplicationLabel = applicationOptions.find(opt => opt.id === activeApplication)?.label || '';
  
  const hasMultipleFilters = activeCategory !== 'all' && activeApplication !== '';
  
  return (
    <div className="flex flex-wrap items-center gap-2 mb-4 p-2 bg-gray-50 rounded-md">
      <span className="text-sm text-gray-500 mr-1">{t('products.filters.activeFilters', 'Active Filters')}:</span>
      
      {/* Material filter chip */}
      {activeCategory !== 'all' && (
        <button 
          onClick={() => onCategoryChange('all')}
          className="inline-flex items-center gap-1 px-3 py-1 text-sm rounded-md bg-white border border-gray-200 hover:bg-gray-100 transition-colors"
          aria-label={t('products.filters.removeMaterial', 'Remove material filter')}
          title={t('products.filters.clickToRemove', 'Click to remove this filter')}
        >
          {activeCategoryLabel}
          <X className="h-3.5 w-3.5 text-gray-600" aria-hidden="true" />
          <span className="sr-only">{t('products.filters.remove', 'Remove')}</span>
        </button>
      )}
      
      {/* Application filter chip */}
      {activeApplication !== '' && (
        <button 
          onClick={() => onApplicationChange('')}
          className="inline-flex items-center gap-1 px-3 py-1 text-sm rounded-md bg-white border border-gray-200 hover:bg-gray-100 transition-colors"
          aria-label={t('products.filters.removeApplication', 'Remove application filter')}
          title={t('products.filters.clickToRemove', 'Click to remove this filter')}
        >
          {activeApplicationLabel}
          <X className="h-3.5 w-3.5 text-gray-600" aria-hidden="true" />
          <span className="sr-only">{t('products.filters.remove', 'Remove')}</span>
        </button>
      )}
      
      {/* Clear all filters link - only show when multiple filters are active */}
      {hasMultipleFilters && onClearAllFilters && (
        <button 
          onClick={onClearAllFilters}
          className="inline-flex items-center gap-1 px-3 py-1 text-sm text-primary hover:underline transition-colors ml-auto"
          aria-label={t('products.filters.clearAllFilters', 'Clear all filters')}
          title={t('products.filters.resetAllFilters', 'Reset all active filters')}
        >
          {t('products.filters.clearAll', 'Clear all')} <X className="h-3.5 w-3.5" aria-hidden="true" />
        </button>
      )}
    </div>
  );
};

// Ultra-optimized animations with minimal CPU/GPU overhead for smooth filter transitions
const containerVariants = {
  hidden: { opacity: 1 }, // No opacity change to reduce GPU work
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.02, // Minimal staggering to reduce rendering work
      duration: 0.15, // Ultra-short duration for near-instant transitions
      ease: "linear" // Linear easing is computationally lightest
    }
  }
};

// Shallow transform for list items to minimize composite/repaint operations
const itemVariants = {
  hidden: { opacity: 0.98, y: 1 }, // Tiny displacement reduces transform calculations
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.12, // Extremely short duration for immediate visibility
      ease: "linear" // Linear is more efficient than cubic bezier calculations
    }
  }
};

// PERFORMANCE: Memoize product card components to prevent costly re-renders
const MemoizedProductCard = React.memo(ProductCard);

export default function EnhancedProducts() {
  console.log('[EnhancedProducts] Component mounting/rendering');

  const { t, i18n } = useTranslation();
  const { language } = useLanguage();
  const [location, setLocation] = useLocation();

  // Parse URL query parameters - support both 'category' and 'material' parameters
  const getQueryParams = () => {
    const queryParams = new URLSearchParams(window.location.search);
    return {
      material: queryParams.get('category') || queryParams.get('material') || 'all',
      search: queryParams.get('search') || '',
      application: queryParams.get('application') || ''
    };
  };

  // Initialize state from URL query parameters
  const queryParams = getQueryParams();
  const [activeCategory, setActiveCategory] = useState(queryParams.material);
  const [activeApplication, setActiveApplication] = useState(queryParams.application);
  const [searchQuery, setSearchQuery] = useState(queryParams.search);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(queryParams.search);
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  // Modal state
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);

  // For Urban Infrastructure load more functionality
  const [visibleProductCount, setVisibleProductCount] = useState(8);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Handle product preview
  const handleProductPreview = (product: any) => {
    setSelectedProduct(product);
    setIsPreviewOpen(true);
  };
  

  // Fallback to React Query hook as a backup
  const { data: apiProducts, isLoading: isLoadingApiProducts, error: productsError } = useAllProducts();

  // Use direct products if available, otherwise fall back to API products
  const allProducts = apiProducts || [];
  const isLoadingProducts = isLoadingApiProducts;

  // Debug logging for products data
  useEffect(() => {
    console.log(`[Products] Products data received:`, {
      apiProducts: apiProducts?.length || 0,
      allProducts: allProducts?.length || 0,
      isLoading: isLoadingProducts,
      error: productsError
    });

    if (apiProducts && apiProducts.length > 0) {
      console.log(`[Products] Sample product:`, apiProducts[0]);
    }
  }, [apiProducts, allProducts, isLoadingProducts, productsError]);
  
  // Get categories from API
  const productCategories = useProductCategories();

  // Simplified material categories based on product material field
  const materialOptions = useMemo(() => {
    // Helper function to normalize materials as specified in the task
    const normalizeMaterial = (material: string): string => {
      const lower = material.toLowerCase();
      if (lower.includes("aluminum")) return "Aluminum";
      if (lower.includes("steel")) return "Steel";
      if (lower.includes("cast")) return "Cast Iron";
      if (lower.includes("poly")) return "Polyethylene";
      return "Other";
    };

    // Get all materials from product.material field or fallback to category/specifications
    const allMaterials = allProducts.map(p => {
      // Try product.material first, then fallback to specifications.Material, then category
      return p.material || p.specifications?.Material || p.category || '';
    }).filter(Boolean);

    // Normalize and deduplicate materials
    const normalizedMaterials = new Set(allMaterials.map(normalizeMaterial));

    // Build simplified material options - always show the 5 main categories
    const options = [
      { id: 'all', label: t('products.materials.all', 'All Materials') },
      { id: 'aluminum', label: 'Aluminum' },
      { id: 'steel', label: 'Steel' },
      { id: 'cast-iron', label: 'Cast Iron' },
      { id: 'polyethylene', label: 'Polyethylene' },
      { id: 'other', label: 'Other' }
    ];

    return options;
  }, [allProducts, t]);

  // Debug filtered products and category state
  useEffect(() => {
    if (activeCategory === "urban-infrastructure") {
      if (import.meta.env.DEV) console.log("Urban Infrastructure category selected");
      if (import.meta.env.DEV) console.log("Total products:", filteredProducts.length);
      if (import.meta.env.DEV) console.log("Visible product count:", visibleProductCount);
    }
  }, [activeCategory, filteredProducts, visibleProductCount]);
  
  // Update URL when filters change (debounced and memoized for performance)
  const updateURL = useMemo(() => {
    // Build query parameter string
    const queryParts = [];
    
    if (activeCategory && activeCategory !== 'all') {
      queryParts.push(`material=${activeCategory}`);
    }
    
    if (activeApplication && activeApplication !== '') {
      queryParts.push(`application=${activeApplication}`);
    }
    
    if (debouncedSearchQuery) {
      queryParts.push(`search=${encodeURIComponent(debouncedSearchQuery)}`);
    }
    
    // Create new URL
    const queryString = queryParts.length > 0 ? `?${queryParts.join('&')}` : '';
    return `/products${queryString}`;
  }, [activeCategory, activeApplication, debouncedSearchQuery]);
  
  // Separate effect for URL updates to reduce work in render cycles
  useEffect(() => {
    // Only update URL if it's different from current location
    if (location !== updateURL) {
      if (import.meta.env.DEV) console.log(`Updating URL: ${location} -> ${updateURL}`);
      
      // Use requestAnimationFrame to ensure smooth UI updates
      requestAnimationFrame(() => {
        setLocation(updateURL, { replace: true });
        
        // After URL update, ensure we have the correct filters applied
        const params = getQueryParams();
        if (import.meta.env.DEV) console.log(`URL params after update: material=${params.material}, application=${params.application}`);
      });
    }
  }, [updateURL, location, setLocation, getQueryParams]);
  
  // Enhanced sync of state with URL parameters to fix filter issues
  useEffect(() => {
    if (import.meta.env.DEV) console.log("Syncing state from URL parameters");
    
    // Get current parameters from URL
    const params = getQueryParams();
    if (import.meta.env.DEV) console.log(`URL params: material=${params.material}, application=${params.application}, search=${params.search}`);
    
    // Force reload filtered products when parameters change
    let needsRefilter = false;
    
    // Always update category from URL to maintain consistency
    if (params.material !== activeCategory) {
      if (import.meta.env.DEV) console.log(`Setting category from URL: ${params.material}`);
      setActiveCategory(params.material);
      needsRefilter = true;
    }
    
    // Always update application from URL to maintain consistency
    if (params.application !== activeApplication) {
      if (import.meta.env.DEV) console.log(`Setting application from URL: ${params.application}`);
      setActiveApplication(params.application);
      needsRefilter = true;
    }
    
    // Update search query if different
    if (params.search !== searchQuery) {
      if (import.meta.env.DEV) console.log(`Setting search from URL: ${params.search}`);
      // Update both search query values in one tick to reduce re-renders
      requestAnimationFrame(() => {
        setSearchQuery(params.search);
        setDebouncedSearchQuery(params.search);
      });
      needsRefilter = true;
    }
    
    // Force refiltering when needed by clearing the current list
    if (needsRefilter) {
      if (import.meta.env.DEV) console.log("URL parameters changed, triggering refilter");
      setFilteredProducts([]);
    }
  }, [location, activeCategory, activeApplication, searchQuery]);
  
  // Debounce search input for better performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchQuery]);
  
  // SIMPLIFIED: Basic filtering logic without complex categorization
  const getFilteredProducts = useMemo(() => {
    const safeProducts = allProducts || [];

    if (import.meta.env.DEV) {
      console.log(`[Filtering] Processing ${safeProducts.length} products with criteria: category=${activeCategory}, application=${activeApplication}, search=${debouncedSearchQuery}`);
    }

    let result = [...safeProducts];

    // STEP 1: Apply material filter
    if (activeCategory && activeCategory !== 'all') {
      result = result.filter(product => {
        if (!product) return false;

        // Helper function to normalize materials (same as in materialOptions)
        const normalizeMaterial = (material: string): string => {
          const lower = material.toLowerCase();
          if (lower.includes("aluminum")) return "Aluminum";
          if (lower.includes("steel")) return "Steel";
          if (lower.includes("cast")) return "Cast Iron";
          if (lower.includes("poly")) return "Polyethylene";
          return "Other";
        };

        // Get material from product.material field or fallback to category/specifications
        const productMaterial = product.material || product.specifications?.Material || product.category || '';

        if (productMaterial) {
          const normalizedProductMaterial = normalizeMaterial(productMaterial);
          const normalizedActiveCategory = activeCategory.charAt(0).toUpperCase() + activeCategory.slice(1).replace('-', ' ');

          // Compare normalized materials
          if (normalizedProductMaterial === normalizedActiveCategory) return true;
        }

        // Additional fallback for direct category matching
        if (product.category) {
          const normalizeString = (str: string) => str ? str.toLowerCase().replace(/[-_\s]/g, '') : '';
          const normalizedProductCategory = normalizeString(product.category);
          const normalizedActiveCategory = normalizeString(activeCategory);

          // Direct category match
          if (normalizedProductCategory === normalizedActiveCategory) return true;
        }

        return false;
      });

      if (import.meta.env.DEV) console.log(`[Filtering] After material filter: ${result.length} products`);
    }

    // STEP 2: Apply application filter
    if (activeApplication && activeApplication !== '') {
      result = result.filter(product => {
        if (!Array.isArray(product.applications)) return false;

        return product.applications.some((app: string) => {
          const appLower = String(app).toLowerCase();
          const searchLower = activeApplication.toLowerCase();
          return appLower.includes(searchLower) || searchLower.includes(appLower);
        });
      });

      if (import.meta.env.DEV) console.log(`[Filtering] After application filter: ${result.length} products`);
    }

    // STEP 3: Apply search filter
    if (debouncedSearchQuery && debouncedSearchQuery.trim() !== '') {
      const query = debouncedSearchQuery.toLowerCase().trim();

      result = result.filter(product => {
        // Basic text search in title, description, category
        const title = (product.title || '').toLowerCase();
        const description = (product.description || '').toLowerCase();
        const category = (product.category || '').toLowerCase();

        if (title.includes(query) || description.includes(query) || category.includes(query)) {
          return true;
        }

        // Search in features
        if (Array.isArray(product.features) && product.features.some((f: any) =>
          String(f).toLowerCase().includes(query)
        )) {
          return true;
        }

        // Search in applications
        if (Array.isArray(product.applications) && product.applications.some((a: any) =>
          String(a).toLowerCase().includes(query)
        )) {
          return true;
        }

        // Search in product ID
        if (String(product.productId || '').toLowerCase().includes(query)) {
          return true;
        }

        return false;
      });

      if (import.meta.env.DEV) console.log(`[Filtering] After search filter: ${result.length} products`);
    }

    return result;
  }, [allProducts, activeCategory, activeApplication, debouncedSearchQuery]);

  
  // Update filteredProducts when getFilteredProducts changes
  useEffect(() => {
    console.log(`[Products] Updating filteredProducts:`, {
      getFilteredProductsLength: getFilteredProducts.length,
      currentFilteredProductsLength: filteredProducts.length,
      activeCategory,
      activeApplication,
      debouncedSearchQuery
    });

    setFilteredProducts(getFilteredProducts);

    // Preload images for the filtered products to prevent flickering
    if (getFilteredProducts.length > 0) {
      preloadProductImages(getFilteredProducts);
    }
  }, [getFilteredProducts]);
  
  // Performance-optimized category change handler for smooth filter transitions
  const handleCategoryChange = React.useCallback((category: string) => {
    // PERFORMANCE: Start timing for benchmarking
    const startTime = performance.now();
    
    if (import.meta.env.DEV) console.log(`Category change requested: ${category}, current: ${activeCategory}`);
    
    // PERFORMANCE: Skip unnecessary updates if category hasn't changed
    if (category === activeCategory) {
      if (import.meta.env.DEV) console.log(`Category ${category} already active - skipping update`);
      return;
    }
    
    // PERFORMANCE: Use deferred rendering with requestAnimationFrame
    // This prevents UI locking during filter changes by moving state updates
    // to the next frame after current JS execution completes
    requestAnimationFrame(() => {
      // Reset visible product count when changing categories
      setVisibleProductCount(12);
      
      // Update state
      setActiveCategory(category);
    });
    
    // Log performance metrics
    const endTime = performance.now();
    if (import.meta.env.DEV) console.log(`⚡ Category change handler executed in: ${(endTime - startTime).toFixed(2)}ms`);
    
    // CRITICAL FIX: If switching to "all" category, ensure ALL filters are cleared
    if (category === 'all') {
      if (import.meta.env.DEV) console.log("Applying special handling for 'All Products' selection");
      
      // Clear application filter if it exists
      if (activeApplication !== '') {
        if (import.meta.env.DEV) console.log("Clearing application filter when switching to All Products");
        setActiveApplication('');
      }
      
      // Clear search if it exists
      if (searchQuery !== '') {
        if (import.meta.env.DEV) console.log("Clearing search query when switching to All Products");
        setSearchQuery('');
        setDebouncedSearchQuery('');
      }
      
      // CRITICAL FIX: Update URL immediately to ensure consistency
      const newURL = '/products';
      setLocation(newURL, { replace: true });
      return; // Exit early as URL is already updated
    }
    
    // For other categories, build and update URL with new parameter
    const params = new URLSearchParams();
    params.set('material', category);
    
    // Preserve application filter if it exists
    if (activeApplication) {
      params.set('application', activeApplication);
    }
    
    // Preserve search if it exists
    if (searchQuery) {
      params.set('search', searchQuery);
    }
    
    // Update URL with new parameters
    const newURL = `/products?${params.toString()}`;
    setLocation(newURL, { replace: true });
    
  }, [activeCategory, activeApplication, searchQuery, setLocation]);
  
  // FIXED: Application change handler with event propagation fix and state update verification
  const handleApplicationChange = React.useCallback((application: string) => {
    // Prevent event propagation issues
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    if (import.meta.env.DEV) {
      console.group('🔄 Application Change Handler');
      console.log(`Changing application from "${activeApplication}" to "${application}"`);
    }
    
    // Skip if application hasn't changed to prevent unnecessary re-renders
    if (application === activeApplication) {
      if (import.meta.env.DEV) {
        console.log('Application unchanged - skipping update');
        console.groupEnd();
      }
      return;
    }
    
    // Reset visible product count when changing applications
    setVisibleProductCount(12);
    
    // CRITICAL FIX: Force immediate state update with callback verification
    setActiveApplication(prevApplication => {
      if (import.meta.env.DEV) {
        console.log(`State update: activeApplication "${prevApplication}" → "${application}"`);
      }
      return application;
    });
    
    // Build URL parameters
    const params = new URLSearchParams();
    
    // Preserve category filter if not 'all'
    if (activeCategory && activeCategory !== 'all') {
      params.set('material', activeCategory);
    }
    
    // Add application filter if it exists
    if (application) {
      params.set('application', application);
    }
    
    // Preserve search if it exists
    if (searchQuery) {
      params.set('search', searchQuery);
    }
    
    // Update URL with new parameters
    const newURL = params.toString() ? `/products?${params.toString()}` : '/products';
    setLocation(newURL, { replace: true });
    
    if (import.meta.env.DEV) {
      console.log(`URL updated to: ${newURL}`);
      console.groupEnd();
    }
    
    // CRITICAL FIX: Force re-filtering by temporarily clearing filtered products
    setFilteredProducts([]);
    
    // Schedule a verification check to ensure state was updated
    setTimeout(() => {
      if (import.meta.env.DEV) {
        console.log('State update verification check:', {
          activeCategory,
          activeApplication,
          filteredProductsCount: filteredProducts.length
        });
      }
    }, 100);
  }, [activeCategory, activeApplication, searchQuery, setLocation, filteredProducts]);
  
  // Enhanced search clearing with proper URL syncing
  const clearSearch = React.useCallback(() => {
    // Performance optimization: don't update state if already empty
    if (!searchQuery) {
      if (import.meta.env.DEV) console.log("Search already empty, no action needed");
      return;
    }
    
    if (import.meta.env.DEV) console.log(`Clearing search query: "${searchQuery}" -> ""`);
    
    // First clear the filtered products to prevent UI showing old items
    setFilteredProducts([]);
    
    // Update both search states to ensure consistent behavior
    setSearchQuery("");
    setDebouncedSearchQuery("");
    
    // Reset to first page of results for better UX
    setVisibleProductCount(12);
    
    // CRITICAL FIX: Update URL to remove search parameter
    const params = new URLSearchParams();
    
    // Preserve category filter if not 'all'
    if (activeCategory && activeCategory !== 'all') {
      params.set('material', activeCategory);
    }
    
    // Preserve application filter if it exists
    if (activeApplication) {
      params.set('application', activeApplication);
    }
    
    // Update URL with parameters
    const queryString = params.toString();
    const newURL = queryString ? `/products?${queryString}` : '/products';
    setLocation(newURL, { replace: true });
    
  }, [activeCategory, activeApplication, searchQuery, setLocation]);
  
  // Complete overhaul of clearAllFilters function for reliable filter resetting
  const clearAllFilters = React.useCallback(() => {
    if (import.meta.env.DEV) console.log("Clearing all filters completely to reset product view");
    
    // Record current filter states for logging purposes
    const previousCategory = activeCategory;
    const previousApplication = activeApplication;
    const previousSearch = searchQuery;
    
    // STEP 1: Clear filtered products first to prevent UI showing outdated results
    setFilteredProducts([]);
    
    // STEP 2: Reset all filter state variables in a specific order
    setActiveCategory('all');
    setActiveApplication('');
    
    // Clear search if needed
    if (searchQuery) {
      setSearchQuery('');
      setDebouncedSearchQuery('');
    }
    
    // STEP 3: Update URL to reflect cleared filters
    setLocation('/products', { replace: true });
    
    // STEP 4: Log the filter reset for debugging
    if (import.meta.env.DEV) {
      console.log('All filters cleared:', {
        from: {
          category: previousCategory,
          application: previousApplication,
          search: previousSearch
        },
        to: {
          category: 'all',
          application: '',
          search: ''
        }
      });
    }
  }, [activeCategory, activeApplication, searchQuery, setLocation]);
  
  // Extract actual applications from product data
  const applicationOptions = useMemo(() => {
    const applications = [...new Set(allProducts.flatMap(p => p.applications || []).filter(Boolean))];
    return [
      { id: '', label: t('products.applications.all', 'All Applications') },
      ...applications.map(application => ({
        id: application.toLowerCase().replace(/\s+/g, '-'),
        label: application
      }))
    ];
  }, [allProducts, t]);

  return (
    <div className="bg-white">
      <MetaTags
        title={t("products.meta.title", "Products - MetaNord Infrastructure Solutions")}
        description={t("products.meta.description", "Browse our complete product catalog including aluminum profiles, polyethylene pipes, steel pipes, cast iron manholes, and urban infrastructure solutions.")}
        keywords="products catalog, aluminum profiles, polyethylene pipes, steel pipes, cast iron manholes, infrastructure solutions, MetaNord"
        ogType="website"
        canonical={`https://metanord.eu${language === 'en' ? '' : `/${language}`}/products`}
        ogUrl={`https://metanord.eu${language === 'en' ? '' : `/${language}`}/products`}
      />
      <SchemaOrg 
        type="website"
        title={t("products.heading", "Products")}
        description={t("products.subtitle", "Explore our diverse range of high-quality products designed for infrastructure and construction applications.")}
      />
      
      <section className="py-10 xs:py-14 sm:py-16 md:py-20 relative">
        <div className="container mx-auto px-3 xs:px-4 sm:px-6">
          {/* Page Header */}
          <div className="text-center mb-8 xs:mb-10 sm:mb-12">
            <motion.h1
              className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl font-inter font-bold mb-3 xs:mb-4 sm:mb-6 tracking-tight"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {t("products.heading", "Products")}
            </motion.h1>
            <motion.p
              className="text-gray-600 max-w-3xl mx-auto text-base xs:text-lg"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {t("products.subtitle", "Explore our diverse range of high-quality products designed for infrastructure and construction applications.")}
            </motion.p>
          </div>
          
          {/* Mobile filter toggle - only shown on small screens */}
          <div className="lg:hidden mb-6">
            <button 
              onClick={() => setIsFilterDrawerOpen(!isFilterDrawerOpen)}
              className="flex items-center justify-center w-full py-3 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 010 2H4a1 1 0 01-1-1zm3 6a1 1 0 011-1h10a1 1 0 010 2H7a1 1 0 01-1-1zm5 6a1 1 0 011-1h4a1 1 0 010 2h-4a1 1 0 01-1-1z" />
              </svg>
              {isFilterDrawerOpen 
                ? t('products.filters.hideFilters', 'Hide Filters') 
                : t('products.filters.showFilters', 'Show Filters')}
            </button>
          </div>
          
          {/* Mobile Filters - Collapsible, only visible on small/medium screens */}
          {isFilterDrawerOpen && (
            <div className="lg:hidden mb-6">
              <MobileProductFilters
                activeCategory={activeCategory}
                activeApplication={activeApplication}
                searchQuery={searchQuery}
                onCategoryChange={handleCategoryChange}
                onApplicationChange={handleApplicationChange}
                onSearchChange={(e) => setSearchQuery(e.target.value)}
                onClearSearch={clearSearch}
                onClearAllFilters={clearAllFilters}
                productCategories={materialOptions}
              />
              
              {/* Active Filter Chips - Mobile */}
              {(activeCategory !== 'all' || activeApplication !== '') && (
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <FilterChips
                    activeCategory={activeCategory}
                    activeApplication={activeApplication}
                    onCategoryChange={handleCategoryChange}
                    onApplicationChange={handleApplicationChange}
                    onClearAllFilters={clearAllFilters}
                    materialOptions={materialOptions}
                    applicationOptions={applicationOptions}
                  />
                </div>
              )}
            </div>
          )}
          
          {/* Main Content with Sidebar Layout */}
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Desktop Sidebar - Hidden on mobile */}
            <aside className="hidden lg:block shrink-0">
              <ProductFiltersSidebar
                activeCategory={activeCategory}
                activeApplication={activeApplication}
                searchQuery={searchQuery}
                onCategoryChange={handleCategoryChange}
                onApplicationChange={handleApplicationChange}
                onSearchChange={(e) => setSearchQuery(e.target.value)}
                onClearSearch={clearSearch}
                onClearAllFilters={clearAllFilters}
                productCategories={materialOptions}
              />
            </aside>
            
            {/* Main Content Area */}
            <div className="flex-1 min-w-0">
              {/* Active Filter Chips - Desktop */}
              {(activeCategory !== 'all' || activeApplication !== '') && (
                <FilterChips
                  activeCategory={activeCategory}
                  activeApplication={activeApplication}
                  onCategoryChange={handleCategoryChange}
                  onApplicationChange={handleApplicationChange}
                  onClearAllFilters={clearAllFilters}
                  materialOptions={materialOptions}
                  applicationOptions={applicationOptions}
                />
              )}
              
              {/* Loading State */}
              {isLoadingProducts && (
                <div className="py-20 text-center">
                  <div className="w-10 h-10 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-gray-500">{t("common.loading", "Loading products...")}</p>
                </div>
              )}
              
              {/* Error State */}
              {!isLoadingProducts && !filteredProducts?.length && (
                <div className="py-16 text-center">
                  <h3 className="text-xl mb-2 font-medium">
                    {searchQuery ? 
                      t("products.noResults", "No products found for your search") : 
                      t("products.noProducts", "No products available in this category")}
                  </h3>
                  <p className="text-gray-500 mb-6">
                    {searchQuery ? 
                      t("products.tryOtherSearch", "Try a different search term or browse by category") : 
                      t("products.browseAll", "Try selecting a different category or view all products")}
                  </p>
                  {searchQuery && (
                    <button 
                      onClick={clearSearch}
                      className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                    >
                      {t("common.clearSearch", "Clear Search")}
                    </button>
                  )}
                </div>
              )}
              
              {/* Products Grid - Optimized for performance */}
              {!isLoadingProducts && filteredProducts?.length > 0 && (
                <>
                  {activeCategory === "urban-infrastructure" ? (
                    /* Use BatchLoadingProducts for Urban Infrastructure */
                    <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-7">
                      {/* PERFORMANCE: Use memoized product cards in urban infrastructure view */}
                      {filteredProducts.map((product) => (
                        <motion.div key={product.productId || product.slug} variants={itemVariants}>
                          <MemoizedProductCard
                            slug={product.slug}
                            title={product.title}
                            description={product.description}
                            image={product.image}
                            category={product.category}
                            link={`/products/${product.slug}`}
                            features={product.features}
                            applications={product.applications}
                            specifications={product.specifications}
                            onPreviewClick={handleProductPreview}
                          />
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    /* Regular grid display for other categories */
                    <motion.div 
                      className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-7"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                    >
                      {/* PERFORMANCE: Use memoized product cards to prevent expensive re-renders */}
                      {filteredProducts.map((product) => (
                        <motion.div key={product.productId || product.slug} variants={itemVariants}>
                          <MemoizedProductCard
                            slug={product.slug}
                            title={product.title}
                            description={product.description}
                            image={product.image}
                            category={product.category}
                            link={`/products/${product.slug}`}
                            features={product.features}
                            applications={product.applications}
                            specifications={product.specifications}
                            onPreviewClick={handleProductPreview}
                          />
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Product Preview Modal */}
      {selectedProduct && (
        <ProductPreviewModal
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          product={selectedProduct}
        />
      )}
    </div>
  );
}
