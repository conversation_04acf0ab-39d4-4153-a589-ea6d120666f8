import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import MetaTags from "../components/seo/MetaTags";
import SchemaOrg from "../components/seo/SchemaOrg";
import { ServiceCard } from "../components/ui/service-card";
import { useLanguage } from "../hooks/use-language";

import {
  TruckIcon,
  ClipboardCheck,
  FileText,
  ShieldCheck,
  Sliders,
  Bolt,
  Zap,
  Layers3,
  HeadphonesIcon,
  Globe2,
  Clock,
  ShoppingCartIcon
} from "lucide-react";

export default function Services() {
  const { t } = useTranslation();
  const { language } = useLanguage();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const services = [
    {
      icon: TruckIcon,
      title: t("services.logistics.title"),
      description: t("services.logistics.description"),
      features: [
        { text: t("services.logistics.features.0"), icon: TruckIcon },
        { text: t("services.logistics.features.1"), icon: Clipboard<PERSON>heck },
        { text: t("services.logistics.features.2"), icon: Sliders },
      ],
    },
    {
      icon: ClipboardCheck,
      title: t("services.consulting.title"),
      description: t("services.consulting.description"),
      features: [
        { text: t("services.consulting.features.0"), icon: FileText },
        { text: t("services.consulting.features.1"), icon: ShieldCheck },
        { text: t("services.consulting.features.2"), icon: Sliders },
      ],
    },
    {
      icon: Bolt,
      title: t("services.custom.title"),
      description: t("services.custom.description"),
      features: [
        { text: t("services.custom.features.0"), icon: Sliders },
        { text: t("services.custom.features.1"), icon: Zap },
        { text: t("services.custom.features.2"), icon: Layers3 },
      ],
    },
    {
      icon: HeadphonesIcon,
      title: t("services.support.title"),
      description: t("services.support.description"),
      features: [
        { text: t("services.support.features.0"), icon: Globe2 },
        { text: t("services.support.features.1"), icon: Clock },
        { text: t("services.support.features.2"), icon: FileText },
      ],
    },
    {
      icon: ShoppingCartIcon,
      title: t("services.procurement.title"),
      description: t("services.procurement.description"),
      features: [
        { text: t("services.procurement.features.0"), icon: ShoppingCartIcon },
        { text: t("services.procurement.features.1"), icon: Sliders },
        { text: t("services.procurement.features.2"), icon: ShieldCheck },
      ],
    },
  ];

  // Generate language alternates for services page
  const baseUrl = 'https://metanord.eu';
  const languageAlternates = ['en', 'et', 'ru', 'lv', 'lt', 'pl'].map(lang => ({
    hrefLang: lang,
    href: `${baseUrl}${lang === 'en' ? '' : `/${lang}`}/services`
  }));

  return (
    <>
      <MetaTags
        title={t("services.metaTitle")}
        description={t("services.metaDescription")}
        keywords="MetaNord services, logistics, consulting, procurement, infrastructure"
        ogType="website"
        canonical={`${baseUrl}${language === 'en' ? '' : `/${language}`}/services`}
        ogUrl={`${baseUrl}${language === 'en' ? '' : `/${language}`}/services`}
        languageAlternates={languageAlternates}
      />
      <SchemaOrg
        type="website"
        title={t("services.metaTitle")}
        description={t("services.metaDescription")}
        url={`${baseUrl}${language === 'en' ? '' : `/${language}`}/services`}
        breadcrumbs={[
          { name: t("navigation.home", "Home"), url: "/" },
          { name: t("navigation.services", "Services"), url: "/services" },
        ]}
      />

      {/* Hero */}
      <section className="relative bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] text-white pt-24 pb-32 overflow-hidden">
        <div className="container mx-auto px-4 text-center z-10 relative">
          <h1 className="text-4xl md:text-5xl font-semibold text-white mb-3">
            {t("services.sectionTitle")}
          </h1>
          <p className="text-lg md:text-xl font-medium text-white max-w-2xl mx-auto">
            {t("services.subtitle")}
          </p>
        </div>
        <div className="absolute bottom-0 left-0 right-0">
          <svg className="w-full h-24" viewBox="0 0 1440 100" preserveAspectRatio="none">
            <path fill="#ffffff" d="M0,80 Q360,40 720,60 T1440,70 L1440,100 L0,100 Z" />
          </svg>
        </div>
      </section>

      {/* Overview */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">
              {t("services.overview.title")}
            </h2>
            <p className="text-sm text-gray-500 max-w-2xl mx-auto">
              {t("services.overview.description")}
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.slice(0, 3).map((service, i) => (
              <ServiceCard key={i} {...service} showButton={false} />
            ))}
          </div>
        </div>
      </section>

      {/* Process Diagram */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            {t("services.process.title")}
          </h2>
          <div className="relative max-w-5xl mx-auto">
            <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-[#2D7EB6] to-[#40BFB9] transform md:-translate-x-1/2" />
            <div className="space-y-20">
              {[0, 1, 2, 3, 4].map((step, index) => (
                <motion.div
                  key={index}
                  className={`relative flex flex-col md:flex-row items-start ${index % 2 === 1 ? "md:flex-row-reverse" : ""}`}
                >
                  <div className={`md:w-1/2 ${index % 2 === 1 ? "md:pl-12" : "md:pr-12"}`}>
                    <div className={`text-${index % 2 === 1 ? "left" : "right"} max-w-md mx-auto`}>
                      <h3 className="text-xl font-semibold text-gray-800 mb-2">
                        {t(`services.process.steps.${step}.title`)}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {t(`services.process.steps.${step}.description`)}
                      </p>
                    </div>
                  </div>
                  <div
                    className="absolute left-4 md:left-1/2 w-10 h-10 rounded-full z-10 transform md:-translate-x-1/2 flex items-center justify-center shadow-lg"
                    style={{ background: "linear-gradient(135deg, #2D7EB6, #40BFB9)" }}
                  >
                    <span className="text-white font-bold">{step + 1}</span>
                  </div>
                  <div className="hidden md:block md:w-1/2" />
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            {t("services.additionalTitle")}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mt-10">
            {services.slice(3).map((service, index) => (
              <ServiceCard key={index} {...service} showButton={false} />
            ))}
          </div>
        </div>
      </section>
    </>
  );
}
