import React, { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { Search, X } from "lucide-react";
import { ProductCard } from "../components/ui/product-card";
import { useAllProducts, useProductCategories } from "../hooks/use-product-api";
import { translateProductCategory } from "../i18n";
import MetaTags from "../components/seo/MetaTags";
import SchemaOrg from "../components/seo/SchemaOrg";
import { useRoute } from "wouter";

// --- Анимации ---
const containerVariants = {
  hidden: { opacity: 0.95 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.03,
      duration: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0.9, y: 5 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.2 }
  }
};

// --- Категории для маршрутизации ---
const categoryMapping: Record<string, string> = {
  'aluminum': 'aluminum',
  'polyethylene': 'polyethylene',
  'steel': 'steel',
  'cast-iron': 'cast_iron',
  'urban-infrastructure': 'urban',
  'fittings': 'fittings'
};

export default function ProductCategory() {
  const { t, i18n } = useTranslation();
  const [, params] = useRoute('/products/:category');
  const categoryParam = params?.category || '';
  // Приведение route-параметра к ID категории
  const categoryId = categoryMapping[categoryParam] || categoryParam;

  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);

  // Получаем продукты
  const { data: allProducts, isLoading: isLoadingProducts } = useAllProducts();
  // Получаем категории
  const productCategories = useProductCategories();

  // Дебаунс поиска
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // --- Фильтрация продуктов ---
  useEffect(() => {
    if (!allProducts) return;

    let filtered = [...allProducts];

    // --- Категорийная фильтрация ---
    if (categoryId && categoryId !== "all") {
      filtered = filtered.filter(product => {
        const productCategory = String(product.category || '').toLowerCase().replace(/-/g, '_');
        // "Cast Iron" как синоним
        if (categoryId === "cast_iron" || categoryId === "cast-iron") {
          return productCategory === "cast_iron" || productCategory === "cast-iron";
        }
        // Urban: включает drainage и urban-infrastructure
        if (categoryId === "urban" || categoryId === "urban-infrastructure") {
          return (
            productCategory === "urban-infrastructure" ||
            productCategory === "urban_infrastructure" ||
            productCategory === "drainage" ||
            productCategory === "drainage_systems"
          );
        }
        // Fittings как отдельная категория
        if (categoryId === "fittings") {
          return productCategory === "fittings";
        }
        // Обычная проверка
        return (
          productCategory === categoryId.toLowerCase() ||
          productCategory.replace(/-/g, '_') === categoryId.toLowerCase().replace(/-/g, '_')
        );
      });
    }

    // --- Поиск по тексту ---
    if (debouncedSearchQuery) {
      const query = debouncedSearchQuery.toLowerCase();
      filtered = filtered.filter(product => {
        const title = (product.title || '').toLowerCase();
        const description = (product.description || '').toLowerCase();
        const category = (product.category || '').toLowerCase();
        return title.includes(query) || description.includes(query) || category.includes(query);
      });
    }

    setFilteredProducts(filtered);
  }, [allProducts, categoryId, debouncedSearchQuery]);

  // Очистка поиска
  const clearSearch = () => setSearchQuery("");

  // Получение имени категории для вывода
  const getCategoryDisplayName = () => {
    const category = productCategories.find(cat => cat.id === categoryId);
    return category?.label || t(`products.categories.${categoryId}`, translateProductCategory(categoryId));
  };

  return (
    <div className="bg-white">
      <MetaTags
        title={`${getCategoryDisplayName()} - ${t("products.meta.title", "Products - MetaNord")}`}
        description={t("products.meta.description", "Browse our complete product catalog including aluminum profiles, polyethylene pipes, steel pipes, cast iron manholes, and urban infrastructure solutions.")}
        keywords={`${getCategoryDisplayName()}, products, MetaNord, infrastructure solutions`}
        ogType="website"
        canonical={`https://metanord.eu/products/${categoryParam}`}
        ogUrl={`https://metanord.eu/products/${categoryParam}`}
      />
      <SchemaOrg
        type="website"
        title={`${getCategoryDisplayName()} - ${t("products.heading", "Our Products")}`}
        description={t("products.subtitle", "Explore our diverse range of high-quality products designed for infrastructure, construction, and industrial applications.")}
        url={`https://metanord.eu/products/${categoryParam}`}
      />

      <section className="py-10 xs:py-14 sm:py-16 md:py-20 relative">
        <div className="container mx-auto px-3 xs:px-4 sm:px-6">
          {/* --- Заголовок страницы --- */}
          <div className="text-center mb-8 xs:mb-10 sm:mb-12">
            <motion.h1
              className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl font-inter font-bold mb-3 xs:mb-4 sm:mb-6 tracking-tight"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {getCategoryDisplayName()}
            </motion.h1>
            <motion.p
              className="text-gray-600 max-w-3xl mx-auto text-base xs:text-lg"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {t("products.subtitle", "Explore our diverse range of high-quality products designed for infrastructure, construction, and industrial applications.")}
            </motion.p>
          </div>

          {/* --- Фильтры (категории и поиск) --- */}
          <div className="mb-8 sm:mb-10 md:mb-12">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
              {/* Категории */}
              <div className="flex flex-nowrap overflow-x-auto pb-2 md:pb-0 px-0.5 gap-1 xs:gap-1.5 w-full md:w-auto scrollbar-hide">
                {productCategories.map(category => (
                  <a
                    key={category.id}
                    href={category.id === 'all' ? '/products' : `/products/${category.id}`}
                    className={`px-3 xs:px-4 py-2 text-sm rounded-md whitespace-nowrap transition-colors ${
                      categoryId === category.id
                        ? "bg-gradient-to-r from-cyan-500 to-teal-500 text-white font-medium"
                        : "bg-white border border-gray-300 hover:bg-gray-50 text-gray-700"
                    }`}
                  >
                    {category.label}
                  </a>
                ))}
              </div>
              {/* Поиск */}
              <div className="relative w-full md:w-64">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder={t("common.search", "Search...")}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-gray-100 pl-10 pr-10 py-2 rounded-md outline-none focus:ring-1 focus:ring-primary"
                />
                {searchQuery && (
                  <button
                    onClick={clearSearch}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* --- Loading --- */}
          {isLoadingProducts && (
            <div className="py-20 text-center">
              <div className="w-10 h-10 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-500">{t("common.loading", "Loading products...")}</p>
            </div>
          )}

          {/* --- Нет продуктов --- */}
          {!isLoadingProducts && !filteredProducts?.length && (
            <div className="py-16 text-center">
              <h3 className="text-xl mb-2 font-medium">
                {searchQuery ? 
                  t("products.noResults", "No products found for your search") : 
                  t("products.noProducts", "No products available in this category")}
              </h3>
              <p className="text-gray-500 mb-6">
                {searchQuery ? 
                  t("products.tryOtherSearch", "Try a different search term or browse by category") : 
                  t("products.browseAll", "Try selecting a different category or view all products")}
              </p>
              {searchQuery && (
                <button 
                  onClick={clearSearch}
                  className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-teal-500 text-white rounded-full hover:from-cyan-600 hover:to-teal-600 transition-colors"
                >
                  {t("common.clearSearch", "Clear Search")}
                </button>
              )}
            </div>
          )}

          {/* --- Грид продуктов --- */}
          {!isLoadingProducts && filteredProducts?.length > 0 && (
            <motion.div
              className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 md:gap-5"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {filteredProducts.map((product, index) => (
                <motion.div key={product.slug || `product-${index}`} variants={itemVariants}>
                  <ProductCard
                    slug={product.slug}
                    title={product.title}
                    image={product.image}
                    category={translateProductCategory(product.category)}
                    description={product.description}
                    link={product.link || `/products/${product.slug}`}
                    features={product.features}
                    applications={product.applications}
                    specifications={product.specifications}
                  />
                </motion.div>
              ))}
            </motion.div>
          )}
        </div>
      </section>
    </div>
  );
}
