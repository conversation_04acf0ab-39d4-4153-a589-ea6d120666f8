import { ContactForm } from "../components/forms/ContactForm";
import { useTranslation } from "react-i18next";
import {
  Briefcase,
  Clipboard,
  MapPin,
  Phone,
  Mail,
  Globe,
} from "lucide-react";
import MetaTags from "../components/seo/MetaTags";
import SchemaOrg from "../components/seo/SchemaOrg";
import { useLanguage } from "../hooks/use-language";

export default function Contact() {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const labelColor = "#40BFB9";

  const contactInfo = [
    {
      icon: <Briefcase className="w-5 h-5 text-white" />,
      label: t("contact.headers.company", "Company"),
      value: t("contact.info.company", "MetaNord OÜ"),
    },
    {
      icon: <Clipboard className="w-5 h-5 text-white" />,
      label: t("contact.headers.registry", "Registry Code"),
      value: t("contact.info.registry", "17235227"),
    },
    {
      icon: <MapPin className="w-5 h-5 text-white" />,
      label: t("contact.headers.address", "Address"),
      value: t("contact.info.address", "Tornimäe 5, Tallinn, 10145, Estonia"),
      link: "https://goo.gl/maps/YJ4SytAoGHbQXauy8",
    },
    {
      icon: <Phone className="w-5 h-5 text-white" />,
      label: t("contact.headers.phone", "Phone"),
      value: t("contact.info.phone", "+372 55589800"),
      link: "tel:+37255589800",
    },
    {
      icon: <Mail className="w-5 h-5 text-white" />,
      label: t("contact.headers.email", "Email"),
      value: t("contact.info.email", "<EMAIL>"),
      link: "mailto:<EMAIL>",
    },
    {
      icon: <Globe className="w-5 h-5 text-white" />,
      label: t("contact.headers.website", "Website"),
      value: t("contact.info.website", "www.metanord.eu"),
      link: "https://www.metanord.eu",
    },
  ];

  // Generate language alternates for contact page
  const baseUrl = 'https://metanord.eu';
  const languageAlternates = ['en', 'et', 'ru', 'lv', 'lt', 'pl'].map(lang => ({
    hrefLang: lang,
    href: `${baseUrl}${lang === 'en' ? '' : `/${lang}`}/contact`
  }));

  return (
    <>
      <MetaTags
        title={t("contact.metaTitle", "Contact Us - MetaNord")}
        description={t("contact.metaDescription", "Get in touch with MetaNord for aluminum profiles, infrastructure products, and industrial solutions. Contact our team for quotes, technical support, and project consultation.")}
        keywords="contact MetaNord, aluminum profiles quote, infrastructure products inquiry, industrial solutions contact, MetaNord Estonia"
        ogType="website"
        canonical={`${baseUrl}${language === 'en' ? '' : `/${language}`}/contact`}
        ogUrl={`${baseUrl}${language === 'en' ? '' : `/${language}`}/contact`}
        languageAlternates={languageAlternates}
      />

      <SchemaOrg
        type="contactpage"
        title={t("contact.metaTitle", "Contact Us - MetaNord")}
        description={t("contact.metaDescription", "Get in touch with MetaNord for aluminum profiles, infrastructure products, and industrial solutions.")}
        url={`${baseUrl}${language === 'en' ? '' : `/${language}`}/contact`}
        breadcrumbs={[
          { name: t("navigation.home", "Home"), url: "/" },
          { name: t("navigation.contact", "Contact"), url: "/contact" },
        ]}
      />

      <section className="relative bg-white min-h-screen pb-20">
      {/* --- Шапка с градиентом и волной --- */}
      <div className="relative overflow-hidden bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] pt-24 pb-32">
        <div className="container mx-auto px-4 relative z-10 text-center">
          <h1 className="text-4xl md:text-5xl font-semibold text-white mb-3">
            {t("contact.simplifiedTitle", "Contact Us")}
          </h1>
          <p
            className="text-lg md:text-xl font-medium text-white max-w-2xl mx-auto"
            style={{
              textShadow: "0 2px 16px #2D7EB6aa, 0 1px 3px #40BFB966",
            }}
          >
            {t(
              "contact.hero.subtitle",
              "We're here to answer your questions and provide the solutions you need"
            )}
          </p>
        </div>

        {/* Simplified Wave SVG */}
        <div className="absolute bottom-0 left-0 w-full">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1440 100"
            className="w-full h-auto fill-white"
          >
            <path d="M0,80 Q360,40 720,60 T1440,70 L1440,100 L0,100 Z" />
          </svg>
        </div>
      </div>

      {/* --- Контактная форма и блок с инфой --- */}
      <div className="container mx-auto grid grid-cols-1 md:grid-cols-2 gap-10 items-start px-4 mt-10 md:mt-20 lg:mt-24">
        {/* Левая колонка: заголовок + форма */}
        <div>
          <h2 className="text-2xl font-bold mb-2 text-black">
            {t("contact.writeUs.title", "Write Us")}
          </h2>
          <p className="text-sm text-gray-600 mb-6">
            {t(
              "contact.writeUs.subtitle",
              "Fill out the form below and our team will get back to you as soon as possible."
            )}
          </p>
          <ContactForm labelColor={labelColor} />
        </div>

        {/* Правая колонка: контактная информация */}
        <div>
          <h2 className="text-2xl font-bold mb-4 text-black">
            {t("contact.info.title", "Contact Information")}
          </h2>
          <ul className="space-y-4 mb-6">
            {contactInfo.map((item, i) => (
              <li key={i} className="flex items-start gap-3">
                <div className="bg-[#2D7EB6] p-2 rounded-full">{item.icon}</div>
                <div>
                  <div
                    className="font-semibold text-base"
                    style={{ color: labelColor }}
                  >
                    {item.label}
                  </div>
                  {item.link ? (
                    <a
                      href={item.link}
                      className="text-sm font-medium text-black underline hover:text-[#2D7EB6] transition"
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ wordBreak: "break-word" }}
                    >
                      {item.value}
                    </a>
                  ) : (
                    <div className="text-sm font-medium text-black">
                      {item.value}
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>

          {/* Google Map */}
          <div className="relative rounded-xl overflow-hidden h-56 w-full mt-4 shadow border border-gray-200">
            <iframe
              src="https://maps.google.com/maps?q=Tornimäe%205,%20Tallinn,%2010145,%20Estonia&t=&z=16&ie=UTF8&iwloc=&output=embed"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="MetaNord Office Location - Tornimäe 5, Tallinn, 10145, Estonia"
            />
          </div>
        </div>
      </div>
    </section>
    </>
  );
}
