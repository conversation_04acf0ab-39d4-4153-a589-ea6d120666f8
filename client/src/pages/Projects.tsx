import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "wouter";
import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { PageHeader } from "../components/ui/page-header";
import { Tabs, TabsList, TabsTrigger } from "../components/ui/tabs";
import { Card, CardContent, CardFooter } from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Loader2, ArrowRight } from "lucide-react";
import MetaTags from "../components/seo/MetaTags";
import SchemaOrg from "../components/seo/SchemaOrg";
import { useLanguage } from "../hooks/use-language";

export default function Projects() {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [category, setCategory] = useState<ProjectCategory>("all");

  const { data, isLoading, error } = useQuery({
    queryKey: ["projects", language],
    queryFn: async () => {
      const res = await fetch(`/api/projects?language=${language}`);
      if (!res.ok) throw new Error("Failed to fetch projects");
      return res.json();
    },
    staleTime: 60 * 1000, // кешируем на минуту
    enabled: !!language,
  });

  const filteredProjects = useMemo(() => {
    if (!data) return [];
    if (category === "all") return data;
    return data.filter((project: any) => project.category === category);
  }, [data, category]);

  if (isLoading) {
    return (
      <div className="container py-12 flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-12">
        <h2 className="text-2xl font-bold mb-4">{t("common.error")}</h2>
        <p>{t("common.errorLoading")}</p>
      </div>
    );
  }

  return (
    <>
      <MetaTags
        title={t("projects.metaTitle", "Featured Projects - MetaNord")}
        description={t("projects.metaDescription", "Explore our successful featured projects...")}
        keywords="projects, infrastructure, commercial, industrial, residential, MetaNord case studies"
        ogType="website"
        canonical={`https://metanord.eu${language === 'en' ? '' : `/${language}`}/projects`}
        ogUrl={`https://metanord.eu${language === 'en' ? '' : `/${language}`}/projects`}
      />

      <SchemaOrg
        type="website"
        title={t("projects.metaTitle")}
        description={t("projects.metaDescription")}
        url={`https://metanord.eu${language === 'en' ? '' : `/${language}`}/projects`}
        breadcrumbs={[
          { name: t("home"), url: "/" },
          { name: t("projects.pageTitle"), url: "/projects" }
        ]}
      />

      <PageHeader
        title={t("projects.pageTitle")}
        description={t("projects.pageDescription")}
      />

      <div className="container py-6 sm:py-8 px-4 sm:px-6">
        {/* Filter Tabs */}
        <div className="mb-6 sm:mb-8 overflow-x-hidden">
          <Tabs defaultValue="all" onValueChange={(val) => setCategory(val as ProjectCategory)}>
            <TabsList className="grid grid-cols-3 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-1 xs:gap-2 max-w-4xl mx-auto w-full">
              <TabsTrigger value="all">{t("projects.filterAll")}</TabsTrigger>
              <TabsTrigger value="infrastructure">{t("projects.filterInfrastructure")}</TabsTrigger>
              <TabsTrigger value="commercial">{t("projects.filterCommercial")}</TabsTrigger>
              <TabsTrigger value="industrial">{t("projects.filterIndustrial")}</TabsTrigger>
              <TabsTrigger value="residential">{t("projects.filterResidential")}</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {filteredProjects.length > 0 ? (
          <>
            <p className="text-center text-sm text-muted-foreground mb-6">
              {t("projects.totalProjects", { count: filteredProjects.length })}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {filteredProjects.map((project: any) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-col h-full"
                >
                  <Link href={`/projects/${project.id}`}>
                    <Card className="overflow-hidden h-full hover-lift transition-300 cursor-pointer">
                      <div className="relative h-48 sm:h-64 overflow-hidden">
                        <img
                          src={project.featuredImage}
                          alt={project.title}
                          className="w-full h-full object-cover transition-all duration-300 hover:scale-105"
                          loading="lazy"
                        />
                        <Badge className="absolute top-3 right-3 bg-primary text-xs whitespace-nowrap">
                          {t(`projects.${project.category}`)}
                        </Badge>
                      </div>
                      <CardContent className="pt-4 pb-3 flex-grow">
                        <h3 className="text-lg font-bold mb-2">{project.title}</h3>
                        <p className="text-muted-foreground text-sm line-clamp-3">{project.summary}</p>
                        <div className="flex flex-wrap gap-1.5 mt-auto">
                          {(project.productTags || []).slice(0, 3).map((tag: string, index: number) => (
                            <Badge key={index} variant="outline" className="bg-background/50 text-xs py-1">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                      <CardFooter className="pt-0 pb-3">
                        <Button variant="ghost" className="p-0 hover:text-primary text-sm">
                          {t("projects.viewProject")}
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </Link>
                </motion.div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium mb-2">{t("projects.noProjectsFound")}</h3>
            <p className="text-muted-foreground mb-6">{t("projects.tryDifferentFilter")}</p>
            <Button variant="outline" onClick={() => setCategory("all")}>
              {t("projects.viewAllProjects")}
            </Button>
          </div>
        )}
      </div>
    </>
  );
}
