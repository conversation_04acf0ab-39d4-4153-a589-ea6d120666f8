import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import {
  BriefcaseIcon,
  UsersIcon,
  GlobeIcon,
  TrendingUpIcon,
  HeartIcon,
  MapPinIcon,
  ClockIcon,
  BuildingIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  MailIcon,
  PhoneIcon,
  UserIcon,
  FileTextIcon,
  SendIcon,
  Briefcase,
  Clipboard,
  MapPin,
  Phone,
  Mail,
  Globe,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link } from "wouter";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Textarea } from "../components/ui/textarea";
import { Label } from "../components/ui/label";
import MetaTags from "../components/seo/MetaTags";
import SchemaOrg from "../components/seo/SchemaOrg";
import { ContactForm } from "../components/forms/ContactForm";
import { useLanguage } from "../hooks/use-language";
import { apiRequest } from "../lib/queryClient";
import { CareersErrorBoundary } from "../components/CareersErrorBoundary";
import { runQuickDiagnostics } from "../utils/production-diagnostics";

interface JobPosition {
  id: number;
  title: string;
  department: string;
  location: string;
  type: string;
  description: string;
  slug: string;
  language: string;
  published: boolean;
  responsibilities: string[];
  requirements: string[];
  createdAt: string;
  updatedAt: string;
}

export default function Careers() {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [selectedJob, setSelectedJob] = useState<JobPosition | null>(null);
  const [isApplicationModalOpen, setIsApplicationModalOpen] = useState(false);
  const [applicationForm, setApplicationForm] = useState({
    position: "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    coverLetter: "",
    resume: null as File | null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle");

  // Fallback job positions data from translations
  const getFallbackJobPositions = (): JobPosition[] => {
    const jobs = [];

    // Sales Manager
    if (t("careers.jobs.salesManager.title") !== "careers.jobs.salesManager.title") {
      jobs.push({
        id: 1,
        title: t("careers.jobs.salesManager.title", "Sales Manager"),
        department: t("careers.jobs.salesManager.department", "Sales"),
        location: t("careers.jobs.salesManager.location", "Tallinn, Estonia"),
        type: t("careers.jobs.salesManager.type", "Full-time"),
        description: t("careers.jobs.salesManager.description", "Lead our sales efforts across European markets, developing client relationships and driving revenue growth in the industrial infrastructure sector."),
        slug: "sales-manager",
        language: language,
        published: true,
        responsibilities: Array.isArray(t("careers.jobs.salesManager.responsibilities", { returnObjects: true }))
          ? t("careers.jobs.salesManager.responsibilities", { returnObjects: true }) as string[]
          : [
              "Develop and execute sales strategies for European markets",
              "Build and maintain relationships with key B2B clients",
              "Identify new business opportunities in infrastructure sectors",
              "Collaborate with technical teams to provide customer solutions",
              "Manage sales pipeline and achieve revenue targets"
            ],
        requirements: Array.isArray(t("careers.jobs.salesManager.requirements", { returnObjects: true }))
          ? t("careers.jobs.salesManager.requirements", { returnObjects: true }) as string[]
          : [
              "Bachelor's degree in Business, Engineering, or related field",
              "5+ years of B2B sales experience, preferably in industrial products",
              "Strong understanding of European markets and regulations",
              "Excellent communication skills in English and at least one other European language",
              "Experience with CRM systems and sales analytics"
            ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }

    // Production Engineer
    if (t("careers.jobs.productionEngineer.title") !== "careers.jobs.productionEngineer.title") {
      jobs.push({
        id: 2,
        title: t("careers.jobs.productionEngineer.title", "Production Engineer"),
        department: t("careers.jobs.productionEngineer.department", "Engineering"),
        location: t("careers.jobs.productionEngineer.location", "Tallinn, Estonia"),
        type: t("careers.jobs.productionEngineer.type", "Full-time"),
        description: t("careers.jobs.productionEngineer.description", "Oversee production processes and quality control for our aluminum profiles and infrastructure products, ensuring compliance with European standards."),
        slug: "production-engineer",
        language: language,
        published: true,
        responsibilities: Array.isArray(t("careers.jobs.productionEngineer.responsibilities", { returnObjects: true }))
          ? t("careers.jobs.productionEngineer.responsibilities", { returnObjects: true }) as string[]
          : [
              "Monitor and optimize aluminum profile production processes",
              "Implement quality control procedures and standards",
              "Coordinate with suppliers and logistics teams",
              "Ensure compliance with European safety and quality regulations",
              "Troubleshoot production issues and implement improvements"
            ],
        requirements: Array.isArray(t("careers.jobs.productionEngineer.requirements", { returnObjects: true }))
          ? t("careers.jobs.productionEngineer.requirements", { returnObjects: true }) as string[]
          : [
              "Bachelor's degree in Mechanical, Industrial, or Materials Engineering",
              "3+ years of experience in manufacturing or production",
              "Knowledge of aluminum processing and quality standards",
              "Experience with production planning and optimization",
              "Strong analytical and problem-solving skills"
            ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }

    // Logistics Coordinator
    if (t("careers.jobs.logisticsCoordinator.title") !== "careers.jobs.logisticsCoordinator.title") {
      jobs.push({
        id: 3,
        title: t("careers.jobs.logisticsCoordinator.title", "Logistics Coordinator"),
        department: t("careers.jobs.logisticsCoordinator.department", "Operations"),
        location: t("careers.jobs.logisticsCoordinator.location", "Tallinn, Estonia"),
        type: t("careers.jobs.logisticsCoordinator.type", "Full-time"),
        description: t("careers.jobs.logisticsCoordinator.description", "Coordinate international shipments and supply chain operations, ensuring timely delivery of products to European and international markets."),
        slug: "logistics-coordinator",
        language: language,
        published: true,
        responsibilities: Array.isArray(t("careers.jobs.logisticsCoordinator.responsibilities", { returnObjects: true }))
          ? t("careers.jobs.logisticsCoordinator.responsibilities", { returnObjects: true }) as string[]
          : [
              "Manage international shipments and customs documentation",
              "Coordinate with freight forwarders and logistics partners",
              "Track inventory levels and optimize warehouse operations",
              "Ensure compliance with international trade regulations",
              "Resolve logistics issues and improve delivery times"
            ],
        requirements: Array.isArray(t("careers.jobs.logisticsCoordinator.requirements", { returnObjects: true }))
          ? t("careers.jobs.logisticsCoordinator.requirements", { returnObjects: true }) as string[]
          : [
              "Bachelor's degree in Logistics, Supply Chain, or Business",
              "2+ years of experience in international logistics",
              "Knowledge of European customs and trade regulations",
              "Experience with logistics software and tracking systems",
              "Strong organizational and communication skills"
            ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }

    return jobs;
  };

  // Fetch job postings from API with enhanced error handling
  const { data: jobPositions = [], isLoading, error } = useQuery({
    queryKey: ["careers", language],
    queryFn: async () => {
      try {
        const response = await apiRequest("GET", `/api/careers?language=${language}`);
        const data = await response.json();

        return data;
      } catch (error) {
        console.error("[Careers] API request failed:", error);

        // In production, run diagnostics to help identify the issue
        if (!import.meta.env.DEV) {
          console.log("[Careers] Running production diagnostics...");
          runQuickDiagnostics().catch(console.error);
        }

        // Return fallback data
        console.warn("[Careers] Using fallback job positions");
        return getFallbackJobPositions();
      }
    },
    // Use fallback data immediately if API is not available
    placeholderData: getFallbackJobPositions(),
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });





  const handleApplicationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus("success");
      setApplicationForm({
        position: "",
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        coverLetter: "",
        resume: null,
      });
      setTimeout(() => {
        setIsApplicationModalOpen(false);
        setSubmitStatus("idle");
      }, 3000);
    } catch (error) {
      setSubmitStatus("error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const openApplicationModal = (job: JobPosition) => {
    setSelectedJob(job);
    setApplicationForm(prev => ({ ...prev, position: job.title }));
    setIsApplicationModalOpen(true);
  };



  // Generate language alternates for careers page
  const baseUrl = 'https://metanord.eu';
  const languageAlternates = ['en', 'et', 'ru', 'lv', 'lt', 'pl'].map(lang => ({
    hrefLang: lang,
    href: `${baseUrl}${lang === 'en' ? '' : `/${lang}`}/careers`
  }));

  return (
    <CareersErrorBoundary>
      <MetaTags
        title={t("careers.metaTitle", "Careers - Join MetaNord | Industrial Infrastructure Opportunities")}
        description={t("careers.metaDescription", "Join MetaNord's growing team in Estonia. Explore career opportunities in industrial infrastructure, sales, engineering, and logistics. Build your future with a leading European supplier.")}
        keywords="MetaNord careers, jobs Estonia, industrial infrastructure careers, sales engineer jobs, logistics coordinator, European company jobs"
        ogType="website"
        canonical={`${baseUrl}${language === 'en' ? '' : `/${language}`}/careers`}
        ogUrl={`${baseUrl}${language === 'en' ? '' : `/${language}`}/careers`}
        languageAlternates={languageAlternates}
      />

      <SchemaOrg
        type="website"
        title={t("careers.metaTitle", "Careers - Join MetaNord | Industrial Infrastructure Opportunities")}
        description={t("careers.metaDescription", "Join MetaNord's growing team in Estonia. Explore career opportunities in industrial infrastructure, sales, engineering, and logistics. Build your future with a leading European supplier.")}
        url={`${baseUrl}${language === 'en' ? '' : `/${language}`}/careers`}
        breadcrumbs={[
          { name: t("header.home", "Home"), url: "/" },
          { name: t("header.careers", "Careers"), url: "/careers" },
        ]}
      />

      {/* Hero Section */}
      <section className="relative min-h-[300px] py-16 overflow-hidden bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9]">
        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row lg:items-center gap-12">
            {/* Left Side */}
            <motion.div
              className="lg:w-1/2 text-white"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
            >
              <div className="mb-6">
                <h1 className="text-4xl md:text-5xl font-inter font-semibold leading-tight">
                  <span className="text-white">{t("careers.pageTitle", "Join Our Team")}</span>
                </h1>
                <h2 className="text-2xl md:text-3xl mt-4 font-medium text-white/90">
                  {t("careers.pageSubtitle", "Build Your Career with MetaNord")}
                </h2>
              </div>
              <p className="text-lg text-white/90 mb-8 font-roboto max-w-xl leading-relaxed">
                {t("careers.heroDescription", "At MetaNord, we're building the infrastructure of tomorrow. Join our dynamic team and help shape the future of European industrial solutions.")}
              </p>
              <div className="flex flex-wrap gap-4">
                <Link to="#positions">
                  <Button className="bg-white text-[#2D7EB6] hover:bg-white/90 font-medium px-6 py-3 rounded-lg shadow-md transition-all duration-300">
                    {t("careers.openPositions", "Open Positions")}
                  </Button>
                </Link>
              </div>
            </motion.div>

            {/* Right Side - Benefits Preview */}
            <motion.div
              className="lg:w-1/2 flex justify-center"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              <div className="w-full max-w-md bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-lg">
                <div className="text-center mb-6">
                  <BriefcaseIcon className="h-12 w-12 text-white mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {t("careers.whyWorkWithUs", "Why Work With Us")}
                  </h3>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 text-white/90">
                    <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                      <CheckCircleIcon className="h-4 w-4 text-[#2D7EB6]" />
                    </div>
                    <span className="text-sm">{t("careers.benefits.competitive", "Competitive salary packages")}</span>
                  </div>
                  <div className="flex items-center gap-3 text-white/90">
                    <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                      <CheckCircleIcon className="h-4 w-4 text-[#2D7EB6]" />
                    </div>
                    <span className="text-sm">{t("careers.benefits.flexible", "Flexible working arrangements")}</span>
                  </div>
                  <div className="flex items-center gap-3 text-white/90">
                    <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                      <CheckCircleIcon className="h-4 w-4 text-[#2D7EB6]" />
                    </div>
                    <span className="text-sm">{t("careers.benefits.training", "Professional development opportunities")}</span>
                  </div>
                  <div className="flex items-center gap-3 text-white/90">
                    <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                      <CheckCircleIcon className="h-4 w-4 text-[#2D7EB6]" />
                    </div>
                    <span className="text-sm">{t("careers.benefits.international", "International project exposure")}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Wave SVG - Simple, smooth curves */}
        <div className="absolute bottom-0 left-0 w-full">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1440 80"
            className="w-full h-auto fill-white"
          >
            <path d="M0,60 Q360,30 720,45 T1440,50 L1440,80 L0,80 Z" />
          </svg>
        </div>
      </section>

      {/* Why Work With Us Section */}
      <section className="py-24 bg-white" id="culture">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl md:text-4xl font-inter font-semibold mb-6">
              <span className="text-[#1a1a1a]">{t("careers.whyWorkWithUs", "Why Work With Us")}</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-roboto leading-relaxed">
              {t("careers.whyWorkDescription", "MetaNord offers a collaborative environment where innovation meets expertise. We value professional growth, work-life balance, and meaningful contributions to infrastructure development across Europe.")}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Innovation */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="bg-white border border-gray-200 rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 h-full transform hover:scale-[1.02] hover:translate-y-[-5px]">
                <div className="w-16 h-16 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-full flex items-center justify-center mx-auto mb-6">
                  <TrendingUpIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-inter font-semibold mb-4 text-[#1a1a1a]">
                  {t("careers.culture.innovation.title", "Innovation")}
                </h3>
                <p className="text-gray-600 font-roboto leading-relaxed">
                  {t("careers.culture.innovation.description", "We encourage creative thinking and innovative solutions to complex industrial challenges.")}
                </p>
              </div>
            </motion.div>

            {/* Professional Growth */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="bg-white border border-gray-200 rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 h-full transform hover:scale-[1.02] hover:translate-y-[-5px]">
                <div className="w-16 h-16 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-full flex items-center justify-center mx-auto mb-6">
                  <TrendingUpIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-inter font-semibold mb-4 text-[#1a1a1a]">
                  {t("careers.culture.growth.title", "Professional Growth")}
                </h3>
                <p className="text-gray-600 font-roboto leading-relaxed">
                  {t("careers.culture.growth.description", "Continuous learning opportunities and career advancement in a growing international company.")}
                </p>
              </div>
            </motion.div>

            {/* Team Collaboration */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="bg-white border border-gray-200 rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 h-full transform hover:scale-[1.02] hover:translate-y-[-5px]">
                <div className="w-16 h-16 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-full flex items-center justify-center mx-auto mb-6">
                  <UsersIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-inter font-semibold mb-4 text-[#1a1a1a]">
                  {t("careers.culture.collaboration.title", "Team Collaboration")}
                </h3>
                <p className="text-gray-600 font-roboto leading-relaxed">
                  {t("careers.culture.collaboration.description", "Work with experienced professionals in a supportive, multicultural environment.")}
                </p>
              </div>
            </motion.div>

            {/* Meaningful Impact */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="bg-white border border-gray-200 rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 h-full transform hover:scale-[1.02] hover:translate-y-[-5px]">
                <div className="w-16 h-16 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-full flex items-center justify-center mx-auto mb-6">
                  <HeartIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-inter font-semibold mb-4 text-[#1a1a1a]">
                  {t("careers.culture.impact.title", "Meaningful Impact")}
                </h3>
                <p className="text-gray-600 font-roboto leading-relaxed">
                  {t("careers.culture.impact.description", "Contribute to infrastructure projects that shape communities across Europe and beyond.")}
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl md:text-4xl font-inter font-semibold mb-6">
              <span className="text-[#1a1a1a]">{t("careers.benefits.title", "Benefits & Perks")}</span>
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <motion.div
              className="flex items-center gap-4 bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="w-12 h-12 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-lg flex items-center justify-center">
                <CheckCircleIcon className="h-6 w-6 text-white" />
              </div>
              <span className="font-medium text-gray-800">{t("careers.benefits.competitive", "Competitive salary packages")}</span>
            </motion.div>

            <motion.div
              className="flex items-center gap-4 bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="w-12 h-12 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-lg flex items-center justify-center">
                <HeartIcon className="h-6 w-6 text-white" />
              </div>
              <span className="font-medium text-gray-800">{t("careers.benefits.health", "Comprehensive health insurance")}</span>
            </motion.div>

            <motion.div
              className="flex items-center gap-4 bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="w-12 h-12 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-lg flex items-center justify-center">
                <ClockIcon className="h-6 w-6 text-white" />
              </div>
              <span className="font-medium text-gray-800">{t("careers.benefits.vacation", "Generous vacation policy")}</span>
            </motion.div>

            <motion.div
              className="flex items-center gap-4 bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="w-12 h-12 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-lg flex items-center justify-center">
                <GlobeIcon className="h-6 w-6 text-white" />
              </div>
              <span className="font-medium text-gray-800">{t("careers.benefits.flexible", "Flexible working arrangements")}</span>
            </motion.div>

            <motion.div
              className="flex items-center gap-4 bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <div className="w-12 h-12 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-lg flex items-center justify-center">
                <TrendingUpIcon className="h-6 w-6 text-white" />
              </div>
              <span className="font-medium text-gray-800">{t("careers.benefits.training", "Professional development opportunities")}</span>
            </motion.div>

            <motion.div
              className="flex items-center gap-4 bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="w-12 h-12 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-lg flex items-center justify-center">
                <GlobeIcon className="h-6 w-6 text-white" />
              </div>
              <span className="font-medium text-gray-800">{t("careers.benefits.international", "International project exposure")}</span>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Open Positions Section */}
      <section className="py-24 bg-white" id="positions">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl md:text-4xl font-inter font-semibold mb-6">
              <span className="text-[#1a1a1a]">{t("careers.openPositions", "Open Positions")}</span>
            </h2>
          </motion.div>

          {isLoading ? (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D7EB6]"></div>
              <p className="mt-4 text-gray-600">{t("careers.loading", "Loading job positions...")}</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600">{t("careers.error", "Error loading job positions. Please try again later.")}</p>
            </div>
          ) : jobPositions.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {jobPositions.map((job, index) => (
                <motion.div
                  key={job.id}
                  className="bg-white border border-gray-200 rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:translate-y-[-5px]"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <div className="mb-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] rounded-lg flex items-center justify-center mb-4">
                      <BriefcaseIcon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-inter font-semibold mb-2 text-[#1a1a1a]">
                      {job.title}
                    </h3>
                    <div className="flex flex-wrap gap-2 mb-4">
                      <span className="inline-flex items-center gap-1 px-3 py-1 bg-[#2D7EB6]/10 text-[#2D7EB6] rounded-full text-sm font-medium">
                        <BuildingIcon className="h-3 w-3" />
                        {job.department}
                      </span>
                      <span className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                        <MapPinIcon className="h-3 w-3" />
                        {job.location}
                      </span>
                      <span className="inline-flex items-center gap-1 px-3 py-1 bg-[#40BFB9]/10 text-[#40BFB9] rounded-full text-sm font-medium">
                        <ClockIcon className="h-3 w-3" />
                        {job.type}
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 font-roboto leading-relaxed mb-6 line-clamp-3">
                    {job.description}
                  </p>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Key Responsibilities:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {job.responsibilities.slice(0, 3).map((responsibility, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <CheckCircleIcon className="h-4 w-4 text-[#40BFB9] mt-0.5 flex-shrink-0" />
                            <span>{responsibility}</span>
                          </li>
                        ))}
                        {job.responsibilities.length > 3 && (
                          <li className="text-[#2D7EB6] text-sm font-medium">
                            +{job.responsibilities.length - 3} more...
                          </li>
                        )}
                      </ul>
                    </div>

                    <div className="pt-4 border-t border-gray-100">
                      <Button
                        onClick={() => openApplicationModal(job)}
                        className="w-full bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] text-white font-medium py-3 rounded-lg hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                      >
                        <span>{t("careers.applyNow", "Apply Now")}</span>
                        <ArrowRightIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <motion.div
              className="text-center py-16"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <BriefcaseIcon className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {t("careers.noPositions", "No open positions at the moment")}
              </h3>
              <p className="text-gray-600 mb-8">
                {t("careers.checkBackSoon", "Please check back soon for new opportunities")}
              </p>
              <Link to="/contact">
                <Button variant="outline" className="border-[#2D7EB6] text-[#2D7EB6] hover:bg-[#2D7EB6] hover:text-white transition-all duration-300">
                  {t("contact.title", "Contact Us")}
                </Button>
              </Link>
            </motion.div>
          )}
        </div>
      </section>



      {/* Application Modal */}
      {isApplicationModalOpen && selectedJob && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <motion.div
            className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-2xl font-inter font-semibold text-[#1a1a1a]">
                    {t("careers.applicationForm.title", "Apply for Position")}
                  </h3>
                  <p className="text-gray-600 mt-1">
                    {selectedJob.title} - {selectedJob.department}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  onClick={() => setIsApplicationModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </Button>
              </div>

              {submitStatus === "success" ? (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <h4 className="text-xl font-semibold text-green-600 mb-2">
                    {t("careers.applicationForm.success", "Application submitted successfully!")}
                  </h4>
                  <p className="text-gray-600">
                    {t("careers.applicationForm.successMessage", "Thank you for your interest in MetaNord. We'll review your application and get back to you soon.")}
                  </p>
                </div>
              ) : (
                <form onSubmit={handleApplicationSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-sm font-medium text-gray-700 mb-2 block">
                        {t("careers.applicationForm.firstName", "First Name")} *
                      </Label>
                      <Input
                        id="firstName"
                        type="text"
                        required
                        value={applicationForm.firstName}
                        onChange={(e) => setApplicationForm(prev => ({ ...prev, firstName: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2D7EB6] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-sm font-medium text-gray-700 mb-2 block">
                        {t("careers.applicationForm.lastName", "Last Name")} *
                      </Label>
                      <Input
                        id="lastName"
                        type="text"
                        required
                        value={applicationForm.lastName}
                        onChange={(e) => setApplicationForm(prev => ({ ...prev, lastName: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2D7EB6] focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="email" className="text-sm font-medium text-gray-700 mb-2 block">
                        {t("careers.applicationForm.email", "Email Address")} *
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        required
                        value={applicationForm.email}
                        onChange={(e) => setApplicationForm(prev => ({ ...prev, email: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2D7EB6] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone" className="text-sm font-medium text-gray-700 mb-2 block">
                        {t("careers.applicationForm.phone", "Phone Number")}
                      </Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={applicationForm.phone}
                        onChange={(e) => setApplicationForm(prev => ({ ...prev, phone: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2D7EB6] focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="coverLetter" className="text-sm font-medium text-gray-700 mb-2 block">
                      {t("careers.applicationForm.coverLetter", "Cover Letter")} *
                    </Label>
                    <Textarea
                      id="coverLetter"
                      required
                      rows={6}
                      value={applicationForm.coverLetter}
                      onChange={(e) => setApplicationForm(prev => ({ ...prev, coverLetter: e.target.value }))}
                      placeholder={t("careers.applicationForm.coverLetterPlaceholder", "Tell us why you're interested in this position and what you can bring to MetaNord...")}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2D7EB6] focus:border-transparent resize-none"
                    />
                  </div>

                  <div>
                    <Label htmlFor="resume" className="text-sm font-medium text-gray-700 mb-2 block">
                      {t("careers.applicationForm.resume", "Resume/CV")} *
                    </Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-[#2D7EB6] transition-colors duration-300">
                      <FileTextIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600 mb-2">
                        {t("careers.applicationForm.resumeUpload", "Upload Resume")}
                      </p>
                      <Input
                        id="resume"
                        type="file"
                        accept=".pdf,.doc,.docx"
                        required
                        onChange={(e) => setApplicationForm(prev => ({ ...prev, resume: e.target.files?.[0] || null }))}
                        className="hidden"
                      />
                      <Label htmlFor="resume" className="cursor-pointer text-[#2D7EB6] hover:text-[#40BFB9] font-medium">
                        Choose File
                      </Label>
                      {applicationForm.resume && (
                        <p className="text-sm text-gray-600 mt-2">
                          Selected: {applicationForm.resume.name}
                        </p>
                      )}
                    </div>
                  </div>

                  {submitStatus === "error" && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <p className="text-red-600 text-sm">
                        {t("careers.applicationForm.error", "Failed to submit application")}. {t("careers.applicationForm.errorMessage", "Please try again or contact us <NAME_EMAIL>")}
                      </p>
                    </div>
                  )}

                  <div className="flex gap-4 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsApplicationModalOpen(false)}
                      className="flex-1 border-[#2D7EB6] text-[#2D7EB6] hover:bg-[#2D7EB6] hover:text-white transition-all duration-300"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1 bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] text-white font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          <span>{t("careers.applicationForm.submitting", "Submitting...")}</span>
                        </>
                      ) : (
                        <>
                          <SendIcon className="h-4 w-4" />
                          <span>{t("careers.applicationForm.submitApplication", "Submit Application")}</span>
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </motion.div>
        </div>
      )}

      {/* Contact Us Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl md:text-4xl font-inter font-semibold mb-6 text-[#1a1a1a]">
              {t("contact.title", "Contact Us")}
            </h2>
            <p className="text-lg text-gray-600 font-roboto leading-relaxed max-w-3xl mx-auto">
              Have questions about our career opportunities? Ready to take the next step in your professional journey? Get in touch with our team.
            </p>
          </motion.div>

          {/* Two-column layout: Contact Form + Contact Information */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-10 items-start"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Left Column: Contact Form */}
            <div>
              <h3 className="text-2xl font-bold mb-2 text-black">
                {t("contact.writeUs.title", "Write Us")}
              </h3>
              <p className="text-sm text-gray-600 mb-6">
                {t(
                  "contact.writeUs.subtitle",
                  "Fill out the form below and our team will get back to you as soon as possible."
                )}
              </p>
              <ContactForm labelColor="#40BFB9" />
            </div>

            {/* Right Column: Contact Information */}
            <div>
              <h3 className="text-2xl font-bold mb-4 text-black">
                {t("contact.info.title", "Contact Information")}
              </h3>
              <ul className="space-y-4 mb-6">
                {/* Company */}
                <li className="flex items-start gap-3">
                  <div className="bg-[#2D7EB6] p-2 rounded-full">
                    <Briefcase className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-base text-[#40BFB9]">
                      {t("contact.headers.company", "Company")}
                    </div>
                    <div className="text-sm font-medium text-black">
                      {t("contact.info.company", "MetaNord OÜ")}
                    </div>
                  </div>
                </li>

                {/* Registry */}
                <li className="flex items-start gap-3">
                  <div className="bg-[#2D7EB6] p-2 rounded-full">
                    <Clipboard className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-base text-[#40BFB9]">
                      {t("contact.headers.registry", "Registry Code")}
                    </div>
                    <div className="text-sm font-medium text-black">
                      {t("contact.info.registry", "17235227")}
                    </div>
                  </div>
                </li>

                {/* Address */}
                <li className="flex items-start gap-3">
                  <div className="bg-[#2D7EB6] p-2 rounded-full">
                    <MapPin className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-base text-[#40BFB9]">
                      {t("contact.headers.address", "Address")}
                    </div>
                    <a
                      href="https://goo.gl/maps/YJ4SytAoGHbQXauy8"
                      className="text-sm font-medium text-black underline hover:text-[#2D7EB6] transition"
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ wordBreak: "break-word" }}
                    >
                      {t("contact.info.address", "Tornimäe tn 5, 10145 Tallinn, Estonia")}
                    </a>
                  </div>
                </li>

                {/* Phone */}
                <li className="flex items-start gap-3">
                  <div className="bg-[#2D7EB6] p-2 rounded-full">
                    <Phone className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-base text-[#40BFB9]">
                      {t("contact.headers.phone", "Phone")}
                    </div>
                    <a
                      href="tel:+37255589800"
                      className="text-sm font-medium text-black underline hover:text-[#2D7EB6] transition"
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ wordBreak: "break-word" }}
                    >
                      {t("contact.info.phone", "+372 55589800")}
                    </a>
                  </div>
                </li>

                {/* Email */}
                <li className="flex items-start gap-3">
                  <div className="bg-[#2D7EB6] p-2 rounded-full">
                    <Mail className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-base text-[#40BFB9]">
                      {t("contact.headers.email", "Email")}
                    </div>
                    <a
                      href="mailto:<EMAIL>"
                      className="text-sm font-medium text-black underline hover:text-[#2D7EB6] transition"
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ wordBreak: "break-word" }}
                    >
                      {t("contact.info.email", "<EMAIL>")}
                    </a>
                  </div>
                </li>

                {/* Website */}
                <li className="flex items-start gap-3">
                  <div className="bg-[#2D7EB6] p-2 rounded-full">
                    <Globe className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-base text-[#40BFB9]">
                      {t("contact.headers.website", "Website")}
                    </div>
                    <a
                      href="https://www.metanord.eu"
                      className="text-sm font-medium text-black underline hover:text-[#2D7EB6] transition"
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ wordBreak: "break-word" }}
                    >
                      {t("contact.info.website", "www.metanord.eu")}
                    </a>
                  </div>
                </li>
              </ul>

              {/* Google Map */}
              <div className="relative rounded-xl overflow-hidden h-56 w-full mt-4 shadow border border-gray-200">
                <iframe
                  src="https://maps.google.com/maps?q=Tornimäe%205,%20Tallinn,%2010145,%20Estonia&t=&z=16&ie=UTF8&iwloc=&output=embed"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="MetaNord Office Location - Tornimäe 5, Tallinn, 10145, Estonia"
                />
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </CareersErrorBoundary>
  );
}
