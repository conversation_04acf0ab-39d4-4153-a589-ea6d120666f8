import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'wouter';
import { ProductCard } from '../components/ui/product-card';
import { translateProductCategory } from '../i18n';
import { Badge } from '../components/ui/badge';
import { Separator } from '../components/ui/separator';
import { SearchBar } from '../components/ui/search-bar';
import { cn } from '../lib/utils';
import { useAllProducts } from '../hooks/use-product-api';
import MetaTags from '../components/seo/MetaTags';
import { useLanguage } from '../hooks/use-language';

export default function SearchResults() {
  const { t, i18n } = useTranslation();
  const { language } = useLanguage();
  const [location] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCategory, setFilteredCategory] = useState<string | null>(null);

  // Загружаем все продукты через useAllProducts (всегда API)
  const { data: allProducts = [], isLoading, isError } = useAllProducts();

  // Получаем query из адреса (q=)
  useEffect(() => {
    const url = new URL(window.location.href);
    const query = url.searchParams.get('q') || '';
    setSearchQuery(query);
  }, [location]);

  // Категории в поисковой выдаче
  const categories = React.useMemo(() => {
    const unique = Array.from(new Set(
      allProducts
        .filter(product =>
          searchQuery
            ? (
                (product.title?.toLowerCase().includes(searchQuery.toLowerCase())) ||
                (product.description?.toLowerCase().includes(searchQuery.toLowerCase())) ||
                (product.category?.toLowerCase().includes(searchQuery.toLowerCase())) ||
                (product.features?.some(f => f.toLowerCase().includes(searchQuery.toLowerCase()))) ||
                (product.applications?.some(a => a.toLowerCase().includes(searchQuery.toLowerCase()))) ||
                (product.specifications && Object.values(product.specifications).some(
                  v => v.toString().toLowerCase().includes(searchQuery.toLowerCase())
                ))
              )
            : true
        )
        .map(product => product.category)
    ));
    return unique;
  }, [allProducts, searchQuery]);

  // Фильтруем результаты по поиску и категории
  const searchResults = React.useMemo(() => {
    if (!searchQuery.trim()) return [];
    const lowerQuery = searchQuery.toLowerCase();

    let filtered = allProducts.filter(product => {
      const titleMatch = product.title?.toLowerCase().includes(lowerQuery);
      const descriptionMatch = product.description?.toLowerCase().includes(lowerQuery);
      const categoryMatch = product.category?.toLowerCase().includes(lowerQuery);

      const featuresMatch = product.features?.some(
        feature => feature.toLowerCase().includes(lowerQuery)
      ) || false;

      const applicationsMatch = product.applications?.some(
        application => application.toLowerCase().includes(lowerQuery)
      ) || false;

      let specificationsMatch = false;
      if (product.specifications) {
        specificationsMatch = Object.values(product.specifications).some(
          value => value.toString().toLowerCase().includes(lowerQuery)
        );
      }

      return titleMatch || descriptionMatch || categoryMatch ||
        featuresMatch || applicationsMatch || specificationsMatch;
    });

    if (filteredCategory) {
      filtered = filtered.filter(product => product.category === filteredCategory);
    }

    return filtered;
  }, [searchQuery, allProducts, filteredCategory]);

  const handleCategoryFilter = (category: string) => {
    setFilteredCategory(prevCategory => prevCategory === category ? null : category);
  };

  const clearFilters = () => {
    setFilteredCategory(null);
  };

  // Generate meta tags for search results
  const baseUrl = 'https://metanord.eu';
  const searchMetaTitle = searchQuery
    ? t('search.metaTitle', 'Search Results for "{{query}}" - MetaNord', { query: searchQuery })
    : t('search.metaTitleDefault', 'Product Search - MetaNord');

  const searchMetaDescription = searchQuery
    ? t('search.metaDescription', 'Search results for "{{query}}" in MetaNord\'s catalog of aluminum profiles, infrastructure products, and industrial solutions. Found {{count}} matching products.', {
        query: searchQuery,
        count: searchResults.length
      })
    : t('search.metaDescriptionDefault', 'Search MetaNord\'s comprehensive catalog of aluminum profiles, polyethylene systems, steel products, and infrastructure solutions.');

  const languageAlternates = ['en', 'et', 'ru', 'lv', 'lt', 'pl'].map(lang => ({
    hrefLang: lang,
    href: `${baseUrl}${lang === 'en' ? '' : `/${lang}`}/search${searchQuery ? `?q=${encodeURIComponent(searchQuery)}` : ''}`
  }));

  return (
    <>
      <MetaTags
        title={searchMetaTitle}
        description={searchMetaDescription}
        keywords={`search, products, ${searchQuery}, aluminum profiles, infrastructure, MetaNord`}
        ogType="website"
        canonical={`${baseUrl}${language === 'en' ? '' : `/${language}`}/search${searchQuery ? `?q=${encodeURIComponent(searchQuery)}` : ''}`}
        ogUrl={`${baseUrl}${language === 'en' ? '' : `/${language}`}/search${searchQuery ? `?q=${encodeURIComponent(searchQuery)}` : ''}`}
        languageAlternates={languageAlternates}
        noIndex={!searchQuery} // Don't index empty search pages
      />

      <main className="pt-8 pb-20">
      <div className="container mx-auto px-6">
        <div className="mb-10">
          <h1 className="text-3xl md:text-4xl font-bold mb-3">
            {t('search.resultsFor', 'Search results for')} "{searchQuery}"
          </h1>

          <div className="mt-4 max-w-lg">
            <SearchBar
              value={searchQuery}
              onChange={v => setSearchQuery(v)}
              placeholder={t("search.refineSearch", "Refine your search...")}
            />
          </div>

          {categories.length > 0 && (
            <div className="mt-8 flex items-center flex-wrap gap-3">
              <span className="text-sm font-medium text-neutral-dark/80 mr-1">
                {t('search.filterByCategory', 'Filter by category:')}
              </span>
              {categories.map(category => (
                <Badge
                  key={category}
                  variant={filteredCategory === category ? "default" : "outline"}
                  className={cn(
                    "cursor-pointer hover:bg-primary/10 transition-colors",
                    filteredCategory === category
                      ? "bg-primary text-white"
                      : "text-neutral-dark"
                  )}
                  onClick={() => handleCategoryFilter(category)}
                >
                  {translateProductCategory(category)}
                </Badge>
              ))}
              {filteredCategory && (
                <Badge
                  variant="outline"
                  className="cursor-pointer border-dashed border-red-400 text-red-500 hover:bg-red-50"
                  onClick={clearFilters}
                >
                  {t('search.clearFilters', 'Clear filters')}
                </Badge>
              )}
            </div>
          )}
        </div>

        <Separator className="mb-10" />

        {/* Loading state */}
        {isLoading && (
          <div className="py-20 text-center text-lg font-medium text-primary">
            {t('search.loading', 'Loading products...')}
          </div>
        )}

        {/* Error state */}
        {isError && (
          <div className="py-20 text-center text-lg font-medium text-red-600">
            {t('search.error', 'Failed to load products. Please try again later.')}
          </div>
        )}

        {/* Search Results */}
        {searchQuery && !isLoading && !isError && (
          <div className="mb-4">
            <p className="text-lg text-neutral-dark/70">
              {t('search.foundResults', {
                count: searchResults.length,
                defaultValue: 'Found {{count}} result',
                defaultValue_plural: 'Found {{count}} results'
              })}
              {filteredCategory && (
                <span className="ml-1">
                  {t('search.inCategory', 'in category')} "{translateProductCategory(filteredCategory)}"
                </span>
              )}
            </p>
          </div>
        )}

        {!isLoading && !isError && (
          searchResults.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
              {searchResults.map(product => (
                <ProductCard
                  key={product.slug}
                  slug={product.slug}
                  title={product.title}
                  image={product.image}
                  category={product.category}
                  description={product.description}
                  link={`/products/${product.slug}`}
                  features={product.features}
                  applications={product.applications}
                  specifications={product.specifications}
                />
              ))}
            </div>
          ) : (
            <div className="py-20 text-center">
              <div className="max-w-md mx-auto">
                <h2 className="text-2xl font-semibold mb-4">
                  {t('search.noResultsFound', 'No products found matching your search')}
                </h2>
                <p className="text-neutral-dark/70 mb-8">
                  {t('search.tryDifferentTerms', 'Try searching with different terms or browse our product categories')}
                </p>
                <div className="flex justify-center">
                  <a href="/products" className="text-primary hover:text-accent font-medium">
                    {t('search.browseAllProducts', 'Browse all products')}
                  </a>
                </div>
              </div>
            </div>
          )
        )}
      </div>
    </main>
    </>
  );
}
