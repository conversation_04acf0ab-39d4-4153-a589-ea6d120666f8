/**
 * Cookie Policy Page
 * Comprehensive cookie policy page with MetaNord design system
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Cookie, Shield, BarChart3, Target, Settings } from 'lucide-react';
import MetaTags from '../components/seo/MetaTags';
import { useCookieConsent } from '../hooks/use-cookie-consent';
import { useLanguage } from '../hooks/use-language';

export default function CookiePolicy() {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const { openSettings } = useCookieConsent();

  // Get current date for last updated
  const lastUpdated = new Date().toLocaleDateString(language === 'en' ? 'en-US' : language, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <>
      <MetaTags
        title={t('cookies.policy.metaTitle', 'Cookie Policy - MetaNord')}
        description={t('cookies.policy.metaDescription', 'Learn about how MetaNord uses cookies, what data we collect, and how you can manage your cookie preferences.')}
        keywords="cookie policy, privacy, data protection, GDPR, MetaNord"
      />

      {/* Hero Section */}
      <section className="relative min-h-[220px] pt-10 pb-6 overflow-hidden">
        {/* Gradient Background */}
        <div 
          className="absolute inset-0 z-0"
          style={{
            background: 'linear-gradient(135deg, #2D7EB6 0%, #40BFB9 100%)',
          }}
        />

        {/* Wave Background */}
        <div className="absolute inset-0 z-10">
          <svg
            viewBox="0 0 1200 120"
            className="absolute bottom-0 w-full h-auto"
            preserveAspectRatio="none"
          >
            <path
              d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z"
              fill="white"
              opacity="0.1"
            />
          </svg>
        </div>

        <div className="container mx-auto px-4 relative z-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-left"
          >
            <div className="flex items-center gap-3 mb-4">
              <Cookie className="w-8 h-8 text-white" />
              <h1 className="text-3xl md:text-4xl font-bold text-white">
                {t('cookies.policy.title', 'Cookie Policy')}
              </h1>
            </div>
            <p className="text-white/90 text-lg max-w-2xl">
              {t('cookies.policy.lastUpdated', 'Last updated')}: {lastUpdated}
            </p>
          </motion.div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-8"
            >
              {/* Introduction */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-semibold text-gray-900 mb-4 flex items-center gap-3">
                  <Cookie className="w-6 h-6 text-[#2D7EB6]" />
                  {t('cookies.policy.sections.introduction.title', 'Introduction')}
                </h2>
                <p className="text-gray-600 leading-relaxed">
                  {t('cookies.policy.sections.introduction.content', 'This Cookie Policy explains how MetaNord OÜ uses cookies and similar technologies when you visit our website. It explains what these technologies are and why we use them, as well as your rights to control our use of them.')}
                </p>
              </div>

              {/* What are Cookies */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                  {t('cookies.policy.sections.whatAreCookies.title', 'What are Cookies?')}
                </h2>
                <p className="text-gray-600 leading-relaxed">
                  {t('cookies.policy.sections.whatAreCookies.content', 'Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners to make their websites work, or to work more efficiently, as well as to provide reporting information.')}
                </p>
              </div>

              {/* How We Use Cookies */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                  {t('cookies.policy.sections.howWeUseCookies.title', 'How We Use Cookies')}
                </h2>
                <p className="text-gray-600 leading-relaxed">
                  {t('cookies.policy.sections.howWeUseCookies.content', 'We use cookies for several reasons. Some cookies are required for technical reasons in order for our website to operate, and we refer to these as \'essential\' or \'strictly necessary\' cookies. Other cookies enable us to track and target the interests of our users to enhance the experience on our website.')}
                </p>
              </div>

              {/* Types of Cookies */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                  {t('cookies.policy.sections.cookieTypes.title', 'Types of Cookies We Use')}
                </h2>
                
                <div className="space-y-6">
                  {/* Essential Cookies */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <Shield className="w-5 h-5 text-green-600" />
                      <h3 className="text-lg font-semibold text-gray-900">
                        {t('cookies.policy.sections.cookieTypes.essential.title', 'Essential Cookies')}
                      </h3>
                    </div>
                    <p className="text-gray-600">
                      {t('cookies.policy.sections.cookieTypes.essential.description', 'These cookies are strictly necessary to provide you with services available through our website and to use some of its features, such as access to secure areas and language preferences.')}
                    </p>
                  </div>

                  {/* Analytics Cookies */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <BarChart3 className="w-5 h-5 text-[#2D7EB6]" />
                      <h3 className="text-lg font-semibold text-gray-900">
                        {t('cookies.policy.sections.cookieTypes.analytics.title', 'Analytics and Performance Cookies')}
                      </h3>
                    </div>
                    <p className="text-gray-600">
                      {t('cookies.policy.sections.cookieTypes.analytics.description', 'These cookies are used to collect information about traffic to our website and how users use our website. The information gathered does not identify any individual visitor.')}
                    </p>
                  </div>

                  {/* Marketing Cookies */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <Target className="w-5 h-5 text-[#40BFB9]" />
                      <h3 className="text-lg font-semibold text-gray-900">
                        {t('cookies.policy.sections.cookieTypes.marketing.title', 'Marketing and Targeting Cookies')}
                      </h3>
                    </div>
                    <p className="text-gray-600">
                      {t('cookies.policy.sections.cookieTypes.marketing.description', 'These cookies track your browsing habits to enable us to show advertising which is more likely to be of interest to you. These cookies use information about your browsing history to group you with other users who have similar interests.')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Managing Cookies */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-semibold text-gray-900 mb-4 flex items-center gap-3">
                  <Settings className="w-6 h-6 text-[#2D7EB6]" />
                  {t('cookies.policy.sections.managingCookies.title', 'Managing Your Cookie Preferences')}
                </h2>
                <p className="text-gray-600 leading-relaxed mb-4">
                  {t('cookies.policy.sections.managingCookies.content', 'You can change your cookie preferences at any time by clicking on the \'Cookie Settings\' link in our website footer. You can also set your browser to refuse all or some browser cookies, or to alert you when websites set or access cookies.')}
                </p>
                <button
                  onClick={openSettings}
                  className="inline-flex items-center gap-2 px-6 py-3 text-white font-semibold rounded-lg transition-all duration-300 hover:shadow-lg hover:scale-105"
                  style={{
                    background: 'linear-gradient(90deg, #2D7EB6 0%, #40BFB9 100%)',
                  }}
                >
                  <Settings className="w-4 h-4" />
                  Manage Cookie Preferences
                </button>
              </div>

              {/* Contact */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                  {t('cookies.policy.sections.contact.title', 'Contact Us')}
                </h2>
                <p className="text-gray-600 leading-relaxed">
                  {t('cookies.policy.sections.contact.content', 'If you have any questions about our use of cookies or other technologies, please email <NAME_EMAIL> or contact us through our website.')}
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
}
