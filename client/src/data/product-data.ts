// Product details data for use in both preview modals and product detail pages
// Use string literals for public folder images to fix build issues
const hdpePipes = '/images/products/HDPE pipes (PE pipes) .png';
const manholeD400 = '/images/products/Manhole cover D400(B) .png';
const rainwaterGrillD400 = '/images/products/Rainwater grill D400 1 .png';
const rainwaterGrillD4002 = '/images/products/Rainwater grill D400 2 .png';
const rainwaterGrillMeria = '/images/products/Rainwater grill D400 MERIA .png';
const rainwaterGrillF900 = '/images/products/Rainwater grill F900 .png';
const drainageGrateC250 = '/images/products/Drainage channel 5066 high-resistance spherical graphite cast iron, C250 EN 1433 .png';
const drainageGrateHighRes = '/images/products/Rainwater grill high-resistance spherical graphite cast iron, GGG50 E600 EN 1433.png';
const steelPipesOilGas = '/images/products/Steel Pipes For Oil and Gas Purpose .png';
const gateValve = '/images/products/Gate valve cast iron DN50-500, PN10-16 .png';
const groundFireHydrant = '/images/products/Ground fire hydrant .png';
const reducerSmall = '/images/products/Reducer 25-125 (Injection) .png';
const reducerMedium = '/images/products/Reducer 140-200 (Injection) .png';
const teeSmall = '/images/products/Reduced TEE 90°125-225 (Injection) .png';
const teeLarge = '/images/products/Reduced TEE 90° 250-400 (Injection) .png';
const wellFacility = '/images/products/Well facility .png';
const wasteBox = '/images/products/Waste box 30 l .png';
const weldedWireMesh = '/images/products/Welded wire mesh .jpg';
const wireMeshGate = '/images/products/Wire mesh gate .jpg';
const doubleCorrugatedPipes = '/images/products/Double corrugated pipes .png';
const steelReinforcedPipe = '/images/products/Steel reinforced corrugated polyethylene pipe.png';

// Aluminum profile images
const uProfiles = '/images/products/Aluminum U-Profiles.jpg';
const tProfiles = '/images/products/Aluminum T-Profiles.jpg';
const ledProfiles = '/images/products/Aluminum LED Profile.jpg';
const specialProfiles = '/images/products/Aluminum-Profile-Extrusion.jpg';

// Create compatibility variables for old references
const standardProfiles = uProfiles;
const machineProfiles = tProfiles;

// Log paths to debug
if (import.meta.env.DEV) console.log('Aluminum profile image paths:', {
  uProfiles,
  tProfiles,
  ledProfiles,
  specialProfiles
});
const elbow90 = '/images/products/Elbow 90° (Injection) fitting .png';
const equalTee = '/images/products/Equal TEE 90° (Injection) fitting .png';
const hsawPilesWater = '/images/products/Helical Submerged Arc Welded (HSAW) Pipes For Water Purpose.png';
const hsawPilesPurpose = '/images/products/Helical Submerged Arc Welded (HSAW) Pipes For Pile Purpose.png';
const elbow45 = '/images/products/Elbow 45° (Injection) fitting .png';
const flangeAdapterLong = '/images/products/Flange adapter-long type 20-355 (Injection).png';
const flangeAdapterShort = '/images/products/Flange adapter-Short type (Injection) .png';

export interface Document {
  id: number;
  title: string;
  description: string;
  fileUrl: string;
  fileSize?: string;
  fileType?: string;
  category?: string;
  language: string;
  createdAt?: Date;
}  

import { useAllProducts, ProductData } from '../hooks/use-product-api';

export async function ProductDetail({ productId, locale }: { productId: string; locale?: string }): Promise<ProductDetails> {
  const lang = locale || 'en';

  const { data: allProducts, isLoading, error } = useAllProducts();
  const product = allProducts?.find(
    (p) => p.id === productId || p.productId === productId
  );

  if (product) {
    return {
      ...product,
      documents: product.documents || [],
    } as ProductData;
  }

  const defaultProduct: ProductData = {
    id: productId,
    title: productId.replace(/-/g, ' ').replace(/\b\w/g, char => char.toUpperCase()),
    description: "Product information",
    image: "",
    category: "other",
    link: `/products/${productId}`,
    features: [],
    applications: [],
    specifications: {},
    status: "available",
    documents: []
  };

  try {
    const response = await fetch(`/api/products/${productId}?language=${lang}`);

    if (response.ok) {
      const apiProduct = await response.json();
      if (import.meta.env.DEV) console.log(`Found product ${productId} from API in ${lang} language`);

      const convertedProduct: ProductData = {
        id: productId,
        title: apiProduct.title || 'Unknown Product',
        description: apiProduct.description || '',
        image: apiProduct.image || '',
        category: apiProduct.category || 'uncategorized',
        features: apiProduct.features || [],
        applications: apiProduct.applications || [],
        specifications: apiProduct.specifications || {},
        status: apiProduct.status || 'unknown'
      };

      try {
        const documentsResponse = await fetch(`/api/products/${productId}/documents`);

        if (documentsResponse.ok) {
          const documents = await documentsResponse.json();
          if (documents && documents.length > 0) {
            convertedProduct.documents = documents.filter((doc: Document) =>
              !doc.language || doc.language === lang || doc.language === 'en'
            );
          }
        }
      } catch (docError) {
        console.error('Error fetching documents for product:', docError);
      }

      return convertedProduct;
    }
  } catch (error) {
    console.error('Error fetching product from API:', error);
  }

  if (import.meta.env.DEV) console.log(`No product details found for id: ${productId} in language: ${lang}, using default`);
  return defaultProduct;
};

// Helper function to sanitize specifications to ensure all values are strings
export const sanitizeSpecifications = (specs: Record<string, any> | undefined): Record<string, string> => {
  if (!specs) return {};

  try {
    const sanitizedSpecs: Record<string, string> = {};

    Object.entries(specs).forEach(([key, value]) => {
      sanitizedSpecs[key] = value != null ? String(value) : '';
    });

    return sanitizedSpecs;
  } catch (error) {
    console.error("Error sanitizing specifications:", error);
    return {};
  }
};
