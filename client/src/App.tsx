import { Switch, Route } from "wouter";
import { useEffect, Suspense, lazy } from "react";
import Header from "./components/layout/Header";
import Footer from "./components/layout/Footer";
import { PageWrapper } from "./components/layout/PageWrapper";
import NotFound from "./pages/not-found";
import { ScrollToTop } from "./components/ui/scroll-to-top";
import { Loader } from "./components/ui/loader";
import NordicPageTransition from "./components/transitions/NordicPageTransition";
import { ProtectedRoute } from "./components/admin/ProtectedRoute";
import { AuthProvider } from "./hooks/use-auth";
import { setupScrollPositionManagement, scrollToTop } from "./utils/scroll-manager";
import { ThemeProvider } from "./components/theme/ThemeProvider";
import { CookieConsentProvider } from "./contexts/CookieConsentContext";
import { CookieConsentBanner } from "./components/cookies/CookieConsentBanner";
import { CookieConsentModal } from "./components/cookies/CookieConsentModal";
import { initializeAnalyticsOnStartup } from "./utils/analytics-manager";
import ErrorBoundary from "./components/ui/error-boundary";
// Import transitions CSS for smooth language switching
import "./styles/transitions.css";
// TransitionProvider and context no longer needed - using fixed transition style
// TransitionSettings removed per client request

// Lazy-loaded page components for performance optimization
const Home = lazy(() => import("./pages/Home"));
const About = lazy(() => import("./pages/About"));
const Products = lazy(() => import("./pages/Products"));
const ProductDetail = lazy(() => import("./pages/ProductDetail"));
const Projects = lazy(() => import("./pages/Projects"));
const ProjectDetail = lazy(() => import("./pages/ProjectDetail"));
const Services = lazy(() => import("./pages/Services"));
const Contact = lazy(() => import("./pages/Contact"));
const Careers = lazy(() => import("./pages/Careers"));
const SearchResults = lazy(() => import("./pages/SearchResults"));
const Documents = lazy(() => import("./pages/Documents"));
const Faq = lazy(() => import("./pages/Faq"));
const PageBuilderTest = lazy(() => import("./pages/PageBuilderTest"));

// Legal pages
const PrivacyPolicy = lazy(() => import("./pages/PrivacyPolicy"));
const TermsOfService = lazy(() => import("./pages/TermsOfService"));
const ShippingInfo = lazy(() => import("./pages/ShippingInfo"));
const SanctionsCompliance = lazy(() => import("./pages/SanctionsCompliance"));
const CookiePolicy = lazy(() => import("./pages/CookiePolicy"));

// Admin pages
const AdminLogin = lazy(() => import("./pages/AdminLogin"));
const AdminDashboard = lazy(() => import("./pages/AdminDashboard"));
const UXDemo = lazy(() => import("./pages/UXDemoPage"));

// Loading fallback component
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-[60vh]">
    <Loader size="lg" variant="accent" text="Loading page..." />
  </div>
);

// The main App component with Nordic-inspired transitions
function AppContent() {
  // Always use the modern 'geometric' transition type instead of relying on settings
  const transitionType = 'geometric';
  
  // Force light mode on application load and initialize analytics
  useEffect(() => {
    // Force light mode regardless of system preferences
    document.documentElement.classList.remove('dark');
    document.documentElement.classList.add('light');
    document.documentElement.setAttribute('data-theme', 'light');

    // Also set in localStorage for persistence
    localStorage.setItem('metanord-theme', 'light');

    // Initialize analytics based on existing consent
    initializeAnalyticsOnStartup();
  }, []);
  
  // Set up scroll position management and handle smooth scroll to anchors
  useEffect(() => {
    // Setup global scroll position management
    const cleanupScrollManager = setupScrollPositionManagement();
    
    // Always scroll to top on component mount
    scrollToTop();
    
    // Function to handle anchor links
    const handleAnchorClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const anchorElement = target.closest('a');
      
      if (anchorElement && anchorElement.href && anchorElement.href.includes('#')) {
        const url = new URL(anchorElement.href);
        const hash = url.hash;

        // Check if we're on the same page as the anchor
        if (url.pathname === window.location.pathname || url.pathname === '/' + window.location.pathname) {
          event.preventDefault();
          
          // Find the element to scroll to
          const element = document.querySelector(hash);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
            // Update URL without refreshing the page
            window.history.pushState(null, '', hash);
          }
        }
      }
    };

    // Check for hash in URL on page load and scroll to it
    const handleHashOnLoad = () => {
      if (window.location.hash) {
        const element = document.querySelector(window.location.hash);
        if (element) {
          // Use a small timeout to ensure the page is fully loaded
          setTimeout(() => {
            element.scrollIntoView({ behavior: 'smooth' });
          }, 300);
        }
      } else {
        // If no hash in URL, ensure we're at the top of the page
        scrollToTop();
      }
    };

    // Add event listener for anchor clicks
    document.body.addEventListener('click', handleAnchorClick);
    // Handle hash in URL on initial load after a short delay to ensure full page load
    setTimeout(handleHashOnLoad, 150);

    // Clean up event listeners on component unmount
    return () => {
      document.body.removeEventListener('click', handleAnchorClick);
      cleanupScrollManager();
    };
  }, []);

  // Check if we're on an admin route
  const isAdminRoute = window.location.pathname.startsWith('/admin');

  return (
    <ErrorBoundary>
      <div className="flex flex-col min-h-screen">
        {/* Display Header only for non-admin routes */}
        {!isAdminRoute && (
          <ErrorBoundary>
            <Header />
          </ErrorBoundary>
        )}

        <main className={`${!isAdminRoute ? 'flex-grow pt-14 xs:pt-16 sm:pt-20 md:pt-24 lg:pt-28' : ''}`}>
          <NordicPageTransition transitionType={transitionType}>
            <Suspense fallback={<PageLoader />}>
              {/* Wrap the Switch with PageWrapper to manage the CTA section */}
              <PageWrapper>
                <ErrorBoundary>
                  <Switch>
                    {/* Public routes */}
                    <Route path="/" component={Home} />
                    <Route path="/about" component={About} />
                    <Route path="/products" component={Products} />

                    {/* Product detail route */}
                    <Route path="/products/:slug" component={ProductDetail} />
                    <Route path="/product" component={ProductDetail} />
                    <Route path="/search" component={SearchResults} />
                    <Route path="/services" component={Services} />
                    <Route path="/contact" component={Contact} />
                    <Route path="/careers" component={Careers} />
                    <Route path="/projects" component={Projects} />
                    <Route path="/projects/:id" component={ProjectDetail} />
                    <Route path="/documents" component={Documents} />
                    <Route path="/faq" component={Faq} />
                    <Route path="/page-builder-test" component={PageBuilderTest} />

                    {/* Legal pages */}
                    <Route path="/privacy-policy" component={PrivacyPolicy} />
                    <Route path="/terms-of-service" component={TermsOfService} />
                    <Route path="/shipping-info" component={ShippingInfo} />
                    <Route path="/sanctions-compliance" component={SanctionsCompliance} />
                    <Route path="/cookie-policy" component={CookiePolicy} />

                    {/* Admin routes */}
                    <Route path="/admin/login" component={AdminLogin} />
                    <ProtectedRoute path="/admin" component={AdminDashboard} />

                    {/* UX Demo route - available to anyone */}
                    <Route path="/ux-demo" component={UXDemo} />

                    {/* 404 route */}
                    <Route component={NotFound} />
                  </Switch>
                </ErrorBoundary>
              </PageWrapper>
            </Suspense>
          </NordicPageTransition>
        </main>

        {/* Display Footer only for non-admin routes */}
        {!isAdminRoute && (
          <ErrorBoundary>
            <Footer />
          </ErrorBoundary>
        )}
        {!isAdminRoute && <ScrollToTop />}
      </div>
    </ErrorBoundary>
  );
}

// Import UI components
import { Toaster } from "./components/ui/toaster";
import { TooltipProvider } from "./components/ui/tooltip";

// Import SEO components
import Sitemap, { OrganizationSchema } from "./components/seo/Sitemap";
import { MetaTags } from "./components/seo/GoogleTagManager";

// Removed debugging tools - no longer needed in production

// Root component - wraps AppContent with AuthProvider, ThemeProvider, and TooltipProvider
function App() {
  if (import.meta.env?.DEV) {
    console.log('App component rendering...');
  }

  try {
    return (
      <ThemeProvider>
        <TooltipProvider>
          <AuthProvider>
            <CookieConsentProvider>
              {/* SEO Components */}
              <Sitemap baseURL="https://metanord.eu" />
              <OrganizationSchema />
              <MetaTags
                title="MetaNord OÜ | Industrial Infrastructure Solutions"
                description="Estonian industrial infrastructure solutions company specializing in aluminum profiles, polyethylene pipes, and infrastructure components."
                keywords="aluminum profiles, polyethylene pipes, infrastructure components, industrial solutions, MetaNord"
              />

              <AppContent />
              <Toaster />

              {/* Cookie Consent Components */}
              <CookieConsentBanner />
              <CookieConsentModal />
            </CookieConsentProvider>
          </AuthProvider>
        </TooltipProvider>
      </ThemeProvider>
    );
  } catch (error) {
    console.error('Error in App component:', error);
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>MetaNord - Error</h1>
        <p>There was an error loading the application. Please refresh the page.</p>
        <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
          {error instanceof Error ? error.message : 'Unknown error'}
        </pre>
      </div>
    );
  }
}

export default App;
