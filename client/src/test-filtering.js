// Simple test to verify product filtering functionality
// This can be run in the browser console to test the fixes

console.log('🧪 Testing Product Filtering Fixes...');

// Test 1: Check if products have unique keys
function testUniqueKeys() {
  console.log('\n1. Testing unique keys...');
  
  // Get all product cards
  const productCards = document.querySelectorAll('[data-product-title]');
  const keys = new Set();
  let duplicateKeys = 0;
  
  productCards.forEach(card => {
    const key = card.closest('[data-key]')?.getAttribute('data-key') || 
                card.getAttribute('data-product-title');
    if (keys.has(key)) {
      duplicateKeys++;
      console.warn(`❌ Duplicate key found: ${key}`);
    } else {
      keys.add(key);
    }
  });
  
  if (duplicateKeys === 0) {
    console.log(`✅ All ${keys.size} product cards have unique keys`);
  } else {
    console.log(`❌ Found ${duplicateKeys} duplicate keys`);
  }
  
  return duplicateKeys === 0;
}

// Test 2: Check if filtering works
function testFiltering() {
  console.log('\n2. Testing category filtering...');
  
  // Get filter buttons
  const filterButtons = document.querySelectorAll('button[data-category], button[onclick*="category"]');
  console.log(`Found ${filterButtons.length} filter buttons`);
  
  // Test clicking a category filter
  const aluminumButton = Array.from(filterButtons).find(btn => 
    btn.textContent.toLowerCase().includes('aluminum') || 
    btn.textContent.toLowerCase().includes('aluminium')
  );
  
  if (aluminumButton) {
    console.log('✅ Found aluminum filter button');
    
    // Count products before filtering
    const productsBefore = document.querySelectorAll('[data-product-title]').length;
    console.log(`Products before filtering: ${productsBefore}`);
    
    // Click the filter (simulate)
    console.log('🔄 Simulating aluminum filter click...');
    // Note: In a real test, we would click the button and wait for the update
    
    return true;
  } else {
    console.log('❌ Could not find aluminum filter button');
    return false;
  }
}

// Test 3: Check if React keys are using productId
function testReactKeys() {
  console.log('\n3. Testing React keys...');
  
  // Check if we can find elements with productId-based keys
  const elementsWithKeys = document.querySelectorAll('[data-reactroot] *');
  let foundProductIdKeys = 0;
  
  // This is a simplified test - in reality, React keys are internal
  // We're checking for data attributes that might indicate proper keying
  const productElements = document.querySelectorAll('[data-product-title]');
  
  productElements.forEach(element => {
    const title = element.getAttribute('data-product-title');
    if (title && title.includes('-')) {
      foundProductIdKeys++;
    }
  });
  
  console.log(`✅ Found ${foundProductIdKeys} elements with product-like identifiers`);
  return true;
}

// Test 4: Check if search works
function testSearch() {
  console.log('\n4. Testing search functionality...');
  
  const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i]');
  
  if (searchInput) {
    console.log('✅ Found search input');
    
    // Test search functionality
    const originalValue = searchInput.value;
    searchInput.value = 'aluminum';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    setTimeout(() => {
      searchInput.value = originalValue;
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    }, 1000);
    
    return true;
  } else {
    console.log('❌ Could not find search input');
    return false;
  }
}

// Run all tests
function runAllTests() {
  console.log('🚀 Running all filtering tests...\n');
  
  const results = {
    uniqueKeys: testUniqueKeys(),
    filtering: testFiltering(),
    reactKeys: testReactKeys(),
    search: testSearch()
  };
  
  console.log('\n📊 Test Results:');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n${allPassed ? '🎉' : '⚠️'} Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  return results;
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
  // Wait for page to load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    setTimeout(runAllTests, 1000); // Give React time to render
  }
}

// Export for manual testing
window.testProductFiltering = runAllTests;
console.log('💡 Run window.testProductFiltering() to test manually');
