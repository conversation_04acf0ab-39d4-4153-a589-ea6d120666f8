// ProductsTable.tsx
import React from "react";
import type { ProductData } from "../hooks/use-product-api";

interface Props {
  products: ProductData[];
}

const ProductsTable: React.FC<Props> = ({ products }) => {
  return (
    <div className="overflow-x-auto border rounded-lg shadow-sm">
      <table className="min-w-full bg-white text-sm">
        <thead className="bg-gray-100 text-gray-700">
          <tr>
            <th className="px-4 py-3 text-left">Image</th>
            <th className="px-4 py-3 text-left">Title</th>
            <th className="px-4 py-3 text-left">Category</th>
            <th className="px-4 py-3 text-left">Language</th>
            <th className="px-4 py-3 text-left">Status</th>
          </tr>
        </thead>
        <tbody>
          {products.map((product) => (
            <tr
              key={`${product.productId}-${product.language}`}
              className="border-t"
            >
              <td className="px-4 py-2">
                <img
                  src={product.image || "/images/product-placeholder.jpg"}
                  alt={product.title}
                  className="w-12 h-12 object-cover rounded"
                />
              </td>
              <td className="px-4 py-2">{product.title}</td>
              <td className="px-4 py-2">{product.category}</td>
              <td className="px-4 py-2">{product.language}</td>
              <td className="px-4 py-2 capitalize">
                {product.status || "unknown"}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProductsTable;
