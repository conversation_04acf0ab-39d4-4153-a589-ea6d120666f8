// AdminProducts.tsx
import React from 'react';
import { useAllProducts } from '../hooks/use-product-api';
import ProductsTable from './ProductsTable';

const AdminProducts: React.FC = () => {
  const { data: products, isLoading, isError } = useAllProducts();

  if (isLoading) {
    return <div className="p-6 text-gray-600">Loading products...</div>;
  }

  if (isError || !products) {
    return <div className="p-6 text-red-600">Failed to load products.</div>;
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold mb-4">All Products ({products.length})</h1>
      <ProductsTable products={products} />
    </div>
  );
};

export default AdminProducts;
