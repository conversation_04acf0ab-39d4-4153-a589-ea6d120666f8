#!/usr/bin/env node

/**
 * Backend Deployment Script for MetaNord
 * Prepares and deploys the backend to Railway or other platforms
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 MetaNord Backend Deployment Script');
console.log('=====================================\n');

// Step 1: Check if Railway CLI is installed
console.log('1️⃣ Checking Railway CLI...');
try {
  execSync('railway --version', { stdio: 'pipe' });
  console.log('✅ Railway CLI is installed');
} catch (error) {
  console.log('❌ Railway CLI not found. Installing...');
  console.log('Please install Railway CLI first:');
  console.log('npm install -g @railway/cli');
  console.log('Then run: railway login');
  process.exit(1);
}

// Step 2: Create package.json for backend if it doesn't exist
console.log('\n2️⃣ Preparing backend package.json...');
const packageJsonPath = path.join(__dirname, 'package.json');
let packageJson;

if (fs.existsSync(packageJsonPath)) {
  packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  console.log('✅ Found existing package.json');
} else {
  console.log('⚠️  Creating new package.json for backend...');
  packageJson = {
    name: "metanord-backend",
    version: "1.0.0",
    description: "MetaNord Backend API Server",
    main: "backend-server.js",
    scripts: {
      start: "node backend-server.js",
      dev: "node backend-server.js"
    },
    dependencies: {
      express: "^4.18.2",
      cors: "^2.8.5",
      "cookie-parser": "^1.4.6"
    },
    engines: {
      node: ">=16.0.0"
    }
  };
}

// Ensure start script points to backend-server.js
packageJson.scripts = packageJson.scripts || {};
packageJson.scripts.start = "node backend-server.js";

fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log('✅ Package.json updated');

// Step 3: Create .env.example for Railway
console.log('\n3️⃣ Creating environment configuration...');
const envExample = `# MetaNord Backend Environment Variables
NODE_ENV=production
PORT=3001

# CORS Origins (comma-separated)
CORS_ORIGINS=https://metanord-frontend.vercel.app,https://metanord.eu

# Database (if using)
# DATABASE_URL=postgresql://...

# JWT Secret (if using)
# JWT_SECRET=your-secret-key

# Email Service (if using)
# EMAIL_SERVICE_API_KEY=your-email-key
`;

fs.writeFileSync('.env.example', envExample);
console.log('✅ Created .env.example');

// Step 4: Create Railway configuration
console.log('\n4️⃣ Creating Railway configuration...');
const railwayToml = `[build]
builder = "NIXPACKS"

[deploy]
startCommand = "npm start"
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[environments.production.variables]
NODE_ENV = "production"
`;

fs.writeFileSync('railway.toml', railwayToml);
console.log('✅ Created railway.toml');

// Step 5: Display deployment instructions
console.log('\n🎯 Deployment Instructions:');
console.log('===========================');
console.log('1. Login to Railway:');
console.log('   railway login');
console.log('');
console.log('2. Create new project:');
console.log('   railway init');
console.log('');
console.log('3. Set environment variables in Railway dashboard:');
console.log('   - NODE_ENV=production');
console.log('   - CORS_ORIGINS=https://metanord-frontend.vercel.app');
console.log('');
console.log('4. Deploy:');
console.log('   railway up');
console.log('');
console.log('5. Get your Railway URL and update frontend:');
console.log('   - Copy the Railway app URL (e.g., https://your-app.railway.app)');
console.log('   - Update VITE_API_URL in Vercel environment variables');
console.log('   - Redeploy frontend');
console.log('');
console.log('📋 Quick Commands:');
console.log('==================');
console.log('# Deploy to Railway');
console.log('railway login');
console.log('railway init');
console.log('railway up');
console.log('');
console.log('# Update frontend API URL');
console.log('vercel env add VITE_API_URL production');
console.log('# Enter your Railway URL when prompted');
console.log('vercel --prod');
console.log('');
console.log('✅ Backend is ready for deployment!');
