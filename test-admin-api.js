#!/usr/bin/env node

// Test script for MetaNord Admin API
const API_BASE = 'http://localhost:3001';

async function testAPI(method, endpoint, data = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    const result = await response.json();
    
    console.log(`✅ ${method} ${endpoint}:`, response.status);
    if (response.status >= 400) {
      console.log('   Error:', result);
    } else {
      console.log('   Success:', Array.isArray(result) ? `${result.length} items` : 'OK');
    }
    
    return { success: response.ok, data: result, status: response.status };
  } catch (error) {
    console.log(`❌ ${method} ${endpoint}:`, error.message);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🧪 Testing MetaNord Admin API\n');
  
  // Test authentication
  console.log('1. Testing Authentication:');
  await testAPI('GET', '/api/admin/me');
  await testAPI('POST', '/api/admin/login', { email: '<EMAIL>', password: 'admin' });
  
  console.log('\n2. Testing Products API:');
  await testAPI('GET', '/api/admin/products?language=en');
  await testAPI('POST', '/api/admin/products', {
    productId: 'test-product',
    title: 'Test Product',
    description: 'This is a test product',
    category: 'aluminum',
    status: 'in stock',
    language: 'en'
  });
  await testAPI('PATCH', '/api/admin/products/test-product', {
    title: 'Updated Test Product'
  });
  await testAPI('DELETE', '/api/admin/products/test-product');
  
  console.log('\n3. Testing Projects API:');
  await testAPI('GET', '/api/admin/projects?language=en');
  await testAPI('POST', '/api/admin/projects', {
    title: 'Test Project',
    language: 'en',
    slug: 'test-project',
    description: 'This is a test project',
    summary: 'Test project summary',
    location: 'Test Location',
    year: 2024,
    published: true,
    productTags: ['aluminum'],
    images: {
      main: '/images/test.jpg',
      gallery: []
    }
  });
  
  console.log('\n✅ All tests completed!');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testAPI, runTests };
