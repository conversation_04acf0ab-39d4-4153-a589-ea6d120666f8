<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MetaNord Admin Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { border-color: #4CAF50; background: #f8fff8; }
        .error { border-color: #f44336; background: #fff8f8; }
        .loading { border-color: #2196F3; background: #f8f8ff; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 MetaNord Admin Debug Panel</h1>
        <p>This page tests the admin API endpoints and authentication.</p>
        
        <div class="test-section" id="backend-test">
            <h3>1. Backend Connectivity</h3>
            <button onclick="testBackend()">Test Backend</button>
            <div id="backend-result"></div>
        </div>
        
        <div class="test-section" id="auth-test">
            <h3>2. Authentication</h3>
            <button onclick="testAuth()">Test Auth</button>
            <button onclick="testLogin()">Test Login</button>
            <div id="auth-result"></div>
        </div>
        
        <div class="test-section" id="dashboard-test">
            <h3>3. Dashboard API</h3>
            <button onclick="testDashboard()">Test Dashboard</button>
            <div id="dashboard-result"></div>
        </div>
        
        <div class="test-section" id="frontend-test">
            <h3>4. Frontend Access</h3>
            <button onclick="openAdmin()">Open Admin Dashboard</button>
            <button onclick="openAdminLogin()">Open Admin Login</button>
            <div id="frontend-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';
        
        function setResult(elementId, content, type = 'loading') {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = `test-section ${type}`;
        }
        
        async function testBackend() {
            setResult('backend-test', '<p>Testing backend connectivity...</p>', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/api/admin/me`, {
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                setResult('backend-test', `
                    <h3>1. Backend Connectivity</h3>
                    <button onclick="testBackend()">Test Backend</button>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Response:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `, response.ok ? 'success' : 'error');
                
            } catch (error) {
                setResult('backend-test', `
                    <h3>1. Backend Connectivity</h3>
                    <button onclick="testBackend()">Test Backend</button>
                    <p><strong>Error:</strong> ${error.message}</p>
                `, 'error');
            }
        }
        
        async function testAuth() {
            setResult('auth-test', '<p>Testing authentication...</p>', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/api/admin/me`, {
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                setResult('auth-test', `
                    <h3>2. Authentication</h3>
                    <button onclick="testAuth()">Test Auth</button>
                    <button onclick="testLogin()">Test Login</button>
                    <p><strong>Auth Status:</strong> ${response.status}</p>
                    <p><strong>User Data:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `, response.ok ? 'success' : 'error');
                
            } catch (error) {
                setResult('auth-test', `
                    <h3>2. Authentication</h3>
                    <button onclick="testAuth()">Test Auth</button>
                    <button onclick="testLogin()">Test Login</button>
                    <p><strong>Error:</strong> ${error.message}</p>
                `, 'error');
            }
        }
        
        async function testLogin() {
            setResult('auth-test', '<p>Testing login...</p>', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/api/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        username: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                setResult('auth-test', `
                    <h3>2. Authentication</h3>
                    <button onclick="testAuth()">Test Auth</button>
                    <button onclick="testLogin()">Test Login</button>
                    <p><strong>Login Status:</strong> ${response.status}</p>
                    <p><strong>Login Response:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `, response.ok ? 'success' : 'error');
                
            } catch (error) {
                setResult('auth-test', `
                    <h3>2. Authentication</h3>
                    <button onclick="testAuth()">Test Auth</button>
                    <button onclick="testLogin()">Test Login</button>
                    <p><strong>Error:</strong> ${error.message}</p>
                `, 'error');
            }
        }
        
        async function testDashboard() {
            setResult('dashboard-test', '<p>Testing dashboard API...</p>', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/api/admin/dashboard`, {
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                setResult('dashboard-test', `
                    <h3>3. Dashboard API</h3>
                    <button onclick="testDashboard()">Test Dashboard</button>
                    <p><strong>Dashboard Status:</strong> ${response.status}</p>
                    <p><strong>Dashboard Data:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `, response.ok ? 'success' : 'error');
                
            } catch (error) {
                setResult('dashboard-test', `
                    <h3>3. Dashboard API</h3>
                    <button onclick="testDashboard()">Test Dashboard</button>
                    <p><strong>Error:</strong> ${error.message}</p>
                `, 'error');
            }
        }
        
        function openAdmin() {
            window.open('http://localhost:5173/admin?tab=dashboard', '_blank');
        }
        
        function openAdminLogin() {
            window.open('http://localhost:5173/admin/login', '_blank');
        }
        
        // Auto-run tests on page load
        window.onload = function() {
            setTimeout(() => {
                testBackend();
                setTimeout(() => testAuth(), 1000);
                setTimeout(() => testDashboard(), 2000);
            }, 500);
        };
    </script>
</body>
</html>
