#!/usr/bin/env node

/**
 * Test script to verify the product edit functionality fix
 * This script tests the admin API endpoints and simulates the edit workflow
 */

const { default: fetch } = require('node-fetch');

const BASE_URL = 'http://localhost:3001';

async function testEditFunctionality() {
  console.log('🧪 Testing Product Edit Functionality Fix...\n');

  try {
    // 1. Test fetching all products
    console.log('1. Fetching all products...');
    const productsResponse = await fetch(`${BASE_URL}/api/admin/products?language=en`);
    
    if (!productsResponse.ok) {
      throw new Error(`Failed to fetch products: ${productsResponse.status}`);
    }
    
    const products = await productsResponse.json();
    console.log(`✅ Found ${products.length} products`);
    
    if (products.length === 0) {
      console.log('❌ No products found to test edit functionality');
      return;
    }

    // 2. Test fetching a specific product for editing
    const testProduct = products[0];
    console.log(`\n2. Testing edit data fetch for product: ${testProduct.productId}`);
    
    const productResponse = await fetch(`${BASE_URL}/api/admin/products/${testProduct.productId}?language=en`);
    
    if (!productResponse.ok) {
      throw new Error(`Failed to fetch product ${testProduct.productId}: ${productResponse.status}`);
    }
    
    const productData = await productResponse.json();
    console.log('✅ Product data fetched successfully');
    console.log(`   - Title: ${productData.title}`);
    console.log(`   - Description: ${productData.description?.substring(0, 50)}...`);
    console.log(`   - Category: ${productData.category}`);
    console.log(`   - Status: ${productData.status}`);
    console.log(`   - Features: ${productData.features?.length || 0} items`);
    console.log(`   - Applications: ${productData.applications?.length || 0} items`);
    console.log(`   - Specifications: ${Object.keys(productData.specifications || {}).length} items`);

    // 3. Test updating the product (simulate form submission)
    console.log(`\n3. Testing product update for: ${testProduct.productId}`);
    
    const formData = new FormData();
    formData.append('productId', productData.productId);
    formData.append('title', productData.title + ' (Test Edit)');
    formData.append('description', productData.description);
    formData.append('category', productData.category);
    formData.append('status', productData.status);
    formData.append('language', 'en');
    
    if (productData.features) {
      formData.append('features', JSON.stringify(productData.features));
    }
    
    if (productData.applications) {
      formData.append('applications', JSON.stringify(productData.applications));
    }
    
    if (productData.specifications) {
      formData.append('specifications', JSON.stringify(productData.specifications));
    }

    const updateResponse = await fetch(`${BASE_URL}/api/admin/products/${testProduct.productId}`, {
      method: 'PUT',
      body: formData
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      throw new Error(`Failed to update product: ${updateResponse.status} - ${errorText}`);
    }

    console.log('✅ Product update successful');

    // 4. Verify the update by fetching the product again
    console.log('\n4. Verifying update by re-fetching product...');
    
    const verifyResponse = await fetch(`${BASE_URL}/api/admin/products/${testProduct.productId}?language=en`);
    const verifiedData = await verifyResponse.json();
    
    if (verifiedData.title.includes('(Test Edit)')) {
      console.log('✅ Update verified - title was modified successfully');
    } else {
      console.log('❌ Update verification failed - title was not modified');
    }

    // 5. Restore original title
    console.log('\n5. Restoring original title...');
    
    const restoreFormData = new FormData();
    restoreFormData.append('productId', productData.productId);
    restoreFormData.append('title', productData.title); // Original title
    restoreFormData.append('description', productData.description);
    restoreFormData.append('category', productData.category);
    restoreFormData.append('status', productData.status);
    restoreFormData.append('language', 'en');
    
    if (productData.features) {
      restoreFormData.append('features', JSON.stringify(productData.features));
    }
    
    if (productData.applications) {
      restoreFormData.append('applications', JSON.stringify(productData.applications));
    }
    
    if (productData.specifications) {
      restoreFormData.append('specifications', JSON.stringify(productData.specifications));
    }

    const restoreResponse = await fetch(`${BASE_URL}/api/admin/products/${testProduct.productId}`, {
      method: 'PUT',
      body: restoreFormData
    });

    if (restoreResponse.ok) {
      console.log('✅ Original title restored');
    } else {
      console.log('⚠️  Failed to restore original title');
    }

    console.log('\n🎉 All tests passed! Product edit functionality is working correctly.');
    console.log('\n📋 Summary:');
    console.log('   ✅ Products list endpoint working');
    console.log('   ✅ Individual product fetch working');
    console.log('   ✅ Product update endpoint working');
    console.log('   ✅ Data persistence verified');
    console.log('\n🔧 The edit modal should now populate correctly with existing product data.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testEditFunctionality();
