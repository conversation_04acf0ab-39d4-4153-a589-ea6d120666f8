#!/usr/bin/env node

/**
 * Test script to verify the admin panel product editing functionality
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001';

async function testEditFunctionality() {
  console.log('🧪 Testing Admin Panel Product Edit Functionality\n');

  try {
    // Test 1: Fetch products list
    console.log('1. Testing products list endpoint...');
    const productsResponse = await fetch(`${BASE_URL}/api/admin/products?language=en`);
    if (!productsResponse.ok) {
      throw new Error(`Products list failed: ${productsResponse.status}`);
    }
    const products = await productsResponse.json();
    console.log(`✅ Found ${products.length} products`);

    if (products.length === 0) {
      console.log('❌ No products found to test with');
      return;
    }

    // Test 2: Test individual product fetch for editing
    const testProduct = products[0];
    console.log(`\n2. Testing product detail fetch for editing: ${testProduct.productId}`);
    
    const productResponse = await fetch(`${BASE_URL}/api/admin/products/${testProduct.productId}?language=en`);
    if (!productResponse.ok) {
      throw new Error(`Product detail failed: ${productResponse.status}`);
    }
    const productDetail = await productResponse.json();
    
    console.log('✅ Product detail fetched successfully');
    console.log(`   - Product ID: ${productDetail.productId}`);
    console.log(`   - Title: ${productDetail.title}`);
    console.log(`   - Category: ${productDetail.category}`);
    console.log(`   - Status: ${productDetail.status}`);
    console.log(`   - Features: ${productDetail.features ? productDetail.features.length : 0} items`);
    console.log(`   - Applications: ${productDetail.applications ? productDetail.applications.length : 0} items`);
    console.log(`   - Specifications: ${productDetail.specifications ? Object.keys(productDetail.specifications).length : 0} items`);

    // Test 3: Verify all required fields are present
    console.log('\n3. Verifying required fields for form population...');
    const requiredFields = ['productId', 'title', 'description', 'category', 'status'];
    const missingFields = requiredFields.filter(field => !productDetail[field]);
    
    if (missingFields.length > 0) {
      console.log(`❌ Missing required fields: ${missingFields.join(', ')}`);
    } else {
      console.log('✅ All required fields present');
    }

    // Test 4: Test documents endpoint
    console.log('\n4. Testing product documents endpoint...');
    const documentsResponse = await fetch(`${BASE_URL}/api/admin/products/${testProduct.productId}/documents`);
    if (!documentsResponse.ok) {
      throw new Error(`Documents fetch failed: ${documentsResponse.status}`);
    }
    const documents = await documentsResponse.json();
    console.log(`✅ Documents endpoint working (${documents.length} documents)`);

    // Test 5: Verify data structure for form compatibility
    console.log('\n5. Verifying data structure compatibility...');
    
    const expectedStructure = {
      productId: 'string',
      title: 'string', 
      description: 'string',
      category: 'string',
      status: 'string',
      features: 'array',
      applications: 'array',
      specifications: 'object'
    };

    let structureValid = true;
    for (const [field, expectedType] of Object.entries(expectedStructure)) {
      const actualValue = productDetail[field];
      const actualType = Array.isArray(actualValue) ? 'array' : typeof actualValue;
      
      if (actualType !== expectedType && actualValue !== undefined && actualValue !== null) {
        console.log(`❌ Field ${field}: expected ${expectedType}, got ${actualType}`);
        structureValid = false;
      }
    }

    if (structureValid) {
      console.log('✅ Data structure is compatible with form schema');
    }

    console.log('\n🎉 All tests passed! The edit functionality should work correctly.');
    console.log('\nTo test manually:');
    console.log('1. Open http://localhost:5173/admin/tab-products');
    console.log('2. Click the "Edit" button on any product');
    console.log('3. Verify that the form is populated with the product data');
    console.log('4. Check that features, applications, and specifications are displayed');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testEditFunctionality().catch(console.error);
}

module.exports = { testEditFunctionality };
