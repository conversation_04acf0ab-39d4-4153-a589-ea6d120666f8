require('dotenv').config()
const fs = require('fs')
const OpenAI = require('openai')

const OPENAI_API_KEY = process.env.OPENAI_API_KEY
if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is required')
}

const openai = new OpenAI({ apiKey: OPENAI_API_KEY })

// --- Рекурсивная синхронизация ключей ---
function syncKeysRecursive(source, target, addedKeys = [], prefix = '') {
  for (const key in source) {
    const fullKey = prefix ? `${prefix}.${key}` : key
    if (
      typeof source[key] === 'object' &&
      source[key] !== null &&
      !Array.isArray(source[key])
    ) {
      if (!(key in target)) {
        target[key] = {}
        addedKeys.push(fullKey)
      }
      syncKeysRecursive(source[key], target[key], addedKeys, fullKey)
    } else {
      if (!(key in target)) {
        target[key] = ''
        addedKeys.push(fullKey)
      }
    }
  }
  return addedKeys
}

// --- Рекурсивный сбор всех пустых ключей ---
function collectEmptyKeysRecursive(obj, path = '', emptyKeys = []) {
  for (const key in obj) {
    const currentPath = path ? `${path}.${key}` : key
    if (
      typeof obj[key] === 'object' &&
      obj[key] !== null &&
      !Array.isArray(obj[key])
    ) {
      collectEmptyKeysRecursive(obj[key], currentPath, emptyKeys)
    } else if (obj[key] === '') {
      emptyKeys.push(currentPath)
    }
  }
  return emptyKeys
}

// --- Рекурсивное создание объекта только с пустыми ключами ---
function extractEntriesByKeys(obj, keys) {
  const result = {}
  for (const keyPath of keys) {
    const parts = keyPath.split('.')
    let src = obj, tgt = result
    for (let i = 0; i < parts.length; i++) {
      if (!src || typeof src !== 'object') break
      if (i === parts.length - 1) {
        tgt[parts[i]] = src[parts[i]]
      } else {
        if (!tgt[parts[i]]) tgt[parts[i]] = {}
        src = src[parts[i]]
        tgt = tgt[parts[i]]
      }
    }
  }
  return result
}

// --- Рекурсивное слияние переводов в локализацию ---
function deepMerge(target, source) {
  for (const key in source) {
    if (
      typeof source[key] === 'object' &&
      source[key] !== null &&
      !Array.isArray(source[key])
    ) {
      if (!target[key]) target[key] = {}
      deepMerge(target[key], source[key])
    } else {
      target[key] = source[key]
    }
  }
}

// --- Разделение массива на батчи ---
function chunkArray(array, chunkSize) {
  const results = []
  for (let i = 0; i < array.length; i += chunkSize) {
    results.push(array.slice(i, i + chunkSize))
  }
  return results
}

// Основная функция
async function updateLanguageFile(english, fileName) {
  const filePath = `./client/src/locales/${fileName}.json`
  let langJson = {}

  if (fs.existsSync(filePath)) {
    langJson = JSON.parse(fs.readFileSync(filePath, 'utf-8'))
  }

  let addedKeys = syncKeysRecursive(english, langJson)
  if (addedKeys.length > 0) {
    console.log(`Добавлены новые ключи в ${fileName}: ${addedKeys.join(', ')}`)
    fs.writeFileSync(filePath, JSON.stringify(langJson, null, 2))
  }

  // 2. Собираем все пустые ключи для перевода (рекурсивно)
  const emptyKeys = collectEmptyKeysRecursive(langJson)
  const batchSize = 30 // Можно менять, если возникают ошибки — уменьши до 20

  if (emptyKeys.length > 0) {
    console.warn(`\n${fileName}.json: Найдено ${emptyKeys.length} пустых ключей, запускаю перевод по ${batchSize} ключей...\n`)
    const batches = chunkArray(emptyKeys, batchSize)
    for (const batch of batches) {
      const englishEntries = extractEntriesByKeys(english, batch)
      try {
        const translatedEntries = await completeTranslation(englishEntries, fileName)
        let translated
        try {
          translated = JSON.parse(translatedEntries)
        } catch (e) {
          console.error('Could not parse translated JSON:', translatedEntries)
          continue // Если ошибка — пропускаем батч
        }
        // Аудит перевода — показываем все новые переводы
        function printAudit(obj, tr, prefix = '') {
          for (const key in tr) {
            const fullKey = prefix ? `${prefix}.${key}` : key
            if (
              typeof tr[key] === 'object' &&
              tr[key] !== null &&
              !Array.isArray(tr[key])
            ) {
              printAudit(obj[key], tr[key], fullKey)
            } else {
              console.log(`[${fullKey}]:\n  EN: ${obj[key]}\n  ${fileName}: ${tr[key]}\n`)
            }
          }
        }
        console.log(`--- Аудит перевода для ${fileName} ---`)
        printAudit(englishEntries, translated)
        // Вставляем переведённые строки в langJson
        deepMerge(langJson, translated)
        fs.writeFileSync(filePath, JSON.stringify(langJson, null, 2))
      } catch (translationError) {
        console.error(`Error translating:  ${translationError.message}`)
      }
    }
  } else {
    console.warn(`\n${fileName}.json: Нет пустых ключей для перевода.`)
  }

  // 3. Аудит после перевода: какие ключи остались пустыми (контроль)
  const stillEmpty = collectEmptyKeysRecursive(langJson)
  if (stillEmpty.length > 0) {
    console.warn(`ВНИМАНИЕ! В ${fileName} остались непереведённые ключи:\n${stillEmpty.join('\n')}\n`)
  } else {
    console.log(`✅ В ${fileName} все строки переведены!`)
  }
}

// Функция перевода через OpenAI
const completeTranslation = async (messageObject, lang) => {
  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `
            You will be provided with a JSON object in English language.
            Your task is to only translate the values into the requested language,
            and return the result as valid JSON.
            Return ONLY valid JSON. Do not include any explanations or comments.
          `,
        },
        {
          role: 'user',
          content: `Translate the following JSON object to '${lang}' language: ${JSON.stringify(messageObject)}`,
        },
      ],
      temperature: 1,
      max_tokens: 1024,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
    })

    return completion.choices[0].message.content
  } catch (error) {
    throw new Error(`Error completing translation: ${error.message}`)
  }
}

// Главный запуск
;(async () => {
  try {
    console.info(`\n=== Проверка и автоперевод всех языков ===\n`)
    const english = JSON.parse(fs.readFileSync('./client/src/locales/en.json', 'utf-8'))
    const languages = ['et', 'ru', 'lv', 'lt', 'pl', 'zh-CN']
    for (const lang of languages) {
      await updateLanguageFile(english, lang)
    }
    console.log('\n--- Аудит завершён ---\n')
  } catch (error) {
    console.error('Error reading JSON files:', error.message)
  }
})()
