#!/usr/bin/env node

/**
 * Production API Test Script for MetaNord Careers
 * Tests the production API endpoints to identify issues
 */

const https = require('https');
const http = require('http');

const API_BASE_URL = process.env.API_URL || 'https://api.metanord.eu';
const FRONTEND_URL = process.env.FRONTEND_URL || 'https://metanord-frontend.vercel.app';

console.log('🧪 Testing MetaNord Production API...');
console.log(`API Base URL: ${API_BASE_URL}`);
console.log(`Frontend URL: ${FRONTEND_URL}\n`);

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const lib = url.startsWith('https:') ? https : http;
    
    const req = lib.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          statusMessage: res.statusMessage,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testEndpoint(name, url, options = {}) {
  console.log(`\n🔍 Testing ${name}...`);
  console.log(`URL: ${url}`);
  
  try {
    const response = await makeRequest(url, options);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      console.log(`✅ ${name}: SUCCESS (${response.statusCode})`);
      
      // Try to parse JSON response
      try {
        const jsonData = JSON.parse(response.data);
        if (Array.isArray(jsonData)) {
          console.log(`   📊 Data: ${jsonData.length} items`);
          if (jsonData.length > 0 && jsonData[0].title) {
            console.log(`   📝 Sample: ${jsonData[0].title}`);
          }
        } else {
          console.log(`   📊 Data: ${Object.keys(jsonData).join(', ')}`);
        }
      } catch (e) {
        console.log(`   📊 Data: ${response.data.length} bytes (non-JSON)`);
      }
    } else {
      console.log(`❌ ${name}: FAILED (${response.statusCode} ${response.statusMessage})`);
      console.log(`   Error: ${response.data}`);
    }
    
    // Check important headers
    const corsOrigin = response.headers['access-control-allow-origin'];
    const corsCredentials = response.headers['access-control-allow-credentials'];
    
    if (corsOrigin) {
      console.log(`   🌐 CORS Origin: ${corsOrigin}`);
    } else {
      console.log(`   ⚠️  CORS Origin: Not set`);
    }
    
    if (corsCredentials) {
      console.log(`   🔐 CORS Credentials: ${corsCredentials}`);
    }
    
    return response;
    
  } catch (error) {
    console.log(`❌ ${name}: ERROR`);
    console.log(`   Error: ${error.message}`);
    return null;
  }
}

async function testCorsPreflightRequest(url) {
  console.log(`\n🔍 Testing CORS Preflight...`);
  
  const options = {
    method: 'OPTIONS',
    headers: {
      'Origin': FRONTEND_URL,
      'Access-Control-Request-Method': 'GET',
      'Access-Control-Request-Headers': 'Content-Type'
    }
  };
  
  try {
    const response = await makeRequest(url, options);
    
    console.log(`Status: ${response.statusCode}`);
    console.log(`CORS Headers:`);
    console.log(`  - Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || 'Not set'}`);
    console.log(`  - Access-Control-Allow-Methods: ${response.headers['access-control-allow-methods'] || 'Not set'}`);
    console.log(`  - Access-Control-Allow-Headers: ${response.headers['access-control-allow-headers'] || 'Not set'}`);
    console.log(`  - Access-Control-Allow-Credentials: ${response.headers['access-control-allow-credentials'] || 'Not set'}`);
    
    return response;
  } catch (error) {
    console.log(`❌ CORS Preflight failed: ${error.message}`);
    return null;
  }
}

async function runTests() {
  console.log('Starting API tests...\n');
  
  // Test 1: Basic connectivity
  await testEndpoint('Basic Connectivity', `${API_BASE_URL}/api/careers`);
  
  // Test 2: With language parameter
  await testEndpoint('Careers with Language', `${API_BASE_URL}/api/careers?language=en`);
  
  // Test 3: CORS preflight
  await testCorsPreflightRequest(`${API_BASE_URL}/api/careers`);
  
  // Test 4: With Origin header (simulating browser request)
  await testEndpoint('Careers with Origin Header', `${API_BASE_URL}/api/careers`, {
    headers: {
      'Origin': FRONTEND_URL,
      'Accept': 'application/json'
    }
  });
  
  // Test 5: Admin endpoint (should be protected)
  await testEndpoint('Admin Careers (should be 401)', `${API_BASE_URL}/api/admin/careers`);
  
  // Test 6: Health check (if available)
  await testEndpoint('Health Check', `${API_BASE_URL}/health`);
  
  console.log('\n🎯 Test Summary:');
  console.log('================');
  console.log('If all tests pass, the API is working correctly.');
  console.log('If tests fail, check:');
  console.log('1. Backend deployment status');
  console.log('2. CORS configuration');
  console.log('3. Environment variables');
  console.log('4. Network connectivity');
  
  console.log('\n📋 Next Steps:');
  console.log('1. Fix any failing tests');
  console.log('2. Update frontend VITE_API_URL if needed');
  console.log('3. Redeploy frontend with correct API URL');
  console.log('4. Test careers page in production');
}

// Run the tests
runTests().catch(console.error);
