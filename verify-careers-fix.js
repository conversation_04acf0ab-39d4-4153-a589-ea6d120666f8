#!/usr/bin/env node

/**
 * Verification Script for MetaNord Careers Fix
 * Monitors the production API until the fixes are deployed
 */

const https = require('https');

async function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data
        });
      });
    });
    req.on('error', reject);
    req.end();
  });
}

async function verifyCareersAPI() {
  console.log('🔍 Verifying MetaNord Careers Fix...\n');

  try {
    const response = await makeRequest('https://api.metanord.eu/api/careers');
    
    if (response.status === 200) {
      const data = JSON.parse(response.data);
      
      console.log(`📊 Found ${data.length} job postings:`);
      data.forEach((job, index) => {
        console.log(`   ${index + 1}. ${job.title} (${job.department || 'No department'})`);
        console.log(`      - Has responsibilities: ${job.responsibilities ? 'Yes' : 'No'}`);
        console.log(`      - Has requirements: ${job.requirements ? 'Yes' : 'No'}`);
        console.log(`      - Published: ${job.published}`);
      });

      // Check if we have the expected 3 jobs
      const expectedJobs = ['Sales Manager', 'Production Engineer', 'Logistics Coordinator'];
      const foundJobs = data.map(job => job.title);
      
      console.log('\n🎯 Expected vs Found:');
      expectedJobs.forEach(expected => {
        const found = foundJobs.some(job => job.includes(expected.split(' ')[0]));
        console.log(`   ${expected}: ${found ? '✅' : '❌'}`);
      });

      if (data.length >= 3) {
        console.log('\n🎉 SUCCESS! Backend fix is deployed!');
        console.log('✅ Careers page should now work in production');
        console.log('✅ Admin careers tab should now work');
        console.log('\n📋 Next steps:');
        console.log('1. Refresh https://metanord-frontend.vercel.app/careers');
        console.log('2. Test admin careers at https://metanord-frontend.vercel.app/admin');
        console.log('3. Verify all 3 job postings are displayed');
      } else {
        console.log('\n⏳ Changes still deploying...');
        console.log('The backend fix is in progress. Please wait a few minutes and try again.');
      }

    } else {
      console.log(`❌ API request failed: ${response.status}`);
    }

  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

// Run verification
verifyCareersAPI();
