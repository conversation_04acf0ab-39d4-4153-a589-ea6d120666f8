#!/usr/bin/env node

/**
 * Test script to verify both critical issues are resolved
 */

const { execSync } = require('child_process');

console.log('🔍 Testing MetaNord Critical Issues Resolution...\n');

// Test 1: Backend API Connectivity
console.log('📡 Testing Backend API Connectivity...');
try {
  const careersResponse = execSync('curl -s http://localhost:3001/api/careers', { encoding: 'utf8' });
  const careers = JSON.parse(careersResponse);
  
  if (Array.isArray(careers) && careers.length > 0) {
    console.log('✅ Backend API is responding correctly');
    console.log(`   Found ${careers.length} job postings`);
    console.log(`   Sample job: ${careers[0].title}`);
  } else {
    console.log('❌ Backend API returned empty or invalid data');
  }
} catch (error) {
  console.log('❌ Backend API is not responding');
  console.log(`   Error: ${error.message}`);
}

// Test 2: Frontend Proxy
console.log('\n🌐 Testing Frontend Proxy...');
try {
  const proxyResponse = execSync('curl -s http://localhost:5174/api/careers', { encoding: 'utf8' });
  const proxyCareers = JSON.parse(proxyResponse);
  
  if (Array.isArray(proxyCareers) && proxyCareers.length > 0) {
    console.log('✅ Frontend proxy is working correctly');
    console.log(`   Proxy returned ${proxyCareers.length} job postings`);
  } else {
    console.log('❌ Frontend proxy returned empty or invalid data');
  }
} catch (error) {
  console.log('❌ Frontend proxy is not working');
  console.log(`   Error: ${error.message}`);
}

// Test 3: Frontend Page Accessibility
console.log('\n📄 Testing Frontend Page Accessibility...');
try {
  const careersPageResponse = execSync('curl -s http://localhost:5174/careers', { encoding: 'utf8' });
  
  if (careersPageResponse.includes('<title>') && careersPageResponse.includes('MetaNord')) {
    console.log('✅ Careers page is accessible');
    console.log('   Page returns valid HTML with MetaNord branding');
  } else {
    console.log('❌ Careers page is not accessible or missing content');
  }
} catch (error) {
  console.log('❌ Careers page is not accessible');
  console.log(`   Error: ${error.message}`);
}

// Test 4: Admin Dashboard Accessibility
console.log('\n🔧 Testing Admin Dashboard Accessibility...');
try {
  const adminPageResponse = execSync('curl -s http://localhost:5174/admin', { encoding: 'utf8' });
  
  if (adminPageResponse.includes('<title>') && adminPageResponse.includes('MetaNord')) {
    console.log('✅ Admin dashboard is accessible');
    console.log('   Page returns valid HTML with MetaNord branding');
  } else {
    console.log('❌ Admin dashboard is not accessible or missing content');
  }
} catch (error) {
  console.log('❌ Admin dashboard is not accessible');
  console.log(`   Error: ${error.message}`);
}

console.log('\n🎯 Test Summary:');
console.log('- Backend API: Check the ✅/❌ status above');
console.log('- Frontend Proxy: Check the ✅/❌ status above');
console.log('- Careers Page: Check the ✅/❌ status above');
console.log('- Admin Dashboard: Check the ✅/❌ status above');

console.log('\n📋 Manual Testing Required:');
console.log('1. Open http://localhost:5174/admin and click through sidebar navigation');
console.log('2. Verify layout remains intact when switching between sections');
console.log('3. Open http://localhost:5174/careers and verify job postings display');
console.log('4. Check browser console for any JavaScript errors');

console.log('\n✨ Testing complete!');
