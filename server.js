const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

const dataPath = path.join(__dirname, 'client', 'src', 'data', 'finalized', 'metanord-products.json');
let products = [];
try {
  products = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
} catch (e) {
  console.error('Failed to load products data', e);
}

app.get('/api/products', (req, res) => {
  const language = req.query.language;
  if (language) {
    return res.json(products.filter(p => !p.language || p.language === language));
  }
  res.json(products);
});

app.get('/api/products/slug/:slug', (req, res) => {
  const slug = req.params.slug;
  const product = products.find(p => String(p.productId) === String(slug));
  if (product) return res.json(product);
  res.status(404).json({ error: 'Product not found' });
});

app.listen(PORT, () => {
  console.log(`API server listening on ${PORT}`);
});
