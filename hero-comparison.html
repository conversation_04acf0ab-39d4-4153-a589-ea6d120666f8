<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero Section Refactor - Before vs After</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(to right, #2D7EB6, #40BFB9);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">Product Detail Hero Section Refactor</h1>
        
        <!-- Before Section -->
        <div class="mb-12">
            <h2 class="text-2xl font-semibold mb-4 text-red-600">❌ BEFORE: Centered Layout</h2>
            <div class="relative gradient-bg text-white pt-24 pb-32 overflow-hidden rounded-lg">
                <div class="container mx-auto px-4 text-center z-10 relative">
                    <button class="inline-flex items-center text-white/80 hover:text-white mb-6 transition-all duration-300">
                        ← Back to Products
                    </button>
                    <h1 class="text-4xl md:text-5xl font-semibold text-white mb-3">
                        Oil and Gas Steel Pipes
                    </h1>
                    <p class="text-lg md:text-xl font-medium text-white max-w-2xl mx-auto">
                        Steel
                    </p>
                </div>
                <!-- Wave SVG -->
                <div class="absolute bottom-0 left-0 w-full">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" class="w-full h-auto fill-white">
                        <path d="M0,96L48,85.3C96,75,192,53,288,53.3C384,53,480,75,576,75C672,75,768,53,864,48C960,43,1056,53,1152,58.7C1248,64,1344,64,1392,64L1440,64L1440,100L1392,100C1344,100,1248,100,1152,100C1056,100,960,100,864,100C768,100,672,100,576,100C480,100,384,100,288,100C192,100,96,100,48,100L0,100Z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded">
                <h3 class="font-semibold text-red-800">Issues with this approach:</h3>
                <ul class="list-disc list-inside text-red-700 mt-2">
                    <li>Excessive vertical space (pt-24 pb-32 = 224px total)</li>
                    <li>Center-aligned content feels disconnected</li>
                    <li>Wastes screen real estate</li>
                    <li>Less modern product page feel</li>
                </ul>
            </div>
        </div>

        <!-- After Section -->
        <div class="mb-12">
            <h2 class="text-2xl font-semibold mb-4 text-green-600">✅ AFTER: Left-aligned Layout</h2>
            <div class="relative gradient-bg text-white py-12 overflow-hidden rounded-lg">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10 relative">
                    <div class="text-left">
                        <button class="inline-flex items-center text-white/80 hover:text-white mb-4 transition-all duration-300">
                            ← Back to Products
                        </button>
                        <h1 class="text-3xl md:text-4xl lg:text-5xl font-semibold text-white mb-3 max-w-4xl">
                            Oil and Gas Steel Pipes
                        </h1>
                        <p class="text-lg md:text-xl font-medium text-white/90 max-w-2xl">
                            Steel
                        </p>
                    </div>
                </div>
                <!-- Wave SVG -->
                <div class="absolute bottom-0 left-0 w-full">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" class="w-full h-auto fill-white">
                        <path d="M0,96L48,85.3C96,75,192,53,288,53.3C384,53,480,75,576,75C672,75,768,53,864,48C960,43,1056,53,1152,58.7C1248,64,1344,64,1392,64L1440,64L1440,100L1392,100C1344,100,1248,100,1152,100C1056,100,960,100,864,100C768,100,672,100,576,100C480,100,384,100,288,100C192,100,96,100,48,100L0,100Z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded">
                <h3 class="font-semibold text-green-800">Improvements achieved:</h3>
                <ul class="list-disc list-inside text-green-700 mt-2">
                    <li>Compact vertical space (py-12 = 96px total) - 57% reduction</li>
                    <li>Left-aligned content flows naturally</li>
                    <li>Efficient use of screen space</li>
                    <li>Modern product documentation style</li>
                    <li>Better content hierarchy</li>
                    <li>Maintained wave background and brand colors</li>
                </ul>
            </div>
        </div>

        <!-- Comparison Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-xl font-semibold mb-4">📊 Metrics Comparison</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span>Vertical Padding:</span>
                        <span class="text-red-600">224px → <span class="text-green-600">96px</span></span>
                    </div>
                    <div class="flex justify-between">
                        <span>Space Reduction:</span>
                        <span class="text-green-600">57%</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Text Alignment:</span>
                        <span class="text-red-600">Center → <span class="text-green-600">Left</span></span>
                    </div>
                    <div class="flex justify-between">
                        <span>Container:</span>
                        <span class="text-green-600">max-w-7xl</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-xl font-semibold mb-4">🎯 Objectives Met</h3>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>Left-aligned content</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>Reduced vertical height</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>Preserved wave background</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>Mobile responsive</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>Brand consistency</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h3 class="text-xl font-semibold mb-4">🔧 Technical Implementation</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-red-600 mb-2">Before (Centered):</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"><code>&lt;section className="... pt-24 pb-32 ..."&gt;
  &lt;div className="container mx-auto px-4 text-center"&gt;
    &lt;h1 className="text-4xl md:text-5xl ..."&gt;
    &lt;p className="... max-w-2xl mx-auto"&gt;</code></pre>
                </div>
                <div>
                    <h4 class="font-semibold text-green-600 mb-2">After (Left-aligned):</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"><code>&lt;section className="... py-12 ..."&gt;
  &lt;div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"&gt;
    &lt;div className="text-left"&gt;
      &lt;h1 className="text-3xl md:text-4xl lg:text-5xl ..."&gt;
      &lt;p className="... max-w-2xl"&gt;</code></pre>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
